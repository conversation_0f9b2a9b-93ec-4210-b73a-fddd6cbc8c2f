package com.yxt.aom.base.bean.im;

import com.yxt.aom.base.entity.common.Activity;
import lombok.Builder;
import lombok.Data;

/**
 * 消息，包含系统群聊，钉钉，飞书，企微
 * 所需要的参数
 */
@Data
@Builder
public class ChatMsgBean {

    /**
     * 群聊ID
     */
    private String imId;
    /**
     * 业务ID
     */
    private String bizId;
    /**
     * 机构id
     */
    private String orgId;
    /**
     * 发送消息类型，0-钉钉 1-飞书 2-企微 3-系统群聊
     */
    private int imType;
    /**
     * 消息标题
     */
    private String title;
    /**
     * 只有系统群传值
     */
    private String pushTitile;
    /**
     * 消息正文
     */
    private String content;
    /**
     * 发送人
     */
    private String creator;
    /**
     * / 文件名称
     */
    private String fileName;
    /**
     * / 文件链接
     */
    private String fileUrl;

    private Integer width;

    private Integer height;
}
