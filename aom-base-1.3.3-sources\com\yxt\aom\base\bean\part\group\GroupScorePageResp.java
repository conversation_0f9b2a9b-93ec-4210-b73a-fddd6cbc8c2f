package com.yxt.aom.base.bean.part.group;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yxt.common.Constants;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@Schema(name = "积分记录详情实体类")
public class GroupScorePageResp {

    private String id;

    @Schema(description = "分数数据状态,0：正常 1撤回状态", example = "0")
    private Integer scoreStatus;

    @Schema(description = "积分", example = "20")
    private Long score;

    @Schema(description = "增加原因", example = "小组进度保持第一名")
    private String reason;

    @Schema(description = "是否分享 默认0不分享 1分享", example = "1")
    private Integer sharedIm = 0;

    @Schema(description = "账号", example = "zhangsan")
    private String userName;

    @Schema(description = "中文名", example = "张三")
    private String fullName;

    @Schema(description = "用户id", example = "dc9de391-e4e9-4704-a881-ba7b74a37e57")
    private String userId;

    @Schema(description = "撤回人id", example = "dc9de391-e4e9-4704-a881-ba7b74a37e57")
    private String withdrawUserId;

    @Schema(description = "撤回人name", example = "xx")
    private String withdrawUserName;

    @Schema(description = "撤回人fullName", example = "yy")
    private String withdrawFullName;

    @Schema(description = "变更时间", example = "2021-10-01 10:10:20")
    @JsonFormat(pattern = Constants.SDF_YEAR2SECOND, timezone = Constants.STR_GMT8)
    private Date createTime;
}
