package com.yxt.aom.base.bean.config;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import java.util.Set;

/**
 * 群聊配置
 */
@Data
@Schema(description = "群聊配置")
public class ImConfig {
    @Schema(description = "项目群聊类型 '0-钉钉,1-飞书,2-企微,3-系统' 默认3")
    @NotNull
    @Range(min = 0, max = 3)
    private Integer imType = 3;

    @Schema(description = "开启项目群聊 0:不开启  1:开启")
    private Integer imEnable = 0;

    @Schema(description = "项目结束后自动解散群聊 0-不解散 1-解散")
    private Integer imOver = 0;

    @Schema(description = "是否开启群聊排除人员设置, 0-不开启 1-开启 默认不开启")
    private Integer excludeUserEnabled = 0;

    @Schema(description = "群聊排除人员id列表")
    private Set<String> excludeUserIds;
}
