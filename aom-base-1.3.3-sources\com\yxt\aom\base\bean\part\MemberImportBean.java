package com.yxt.aom.base.bean.part;

import com.alibaba.excel.annotation.ExcelProperty;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;

import static com.yxt.aom.base.common.AomPropConstants.*;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MemberImportBean {

    public static final List<String> COLUMN = Collections.unmodifiableList(
            Lists.newArrayList(USER_NAME, USER_FULL_NAME, USER_NO, GROUP_NAME, FORMAL, LEADER, ERROR_INFO));


    @ExcelProperty(index = 0)
    private String userName;

    @ExcelProperty(index = 1)
    private String userFullName;

    @ExcelProperty(index = 2)
    private String userNo;

    @ExcelProperty(index = 3)
    private String groupName;

    /**
     * 是否是正式学员（0-旁听学员，1-正式学员）
     */
    @ExcelProperty(index = 4)
    private Integer formal;

    /**
     * 是否组长 （0 & null-普通组员，1-小组组长）
     */
    @ExcelProperty(index = 5)
    private Integer leader;

    private String errorInfo;
    private String subProjectName;
}
