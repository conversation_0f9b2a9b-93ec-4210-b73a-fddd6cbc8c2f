package com.yxt.aom.base.bean.part.group;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/12
 */
@Getter
@Setter
@NoArgsConstructor
public class GroupBeanRes {

    @Schema(description = "小组id")
    @JsonSerialize(using = ToStringSerializer.class)
    private long id;

    @Schema(description = "小组name")
    private String name;

    @Schema(description = "小组人数")
    private Integer totalCnt = 0;

    @Schema(description = "辅导员")
    private List<InstructorBean> tutors;

    @Schema(description = "组长名字")
    private String leader;

    @Schema(description = "组长ID")
    private String leaderId;

    @Schema(description = "小组项目完成进度=已完成学员数/小组学员数")
    private String groupProgress = "0";

    @Schema(description = "全部任务完成率")
    private String allProgress = "0";

    @Schema(description = "必修任务完成率")
    private String requiredProgress = "0";

}
