package com.yxt.aom.base.bean.setup;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
public class AomActivityFixTimeExt implements Serializable {

    private static final long serialVersionUID = 5063718445138092251L;

    @Schema(description = "固定时间点e:2021-12-08 12:00")
    private List<String> fixTimes;
}
