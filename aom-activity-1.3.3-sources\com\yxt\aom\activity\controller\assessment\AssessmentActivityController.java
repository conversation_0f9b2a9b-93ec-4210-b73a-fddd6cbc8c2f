package com.yxt.aom.activity.controller.assessment;


import com.yxt.aom.activity.bean.RepeatReq;
import com.yxt.aom.activity.bean.SubTaskRepeatResp;
import com.yxt.aom.activity.bean.TaskHandCompleteResp;
import com.yxt.aom.activity.service.trace.ActivityHandCompleteService;
import com.yxt.aom.activity.service.trace.ActivityRepeatService;
import com.yxt.common.annotation.Auth;
import com.yxt.common.annotation.timezone.ConvertDateParams;
import com.yxt.common.annotation.timezone.DateFormatParam;
import com.yxt.common.enums.AuthType;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.service.AuthService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

/**
 * 评鉴活动
 */
@Tag(name = "评鉴活动")
@RestController("/assessment/actv")
@RequiredArgsConstructor
public class AssessmentActivityController {

    private final AuthService authService;

    private final ActivityRepeatService assessmentActivityRepeat;
    private final ActivityHandCompleteService activityHandCompleteService;

    @Operation(summary = "测评任务指派重学")
    @PutMapping(value = "/repeat")
    @ResponseStatus(HttpStatus.OK)
    //@Auth(type = {AuthType.TOKEN})
    @ConvertDateParams
    public SubTaskRepeatResp repeatLearn(@Validated @RequestBody @DateFormatParam RepeatReq bean) {
        //UserCacheDetail userCacheDetail = authService.getUserCacheDetail();
        return assessmentActivityRepeat.assessmentRepeat(bean.getOrgId(), bean);

    }


    @Operation(summary = "活动任务标记完成")
    @PutMapping(value = "/handComplete")
    @ResponseStatus(HttpStatus.OK)
    @Auth(type = {AuthType.TOKEN})
    @ConvertDateParams
    public void handComplete(@Validated @RequestBody @DateFormatParam TaskHandCompleteResp taskHandCompleteResp) {
        UserCacheDetail userCacheDetail = authService.getUserCacheDetail();
        activityHandCompleteService.handComplete(userCacheDetail, taskHandCompleteResp);
    }
}
