package com.yxt.aom.activity.mq;

import com.alibaba.fastjson.JSON;
import com.yxt.aom.activity.handle.AomStatusChange4ActvFunctionMap;
import com.yxt.aom.base.bean.part.ActivityStatusChange4ProMsg;
import com.yxt.aom.base.common.AomConstants;
import com.yxt.aom.common.config.AomDataSourceTypeHolder;
import com.yxt.aom.common.config.AomDbProperties;
import com.yxt.dbrouter.core.toolkit.DBRouteHolder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.function.Consumer;

import static com.yxt.aom.base.common.AomMqConstants.TOPIC_AOM_ACTIVITY_STATUS_CHANGED_FOR_ACTV;

/**
 * 项目状态变更
 */
@Slf4j
@RequiredArgsConstructor
@Component
@ConditionalOnProperty(name = "aom.rocketmq.consumer-enabled", havingValue = "true", matchIfMissing = true)
@RocketMQMessageListener(consumerGroup = AomConstants.MQ_GROUP_PREFIX
        + TOPIC_AOM_ACTIVITY_STATUS_CHANGED_FOR_ACTV, topic = TOPIC_AOM_ACTIVITY_STATUS_CHANGED_FOR_ACTV, consumeMode = ConsumeMode.CONCURRENTLY, consumeThreadNumber = 2)
public class AomActivityStatusChange4ActvListener implements RocketMQListener<String> {

    private final AomDbProperties aomDbProperties;
    private final AomStatusChange4ActvFunctionMap functionMap;

    @Override
    public void onMessage(String message) {
        log.info("ACTV AomActivityStatusChange4ProjectListener, message: {}", message);
        try {

            if (StringUtils.isEmpty(message)) {
                return;
            }
            ActivityStatusChange4ProMsg msgBean = JSON.parseObject(message, ActivityStatusChange4ProMsg.class);
            if (msgBean == null) {
                log.warn("ACTV AomActivityStatusChange4ProjectListener invalid message: {}", message);
                return;
            }
            String regId = msgBean.getRegId();
            Map<String, String> dsmap = aomDbProperties.getDsmap();
            log.info("ACTV AomActivityStatusChange4ProjectListener dsmap:{}", dsmap);
            if (!dsmap.containsKey(regId)) {
                return;
            }
            AomDataSourceTypeHolder.set(regId);
            String orgId = msgBean.getOrgId();
            DBRouteHolder.push(orgId);
            Integer newStatus = msgBean.getNewStatus();
            Map<Integer, Consumer<ActivityStatusChange4ProMsg>> map = functionMap.map;
            Consumer<ActivityStatusChange4ProMsg> func = map.get(newStatus);
            if (func != null) {
                func.accept(msgBean);
            }
            
        } catch (Exception ex) {
            log.error("ACTV AomActivityTimeCycleChangeListener message failed. message: {} , err: ", message, ex);
        }
    }
}
