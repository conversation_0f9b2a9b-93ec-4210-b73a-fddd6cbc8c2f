package com.yxt.aom.base.bean.part;

import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

@Getter
@Setter
public class ActivityStatusChangeThird {

    private String userId;

    private String orgId;

    /**
     * 考试ids/课程ids.....
     */
    private List<String> refIds;

    private String sourceId;

    private Integer oldStatus;

    /**
     * 用户登陆的source, 例如501,502等
     */
    private String source;

    /**
     * 业务定制参数
     */
    private Map<String, Object> params;

}
