package com.yxt.aom.base.component.dlc;

import com.alibaba.excel.EasyExcelFactory;
import com.yxt.aom.base.custom.interceptor.DlcActionInterceptor;
import com.yxt.aom.base.wrapper.FileWrapper;
import com.yxt.common.exception.ApiException;
import com.yxt.common.util.Validate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static com.yxt.aom.base.common.BaseErrorConsts.EXCEL_FILE_NOT_FOUND;
import static com.yxt.aom.base.common.BaseErrorConsts.EXCEL_FILE_PARSE_FAILED;

/**
 * <AUTHOR>
 * @since 2024/11/12
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class AomDlcComponent {

    private final FileWrapper fileWrapper;
    private final Optional<DlcActionInterceptor> dlcActionInterceptor;

    /**
     * Read excel by file id exclude line null list.
     *
     * @param <T>           the type parameter
     * @param fileId        the file id
     * @param headRowNumber the head row number
     * @param headClazz     the head clazz
     * @return the list
     */
    public <T> List<T> readExcelByFileIdExcludeLineNull(String fileId, int headRowNumber, Class<T> headClazz) {
        Validate.isNotBlank(fileId, EXCEL_FILE_NOT_FOUND);

        // 插槽，接入方可以自己解析文件
        if (dlcActionInterceptor.isPresent()) {
            return dlcActionInterceptor.get().readExcel(fileId, headRowNumber, headClazz);
        }

        String fileUrl = fileWrapper.getDownloadUrl(fileId);
        Validate.isNotBlank(fileUrl, EXCEL_FILE_NOT_FOUND);
        AomReadExcludeLineNullListener<T> listener = new AomReadExcludeLineNullListener<>();
        try (InputStream is = fileAsStream(fileUrl)) {
            EasyExcelFactory.read(is, listener).head(headClazz).headRowNumber(headRowNumber).autoTrim(Boolean.TRUE).sheet()
                    .doRead();
        } catch (Exception ex) {
            log.error("解析文件报错 fileId={}", fileId, ex);
            throw new ApiException(EXCEL_FILE_PARSE_FAILED);
        }
        final List<T> data = listener.getList();

        return data == null ? Collections.emptyList() : data;
    }

    private InputStream fileAsStream(String fileUrl) throws IOException {
        final URL url = new URL(fileUrl);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.connect();
        return connection.getInputStream();
    }

}


