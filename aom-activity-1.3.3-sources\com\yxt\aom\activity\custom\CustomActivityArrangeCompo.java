package com.yxt.aom.activity.custom;

import com.yxt.aom.activity.entity.arrange.ActivityDraft;
import com.yxt.aom.activity.facade.bean.arrange.ActivityDemoCopyReq;
import com.yxt.aom.activity.facade.bean.arrange.ActivityDemoCopyRsp;
import com.yxt.aom.activity.facade.bean.arrange.ActivityDraft4BatchCheck;
import com.yxt.aom.activity.facade.bean.arrange.ActivityDraft4BatchCopy;
import com.yxt.aom.activity.facade.bean.arrange.Response4BatchCheck;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 编排相关定制接口
 */
public interface CustomActivityArrangeCompo {
    /**
     * 批量预检活动草稿
     *
     * @param bean 预检请求体
     * @return 预检结果
     */
    Response4BatchCheck batchCheckDraft(ActivityDraft4BatchCheck bean);

    /**
     * 批量保存活动（草稿转正式）
     * since 6.1, 一个itemId下可能会有多个活动, map key改为草稿id
     *
     * @param orgId              机构id
     * @param actvRegId          活动/项目注册id
     * @param actvId             活动/项目id
     * @param operatorId         操作者id
     * @param activities2Create  待创建的活动
     * @param activities2Update  待更新的活动
     * @param activityIds2Remove 待删除的活动
     * @return 新增活动的结果map(草稿id, value为叶节点引用对象的id)
     */
    Map<Long, String> batchSaveActivity(String orgId, String actvRegId, String actvId, String operatorId,
            List<ActivityDraft> activities2Create, List<ActivityDraft> activities2Update,
            Set<String> activityIds2Remove);

    /**
     * 复制活动时由活动方更新老的活动草稿表单数据(ActivityDraft.formData)中相关id字段
     *
     * @param req           ActivityDraft4BatchCopy
     * @param fromDraftList List
     */
    default void updateDraftIdFields4Copy(ActivityDraft4BatchCopy req, List<ActivityDraft> fromDraftList) {
        // do nothing
    }

    /**
     * demo复制活动
     *
     * @param bean the bean
     * @return the activity demo copy rsp
     */
    default ActivityDemoCopyRsp demoCopyActivity(ActivityDemoCopyReq bean) {
        return null;
    }

    /**
     * 批量更新活动formData(需要重新组装formData的情况自己实现)
     *
     * @param orgId      机构id
     * @param actvId     活动/项目id
     * @param operatorId 操作者id
     * @param map key为refId, value为旧formData
     * @return 需要更新的结果map(key为业务方活动id, value为新formData)
     */
    default Map<String, String> batchUpdateFormData(String orgId, String actvId, String operatorId) {
        return Collections.emptyMap();
    }

    /**
     * 列出项目中已使用的refId（草稿表单中使用的也算）
     *
     * @param orgId  String
     * @param actvId String
     * @param refIds 要查找使用情况的refId
     * @param drafts 草稿表单列表
     * @return 已使用的refId
     */
    default Set<String> listUsedRefId(String orgId, String actvId, Set<String> refIds, List<ActivityDraft> drafts) {
        return Collections.emptySet();
    }

    /**
     * 批量更新活动formData(需要重新组装formData的情况自己实现)
     *
     * @param orgId      机构id
     * @param actvId     活动/项目id
     * @param operatorId 操作者id
     * @param map key为refId, value为旧formData
     * @param actvRegId actvRegId
     * @return 需要更新的结果map(key为业务方活动id, value为新formData)
     */
    Map<String, String> batchUpdateFormData(String orgId, String actvId, String operatorId,Map<String,String> map,String actvRegId);
}
