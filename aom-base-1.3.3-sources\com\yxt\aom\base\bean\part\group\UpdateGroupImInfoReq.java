package com.yxt.aom.base.bean.part.group;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Schema(name = "修改群聊基本信息请求体")
@Getter
@Setter
@ToString
public class UpdateGroupImInfoReq {

    @Schema(description = "活动id")
    @NotNull
    private String actvId;

    @Schema(description = "UACD注册表中定义Id")
    @NotNull
    private String regId;

    @Schema(description = "小组id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull
    private Long groupId;

    @Schema(description = "开启或关闭，0关闭，1开启", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer openOrClose;

    @Schema(description = "名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String imName;

    @Schema(description = "群主id", requiredMode = Schema.RequiredMode.REQUIRED)
    private String ownerUserId;

}
