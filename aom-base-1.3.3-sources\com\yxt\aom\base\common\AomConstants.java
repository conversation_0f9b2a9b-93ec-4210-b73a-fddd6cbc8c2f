package com.yxt.aom.base.common;

import com.google.common.collect.Lists;

import java.util.Collections;
import java.util.List;

/**
 * The type Aom constants.
 */
public final class AomConstants {
    private AomConstants() {
        //Hide Utility Class Constructor
    }

    /**
     * The constant APP_CODE.
     */
    public static final String APP_CODE = "aom";

    /**
     * ID格式为UUID的活动注册id列表
     */
    public static final List<String> REGIDS_UUID = Collections.unmodifiableList(Lists.newArrayList("proj_rcpd"));

    /**
     * The constant ONE.
     */
    public static final String ONE = "1";

    /**
     * The constant AOM_TRANSACTION_MANAGER.
     */
    public static final String AOM_TRANSACTION_MANAGER = "aomTransactionManager";

    /**
     * The constant REGID_PREFIX_PROJECT.
     */
    public static final String REGID_PREFIX_PROJECT = "proj_";

    /**
     * The constant REGID_PREFIX_ACTIVITY.
     */
    public static final String REGID_PREFIX_ACTIVITY = "actv_";

    /**
     * he constant AUDIT_FORM_TYPE.
     */
    public static final int AUDIT_FORM_TYPE = 38;

    /**
     * The constant LIMIT_1.
     */
    public static final String LIMIT_1 = "limit 1";

    /**
     * The constant MQ_GROUP_PREFIX.
     */
    public static final String MQ_GROUP_PREFIX = "${spring.application.name}-group-";

    /**
     * 要素code-培训模板库
     */
    public static final String CODE_TEMPLATELIST = "train_template";

    /**
     * 要素code-培训计划
     */
    public static final String CODE_TRAIN_PLAN = "train_plan";

    /**
     * 要素code-皮肤
     */
    public static final String CODE_SKIN = "skin";
}
