package com.yxt.aom.base.bean.part;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/11/14
 */
@Getter
@Setter
@NoArgsConstructor
@SuperBuilder
public class PartMemberFormalChangeBean {

    private String orgId;

    @Schema(description = "活动/项目id")
    private String actvId;

    /**
     * 注册表id,方便找到对应类型
     */
    @Schema(description = "活动/项目注册id")
    private String regId;

    /**
     * 具体活动ids
     */
    private List<String> refIds;

    @Schema(description = "userIds")
    private List<String> userIds;

    @Schema(description = "学员类型 0-旁听学员 1-正式学员")
    private Integer formal;

    @Schema(description = "参与id")
    private Long participationId;

    /**
     * 操作人id
     */
    private String optUserId;

    /**
     * 操作人姓名
     */
    private String fullName = StringUtils.EMPTY;

    @Schema(description = "生效时间")
    private Date effectTime;

    private Date currentTime;

    @Schema(description = "类型(1-活动, 2-项目)")
    private Integer actvType;

    /**
     * 是否推送消息配置 true-推送消息 false-不推送消息
     */
    private Boolean sendMsg = Boolean.FALSE;

}
