package com.yxt.aom.base.bean.arrange;

import java.io.Serial;

/**
 * ActvCheckException
 */
public class ActvCheckException extends RuntimeException {
    @Serial
    private static final long serialVersionUID = 1080985736650805737L;

    private final int code;
    private final String message;
    private final boolean finished;

    /**
     * Constructor
     *
     * @param code     int
     * @param message  String
     * @param finished boolean
     */
    public ActvCheckException(int code, String message, boolean finished) {
        this.code = code;
        this.message = message;
        this.finished = finished;
    }

    /**
     * Constructor
     *
     * @param code    int
     * @param message String
     */
    public ActvCheckException(int code, String message) {
        this.code = code;
        this.message = message;
        this.finished = false;
    }

    /**
     * Gets code.
     *
     * @return the code
     */
    public int getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }

    /**
     * Is finished boolean.
     *
     * @return the boolean
     */
    public boolean isFinished() {
        return finished;
    }
}
