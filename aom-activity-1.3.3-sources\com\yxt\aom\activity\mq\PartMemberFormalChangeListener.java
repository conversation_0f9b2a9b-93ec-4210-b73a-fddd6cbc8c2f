package com.yxt.aom.activity.mq;

import com.alibaba.fastjson2.JSON;
import com.yxt.aom.base.bean.part.PartUserChangeBean;
import com.yxt.aom.base.common.AomConstants;
import com.yxt.aom.base.service.part.impl.ActivityParticipationMemberService;
import com.yxt.aom.common.config.AomDbProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

import static com.yxt.aom.base.common.AomMqConstants.TOPIC_SC_MEMBER_FORMAL_CHANGE;
import static com.yxt.common.Constants.INT_1;
import static com.yxt.common.Constants.INT_2;

/**
 * 参与人员类型变更mq消费者
 *
 * <AUTHOR>
 * @since 2024/11/1
 */
@Slf4j
@RequiredArgsConstructor
@Component
@ConditionalOnProperty(name = "aom.rocketmq.consumer-enabled", havingValue = "true", matchIfMissing = true)
@RocketMQMessageListener(consumerGroup = AomConstants.MQ_GROUP_PREFIX
        + TOPIC_SC_MEMBER_FORMAL_CHANGE, topic = TOPIC_SC_MEMBER_FORMAL_CHANGE,
        consumeMode = ConsumeMode.CONCURRENTLY, consumeThreadNumber = INT_2)
public class PartMemberFormalChangeListener implements RocketMQListener<String> {
    private final ActivityParticipationMemberService activityParticipationMemberService;
    private final AomDbProperties aomDbProperties;

    @Override
    public void onMessage(String message) {
        log.info("partMemberFormalChangeListener received msg={}", message);
        try {
            PartUserChangeBean changeBean = JSON.parseObject(message, PartUserChangeBean.class);
            if (Objects.isNull(changeBean)) {
                return;
            }
            String regId = changeBean.getRegId();
            Map<String, String> dsmap = aomDbProperties.getDsmap();
            if (!dsmap.containsKey(regId)) {
                log.info("人员类型变更未找到对应的regId信息 msg={}", message);
                return;
            }
            changeBean.setSendMsg(Boolean.FALSE);
            changeBean.setActvType(INT_1);
            activityParticipationMemberService.doChangeUserFormal(changeBean);
        } catch (Exception e) {
            log.error("partMemberFormalChangeListenerErr msg={}", message, e);
        }
    }
}
