package com.yxt.aom.base.bean.part.group;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @createDate 2024/12/12 10:45:08
 */
@Data
public class GroupMemberRemove4Req {

    @Schema(description = "UACD注册表中定义Id")
    private String regId;

    @Schema(description = "项目id")
    private String actvId;

    @Schema(description = "参与id")
    private Long participationId;

    @Schema(description = "小组id，不分组就传0")
    @NotNull(message = "apis.aom.part.groupId.invalid")
    private Long groupId;

    @Schema(description = "学员ID")
    private List<String> userIds;
}
