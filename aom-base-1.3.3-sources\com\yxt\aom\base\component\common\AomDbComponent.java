package com.yxt.aom.base.component.common;

import com.google.common.collect.Sets;
import com.yxt.aom.base.enums.ActivityTypeEnum;
import com.yxt.aom.base.util.AomUtils;
import com.yxt.aom.common.config.AomDataSourceTypeHolder;
import com.yxt.aom.common.config.AomDbProperties;
import com.yxt.dbrouter.job.JobExecutor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Set;
import java.util.function.Consumer;

/**
 * AomDbComponent
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class AomDbComponent {
    private static final String STR_AT = "@";
    private final AomDbProperties aomDbProperties;

    /**
     * 遍历所有数据源并执行操作
     *
     * @param activityTypeEnum 需要遍历的数据库类型（传null遍历AomDbProperties中定义的所有数据库）
     * @param consumer         需要执行的操作（入参为UACD注册id）
     */
    public void invokeByAllDataSource(ActivityTypeEnum activityTypeEnum, Consumer<String> consumer) {
        invokeByAllDataSource(activityTypeEnum, false, consumer);
    }

    /**
     * 遍历所有数据源并执行操作
     *
     * @param activityTypeEnum 需要遍历的数据库类型（传null遍历AomDbProperties中定义的所有数据库）
     * @param allDbRouter      是否需要遍历所有分库（如果入参中有orgId就已经明确了用哪个分库，可以传false；反之则需要传true）
     * @param consumer         需要执行的操作（入参为UACD注册id）
     */
    public void invokeByAllDataSource(ActivityTypeEnum activityTypeEnum, boolean allDbRouter,
            Consumer<String> consumer) {
        Set<String> finishedDatasourceSet = Sets.newHashSet();
        aomDbProperties.getDsmap().forEach((regId, dsBeanName) -> {
            String key = getUniqueKey(regId, dsBeanName);
            if (!AomUtils.isPrefixMatch(regId, activityTypeEnum) || finishedDatasourceSet.contains(key)) {
                return;
            }
            if (allDbRouter && aomDbProperties.getDbrouters().contains(regId)) {
                try {
                    JobExecutor.build().execute(dbRouteKey -> {
                        log.info("before invoke regId - {}, dbRouteKey - {}", regId, dbRouteKey);
                        AomDataSourceTypeHolder.set(regId);
                        consumer.accept(regId);
                        log.info("after invoke regId - {}, dbRouteKey - {}", regId, dbRouteKey);
                    });
                } catch (Exception e) {
                    log.error("JobExecutor.build().execute error", e);
                }
            } else {
                try {
                    log.info("before invoke regId - {}", regId);
                    AomDataSourceTypeHolder.set(regId);
                    consumer.accept(regId);
                    log.info("after invoke regId - {}", regId);
                } catch (Exception e) {
                    log.error("consumer.accept() error", e);
                }
            }
            finishedDatasourceSet.add(key);
        });
    }

    private String getUniqueKey(String regId, String dsBeanName) {
        return dsBeanName + STR_AT + aomDbProperties.getTblprefixmap().get(regId);
    }
}
