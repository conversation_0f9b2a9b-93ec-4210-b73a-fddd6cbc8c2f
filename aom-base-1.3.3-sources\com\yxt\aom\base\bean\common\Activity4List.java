package com.yxt.aom.base.bean.common;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.yxt.common.annotation.timezone.DateFormatField;
import com.yxt.common.pojo.IdName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * Activity4List
 */
@Data
public class Activity4List {
    /**
     * 主键id
     */
    @Schema(description = "活动/项目id", example = "1849276367002220000")
    private String id;

    /**
     * 机构id
     */
    @JsonIgnore
    private String orgId;

    /**
     * 活动/项目的具体类型(UACD注册表中定义)
     */
    @JsonIgnore
    private String actvRegId;

    /**
     * 活动/项目名称
     */
    @Schema(description = "活动/项目名称", example = "项目名称test")
    private String actvName;

    /**
     * 类型(1-活动, 2-项目)
     */
    @Schema(description = "类型(1-活动, 2-项目)", example = "2")
    private Integer actvType;

    /**
     * 状态(0-未保存, 1-未发布, 2-进行中, 3-已结束, 4-归档中, 41-已归档, 42-归档超过一年, 5-已删除, 6-已暂停, 7-已撤回)
     */
    @Schema(description = "状态(0-未保存, 1-未发布, 2-进行中, 3-已结束, 4-归档中, 41-已归档, 42-归档超过一年, 5-已删除, 6-已暂停, 7-已撤回)", example = "2")
    private Integer actvStatus;

    /**
     * 时间模式(0-固定, 1-相对)
     */
    @Schema(description = "时间模式(0-固定, 1-相对)", example = "0")
    private Integer timeModel;

    /**
     * 固定开始时间
     */
    @Schema(description = "固定开始时间", example = "2024-10-25 10:56:47")
    @DateFormatField(isDate = true)
    private Date startTime;

    /**
     * 固定截止时间
     */
    @Schema(description = "固定截止时间", example = "2024-11-25 10:56:47")
    @DateFormatField(isDate = true)
    private Date endTime;

    /**
     * 相对开始天数
     */
    @Schema(description = "相对开始天数", example = "0")
    private Integer startDayOffset;

    /**
     * 相对截止天数
     */
    @Schema(description = "相对截止天数", example = "7")
    private Integer endDayOffset;

    /**
     * 封面
     */
    @Schema(description = "封面", example = "https://stc.yxt.com/ufd/55a3e0/o2o/pc/other/project.png")
    private String imageUrl;

    /**
     * 简介
     */
    @Schema(description = "简介", example = "简介test")
    private String description;

    /**
     * UACD设计器id
     */
    @Schema(description = "UACD设计器id", example = "1849276367002220000")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long designerId;

    /**
     * 来源id
     */
    @JsonIgnore
    private String sourceId;

    /**
     * 来源名称
     */
    @JsonIgnore
    private String sourceName;

    /**
     * 来源的具体类型
     */
    @JsonIgnore
    private String sourceRegId;

    /**
     * 模型id
     */
    @Schema(description = "模型id", example = "ac0a267c-044e-4a28-b489-6be16e0ab781")
    private String modelId;

    /**
     * 场景id
     */
    @Schema(description = "场景id", example = "ac0a267c-044e-4a28-b489-6be16e0ab783")
    private String sceneId;

    /**
     * 分类id
     */
    @Schema(description = "分类id", example = "1849276367002220000")
    private String categoryId;

    /**
     * 是否自动结束(0-否, 1-是; 默认为0)
     */
    @Schema(description = "是否自动结束(0-否, 1-是; 默认为0)", example = "0")
    private Integer autoEnd;

    /**
     * 是否自动归档(0-否, 1-是; 默认为0)
     */
    @Schema(description = "是否自动归档(0-否, 1-是; 默认为0)", example = "0")
    private Integer autoArchive;

    /**
     * 是否开启审核(0-未开启, 1-开启; 默认为0)
     */
    @Schema(description = "是否开启审核(0-未开启, 1-开启; 默认为0)", example = "0")
    private Integer auditEnabled;

    /**
     * 审核状态(0-待审核, 1-审核中, 2-已通过, 3-未通过, 4-已撤回; 默认为0)
     */
    @Schema(description = "审核状态(0-待审核, 1-审核中, 2-已通过, 3-未通过, 4-已撤回; 默认为0)", example = "0")
    private Integer auditStatus;

    /**
     * 创建人id
     */
    @Schema(description = "创建人id", example = "00f5760d-f921-424f-adaa-6a94b812baab")
    private String createUserId;

    /**
     * 更新人id
     */
    @Schema(description = "更新人id", example = "00f5760d-f921-424f-adaa-6a94b812baab")
    private String updateUserId;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2024-11-25 10:56:47")
    @DateFormatField(isDate = true)
    private Date createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间", example = "2024-11-25 10:56:47")
    @DateFormatField(isDate = true)
    private Date updateTime;

    /**
     * 是否删除(0-未删除, 1-已删除)
     */
    @JsonIgnore
    private Integer deleted;

    /**
     * 是否重要(0-不是，1-是)
     */
    @Schema(description = "是否重要(0-不是，1-是)", example = "0")
    private Integer veryImportant;

    /**
     * 项目/活动创建时使用的模板id
     */
    @JsonIgnore
    private String templateId;

    /**
     * 用途类别(0-普通项目/活动, 1-项目/活动模板, 2-集团化项目/活动, 3-移动端项目/活动; 默认为0; 3目前已停用)
     */
    @Schema(description = "用途类别(0-普通项目/活动, 1-项目/活动模板, 2-集团化项目/活动, 3-移动端项目/活动; 默认为0; 3目前已停用)", example = "0")
    private Integer usageType;

    /**
     * 来源类型
     */
    @Schema(description = "来源类型", example = "0")
    private Integer sourceType;

    /**
     * 计划id
     */
    @Schema(description = "计划id", example = "1849276367002225555")
    private String planId;

    /**
     * 活动/项目编号
     */
    @Schema(description = "活动/项目编号", example = "YXT001")
    private String actvCode;

    /**
     * 是否公开活动(0-不公开, 1-公开)
     */
    @Schema(description = "是否公开活动(0-不公开, 1-公开)", example = "0")
    private Integer publicActv;

    /**
     * 是否同步在线课堂中的课程学习进度(0-不同步, 1-同步)
     */
    @Schema(description = "是否同步在线课堂中的课程学习进度(0-不同步, 1-同步)", example = "1")
    private Integer progressSync;

    /**
     * 负责人列表
     */
    @Schema(description = "负责人列表")
    private List<IdName> mgrs;

    /**
     * 分类名称
     */
    @Schema(description = "分类名称(需要业务方在插槽中设值)", example = "分类一")
    private String categoryName;
}
