package com.yxt.aom.base.bean.part.group;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 小组学员进度统计信息
 *
 * <AUTHOR>
 * @since 2024/12/12
 */
@Getter
@Setter
@NoArgsConstructor
public class GroupMemberStatisticsBean {

    private Long groupId;

    /**
     * 必修任务数
     */
    private Integer requiredTaskCnt;

    /**
     * 必修任务完成数
     */
    private Integer requiredFinishedTaskCnt;

    /**
     * 全部任务数
     */
    private Integer allTaskCnt;

    /**
     * 全部任务完成数
     */
    private Integer allTaskFinishedCnt;

    /**
     * 活动完成状态：0-未完成，1-进行中，2-已完成
     */
    private Integer actvCompletedStatus;

}
