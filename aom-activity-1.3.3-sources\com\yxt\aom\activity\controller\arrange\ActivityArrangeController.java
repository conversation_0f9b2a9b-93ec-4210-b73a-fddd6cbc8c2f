package com.yxt.aom.activity.controller.arrange;

import com.google.common.collect.Lists;
import com.yxt.aom.activity.custom.CustomActivityArrangeCompo;
import com.yxt.aom.activity.entity.arrange.ActivityDraft;
import com.yxt.aom.activity.facade.bean.arrange.Activity4BatchSave;
import com.yxt.aom.activity.facade.bean.arrange.ActivityDemoCopyReq;
import com.yxt.aom.activity.facade.bean.arrange.ActivityDemoCopyRsp;
import com.yxt.aom.activity.facade.bean.arrange.ActivityDraft4BatchCheck;
import com.yxt.aom.activity.facade.bean.arrange.ActivityDraft4BatchCopy;
import com.yxt.aom.activity.facade.bean.arrange.ActivityDraft4BatchSave;
import com.yxt.aom.activity.facade.bean.arrange.ActivityDraft4Get;
import com.yxt.aom.activity.facade.bean.arrange.DraftName4Update;
import com.yxt.aom.activity.facade.bean.arrange.RefIdCheckReq;
import com.yxt.aom.activity.facade.bean.arrange.RefIdMap;
import com.yxt.aom.activity.facade.bean.arrange.Response4BatchCheck;
import com.yxt.aom.activity.facade.client.ActivityArrangeClient;
import com.yxt.aom.activity.service.arrange.ActivityArrangeService;
import com.yxt.aom.common.util.AomBeanNameUtils;
import com.yxt.common.annotation.Auth;
import com.yxt.common.annotation.timezone.ConvertDateReturn;
import com.yxt.common.enums.AuthType;
import com.yxt.common.pojo.api.CommonList;
import com.yxt.common.pojo.api.GenericCommonData;
import com.yxt.common.util.BeanCopierUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * ArrangeController
 */
@Tag(name = "服务端-编排")
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("")
public class ActivityArrangeController implements ActivityArrangeClient {
    private final ActivityArrangeService arrangeService;

    /**
     * 检查指定活动是否实现AOM接口
     *
     * @param actvRegId the actv reg id
     * @return the generic common data
     */
    @Override
    @Operation(summary = "检查指定活动是否实现AOM接口")
    @GetMapping(value = "/aom/external/registers/{actvRegId}/implemented")
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    @Auth(type = AuthType.AKSK)
    public GenericCommonData<Boolean> checkImplememt(@PathVariable String actvRegId) {
        CustomActivityArrangeCompo compo = AomBeanNameUtils.getCustomBean(CustomActivityArrangeCompo.class, actvRegId);
        return new GenericCommonData<>(compo != null);
    }

    /**
     * 获取活动表单信息
     *
     * @param actvId   the actv id
     * @param itemId   the item id
     * @param refRegId the ref reg id
     * @param refId    the ref id
     * @param orgId    the org id
     * @return ActivityDraft4Get
     */
    @Override
    @Operation(summary = "获取活动表单信息")
    @GetMapping(value = "/aom/external/activities/{actvId}/draftitems/{itemId}")
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    @Auth(type = AuthType.AKSK)
    @ConvertDateReturn
    public ActivityDraft4Get getActivityDraft(@PathVariable String actvId, @PathVariable Long itemId,
            @RequestParam String refRegId, @RequestParam(required = false) String refId, @RequestParam String orgId) {
        ActivityDraft draft = arrangeService.getActivityDraft(orgId, actvId, itemId, refRegId, refId);
        ActivityDraft4Get result = new ActivityDraft4Get();
        BeanCopierUtil.copy(draft, result, false);
        return result;
    }

    /**
     * 批量预检活动草稿
     *
     * @param bean the bean
     * @return the response 4 batch check
     */
    @Override
    @Operation(summary = "批量预检活动草稿")
    @PostMapping(value = "/aom/external/activities/checkdrafts", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    @Auth(type = AuthType.AKSK)
    public Response4BatchCheck batchCheckDraft(@Valid @RequestBody ActivityDraft4BatchCheck bean) {
        return arrangeService.batchCheckDraft(bean);
    }

    /**
     * 列出项目中已使用的refId（草稿表单中使用的也算）
     *
     * @param bean the bean
     * @return the common list
     */
    @Override
    @Operation(summary = "列出项目中已使用的refId（草稿表单中使用的也算）")
    @PostMapping(value = "/aom/external/activities/listusedrefid", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    @Auth(type = AuthType.AKSK)
    public CommonList<String> listUsedRefId(RefIdCheckReq bean) {
        Set<String> refIds = arrangeService.listUsedRefId(bean);
        List<String> datas = CollectionUtils.isNotEmpty(refIds) ? Lists.newArrayList(refIds) : Collections.emptyList();
        return new CommonList<>(datas);
    }

    /**
     * 批量保存活动草稿
     *
     * @param bean the bean
     * @return 结果map，key为UACD叶节点id，value为草稿id
     */
    @Override
    @ApiResponse(description = "结果map，key为UACD叶节点id，value为草稿id")
    @Operation(summary = "批量保存活动草稿")
    @PostMapping(value = "/aom/external/activities/savedrafts", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    @Auth(type = AuthType.AKSK)
    public Map<Long, String> batchSaveDraft(@Valid @RequestBody ActivityDraft4BatchSave bean) {
        return arrangeService.batchSaveDraft(bean);
    }

    /**
     * 批量复制活动草稿
     *
     * @param bean the bean
     * @return List
     */
    @Override
    @ApiResponse(description = "结果map，key为from的UACD叶节点id，value为to的草稿id")
    @Operation(summary = "批量复制活动草稿")
    @PostMapping(value = "/aom/external/activities/copydrafts", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    @Auth(type = AuthType.AKSK)
    public List<RefIdMap> batchCopyDraft(@Valid @RequestBody ActivityDraft4BatchCopy bean) {
        return arrangeService.batchCopyDraft(bean);
    }

    /**
     * 批量保存活动（草稿转正式）
     * since 6.1, 一个itemId下可能会有多个活动, map key改为草稿id
     *
     * @param bean the bean
     * @return 结果map(草稿id ， value为叶节点引用对象的id)
     */
    @Override
    @ApiResponse(description = "结果map(key为UACD叶节点id，value为叶节点引用对象的id)")
    @Operation(summary = "批量保存活动（草稿转正式）")
    @PostMapping(value = "/aom/external/activities/saveactivities", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    @Auth(type = AuthType.AKSK)
    public Map<Long, String> batchSaveActivity(@Valid @RequestBody Activity4BatchSave bean) {
        return arrangeService.batchSaveActivity(bean);
    }

    /**
     * 更新活动草稿名称
     *
     * @param bean the bean
     */
    @Override
    @Operation(summary = "更新活动草稿名称")
    @PutMapping(value = "/aom/external/activities/updatedraftname", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @Auth(type = AuthType.AKSK)
    public void updateDraftName(DraftName4Update bean) {
        arrangeService.updateDraftName(bean);
    }

    /**
     * demo复制活动
     *
     * @param bean the bean
     * @return the activity demo copy rsp
     */
    @Override
    @Operation(summary = "demo复制活动")
    @PostMapping(value = "/aom/external/activities/democopy", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    @Auth(type = AuthType.AKSK)
    public ActivityDemoCopyRsp demoCopyActivity(@Valid @RequestBody ActivityDemoCopyReq bean) {
        return arrangeService.demoCopyActivity(bean);
    }
}
