package com.yxt.aom.base.bean.common;

import com.yxt.aom.base.common.BaseErrorConsts;
import com.yxt.common.Constants;
import com.yxt.common.exception.ExceptionKey;
import com.yxt.common.validation.constraints.IntegerScope;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * ActivityName4Check
 */
@Data
public class ActivityName4Check {
    /**
     * 活动/项目名称
     */
    @Schema(description = "活动/项目名称", example = "项目名称test", requiredMode = Schema.RequiredMode.REQUIRED)
    @Size(max = 200, message = BaseErrorConsts.ACTV_NAME_SIZE)
    private String actvName;

    /**
     * 活动/项目的具体类型(UACD注册表中定义)
     */
    @Schema(description = "活动/项目的具体类型(UACD注册表中定义)", example = "proj_o2o", requiredMode = Schema.RequiredMode.REQUIRED)
    private String actvRegId;

    /**
     * 用途类别(0-普通项目/活动, 1-项目/活动模板, 2-集团化项目/活动, 3-移动端项目/活动; 默认为0; 3目前已停用)
     */
    @Schema(description = "用途类别(0-普通项目/活动, 1-项目/活动模板, 2-集团化项目/活动, 3-移动端项目/活动; 默认为0; 3目前已停用)", example = "0")
    @IntegerScope(scope = {0, 1, 2}, message = BaseErrorConsts.ACTV_USAGETYPE_SCOPE)
    private Integer usageType = 0;

    /**
     * 要更新的活动/项目id
     */
    @Schema(description = "要更新的活动/项目id", example = "1849276367003750000")
    @Pattern(regexp = Constants.ID_PATTERN, message = ExceptionKey.UUID_FORMAT_INVALID)
    private String actvId;
}
