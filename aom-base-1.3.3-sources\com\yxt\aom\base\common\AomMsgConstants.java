package com.yxt.aom.base.common;

/**
 * AomMsgConstants
 */
public class AomMsgConstants {
    private AomMsgConstants() {
        throw new UnsupportedOperationException();
    }

    /**
     * 删除时间
     */
    public static final String DELTIME = "{{delTime}}";

    /**
     * 删除操作者
     */
    public static final String DELUSER = "{{delUser}}";
    /**
     * The constant MSG_VAR_ORGID.
     */
    public static final String MSG_VAR_ORGID = "{{orgId}}";
    /**
     * The constant MSG_VAR_ACTVID.
     */
    public static final String MSG_VAR_ACTVID = "{{actvId}}";
    /**
     * The constant MSG_VAR_ITEMID.
     */
    public static final String MSG_VAR_ITEMID = "{{itemId}}";

    /**
     *项目导师
     */
    public static final String TUTOR = "{{Tutor}}";
    /**
     * 截止日期
     */
    public static final String DEAD_LINE = "{{deadLine}}";

    /**
     * The constant URL_H5.
     */
    public static final String URL_H5 = "{{urlH5}}";
    /**
     * The constant URL_PC.
     */
    public static final String URL_PC = "{{urlPC}}";
    /**
     * The constant URL.
     */
    public static final String URL = "{{url}}";
    /**
     * The constant HTTPS.
     */
    public static final String HTTPS = "https://";
    /**
     * The constant CREATE_NAME.
     */
    public static final String CREATE_NAME = "{{createName}}";
    /**
     * The constant GROUP_NAME.
     */
    public static final String GROUP_NAME = "{{groupName}}";
    /**
     * The constant TUTOR_NAMES.
     */
    public static final String TUTOR_NAMES = "{{tutorNames}}";
    /**
     * The constant FULL_NAME.
     */
    public static final String FULL_NAME = "{{fullName}}";
    /**
     * The constant PROJECT_NAME.
     */
    public static final String PROJECT_NAME = "{{projectName}}";
    /**
     * The constant PROJECT_NAME_1.
     */
    public static final String PROJECT_NAME_1 = "{{ProjectName}}";
    /**
     * The constant PROJECT_NAME_2.
     */
    public static final String PROJECT_NAME_2 = "{{project}}";
    /**
     * The constant HOMEWORK_NAME.
     */
    public static final String HOMEWORK_NAME = "{{homeworkName}}";
    /**
     * The constant TASK_NAME.
     */
    public static final String TASK_NAME = "{{taskName}}";
    /**
     * The constant TASK_NAME.
     */
    public static final String TASK_NAME_1 = "{{TaskName}}";
    /**
     * The constant SCENE_NAME.
     */
    public static final String SCENE_NAME = "{{sessionName}}";

    public static final String FLIP_NAME = "{{flipName}}";

    public static final String ATTEND_NAME = "{{attendName}}";

    /**
     * The constant TASK_TYPE.
     */
    public static final String TASK_TYPE = "{{taskType}}";
    /**
     * 阶段
     */
    public static final String STAGE = "{{stage}}";
    /**
     * The constant TASK_RESULT.
     */
    public static final String TASK_RESULT = "{{resultOrScore}}";
    /**
     * The constant TEACHER_NAME.
     */
    public static final String TEACHER_NAME = "{{teacherName}}";
    /**
     * The constant TEACHER_NAME_1.
     */
    public static final String TEACHER_NAME_1 = "{{TeacherName}}";
    /**
     * The constant STUDENT_NAME.
     */
    public static final String STUDENT_NAME = "{{studentName}}";
    /**
     * The constant STUDENT_NAME_1.
     */
    public static final String STUDENT_NAME_1 = "{{StudentName}}";
    /**
     * The constant ADMINISTRATORS_NAME.
     */
    public static final String ADMINISTRATORS_NAME = "{{AdministratorsName}}";
    /**
     * The constant USER_NAME.
     */
    public static final String USER_NAME = "{{userName}}";
    /**
     * The constant USER_NAME_1.
     */
    public static final String USER_NAME_1 = "{{UserName}}";
    /**
     * The constant START_TIME.
     */
    public static final String START_TIME = "{{StartTime}}";
    /**
     * The constant START_TIME_LOWER_CASE.
     */
    public static final String START_TIME_LOWER_CASE = "{{startTime}}";
    /**
     * The constant END_TIME.
     */
    public static final String END_TIME = "{{EndTime}}";
    /**
     * The constant END_TIME_LOWER_CASE.
     */
    public static final String END_TIME_LOWER_CASE = "{{endTime}}";

    public static final String ACTIVITY_NAME = "{{projectName}}";

    public static final String STUDENT_TYPE= "{{Student_type}}";
    /**
     * The constant TASK_TYPE_NAME.
     */
    public static final String TASK_TYPE_NAME = "{{taskTypeName}}";
    /**
     * The constant REMIND_LEVEL_STR.
     */
    public static final String REMIND_LEVEL_STR = "{{remindLevelStr}}";
    /**
     * The constant REMARK_TYPE.
     */
    public static final String REMARK_TYPE = "{{remarkType}}";
    /**
     * The constant REPLY.
     */
    public static final String REPLY = "{{reply}}";
    /**
     * The constant REPLY_USER.
     */
    public static final String REPLY_USER = "{{replyUser}}";
    /**
     * The constant REPLY_TIME.
     */
    public static final String REPLY_TIME = "{{replyTime}}";
    /**
     * The constant COURSE_NAME.
     */
    public static final String COURSE_NAME = "{{courseName}}";
    /**
     * The constant EXAM_NAME.
     */
    public static final String EXAM_NAME = "{{examName}}";
    /**
     * The constant RESULT_SCORE.
     */
    public static final String RESULT_SCORE = "{{得分/结论}}";
    /**
     * The constant PLACE_HOLDER_START_TIME.
     */
    public static final String PLACE_HOLDER_START_TIME = "{{startTime}}";
    /**
     * The constant PLACE_HOLDER_END_TIME.
     */
    public static final String PLACE_HOLDER_END_TIME = "{{endTime}}";
    /**
     * The constant STARTANDEND_TIME.
     */
    public static final String STARTANDEND_TIME = "{{startAndEndTime}}";

    /**
     * The constant CREATE_FULL_NAME.
     */
    public static final String CREATE_FULL_NAME = "{{createFullname}}";
    /**
     * The constant PHASE_NO.
     */
    public static final String PHASE_NO= "{{phaseNo.}}";
    /**
     * The constant ADDRESS.
     */
    public static final String ADDRESS= "{{address}}";
    /**
     * The constant SIGN_STARTANDEND_TIME.
     */
    public static final String SIGN_STARTANDEND_TIME= "{{signStartAndEndTime}}";
    /**
     * The constant TITLE_COMMENT.
     */
    public static final String TITLE_COMMENT = "{{comment}}";
    /**
     * The constant TITLE_NAME.
     */
    public static final String TITLE_NAME = "{{title}}";
    /**
     * The constant NAME.
     */
    public static final String NAME= "{{name}}";
    /**
     * The constant APPLIED_THRESHOLD.
     */
    public static final String APPLIED_THRESHOLD= "{{appliedThreshold}}";
    /**
     * The constant ACTUAL_THRESHOLD.
     */
    public static final String ACTUAL_THRESHOLD= "{{actualThreshold}}";
    /**
     * The constant BUDGET_NAME.
     */
    public static final String BUDGET_NAME= "{{budgetName}}";
    /**
     * The constant BUDGET_YEAR.
     */
    public static final String BUDGET_YEAR= "{{budgetYear}}";
    /**
     * The constant TOTAL_AMOUNT.
     */
    public static final String TOTAL_AMOUNT= "{{totalAmount}}";
    /**
     * The constant APPLIED_AMOUNT.
     */
    public static final String APPLIED_AMOUNT= "{{appliedAmount}}";
    /**
     * The constant ACTUAL_AMOUNT.
     */
    public static final String ACTUAL_AMOUNT= "{{actualAmount}}";
    /**
     * The constant OPERATE_NAME.
     */
    public static final String OPERATE_NAME= "{{operateName}}";
    /**
     * The constant UPDATE_TIME.
     */
    public static final String UPDATE_TIME = "{{updateTime}}";
    /**
     * 下属参与数量
     */
    public static final String UNDERLING_NUM  = "{{underlingNum}}";
    /**
     * The constant DELAY_NUM.
     */
    public static final String DELAY_NUM  = "{{delayNum}}";
    /**
     * The constant UN_COMPLETED_NUM.
     */
    public static final String UN_COMPLETED_NUM  = "{{unCompletedNum}}";
    /**
     * The constant COMPLETED_NUM.
     */
    public static final String COMPLETED_NUM  = "{{completedNum}}";
    /**
     * The constant TIME.
     */
    public static final String TIME  = "{{time}}";
    /**
     * The constant DEPT_RATE.
     */
    public static final String DEPT_RATE  = "{{deptRate}}";
    /**
     * The constant FINISH_RATE.
     */
    public static final String FINISH_RATE = "{{finishRate}}";
    /**
     * The constant REMAIN_DAYS.
     */
    public static final String REMAIN_DAYS = "{{remainDays}}";
    /**
     * The constant PERIOD_NAME.
     */
    public static final String PERIOD_NAME = "{{periodName}}";
    /**
     * The constant IDENTITY.
     */
    public static final String IDENTITY = "{{identity}}";

    /**
     * 鉴定任务
     */
    public static final String SCORE= "{{score}}";
    /**
     * The constant STATUS.
     */
    public static final String STATUS = "{{status}}";

    /**
     * 催促批阅人
     */
    public static final String REMARK_COUNT = "{{count}}";
    /**
     * The constant TASK_TIME.
     */
    public static final String TASK_TIME = "{{taskTime}}";
    /**
     * The constant MODULE_NAME.
     */
    public static final String MODULE_NAME = "{{moduleName}}";

    /**
     * The constant OJTMODE.
     */
    public static final String OJTMODE = "{{ojtMode}}";
    /**
     * The constant PROGRESS.
     */
    public static final String PROGRESS = "{{progress}}";
    /**
     * The constant FORMAL_TYPE.
     */
    public static final String FORMAL_TYPE= "{{formal_type}}";
    /**
     * 直播嘉宾消息模版相关
     */
    public static final String ROOM_NAME = "{{roomName}}";
    /**
     * The constant BEGIN_TIME.
     */
    public static final String BEGIN_TIME = "{{beginTime}}";
    /**
     * 创建群聊失败原因
     */
    public static final String FAILURE_REASON = "{{failureReason}}";

    /**
     * The constant DAYS.
     */
    public static final String DAYS = "{{Days}}";

    public static final String DAYS2 = "{{days}}";
    /**
     * The constant ENDDATE.
     */
    public static final String ENDDATE = "{{endDate}}";

    /**
     * The constant PROJECTID.
     */
    public static final String PROJECTID = "{{projectId}}";

    /**
     * The constant TASKID.
     */
    public static final String TASKID = "{{taskId}}";

    /**
     * The constant TARGETID.
     */
    public static final String TARGETID = "{{targetId}}";

    /**
     * The constant ACTVREGID.
     */
    public static final String ACTVREGID = "{{actvRegId}}";

    /**
     * The constant TASKACTVREGID.
     */
    public static final String TASKACTVREGID = "{{taskActvRegId}}";

    /**
     * The constant SERVERPATH.
     */
    public static final String SERVERPATH = "{{serverPath}}";

    /**
     * The constant PLAYTYPE.
     */
    public static final String PLAYTYPE = "{{playType}}";

    public static final String RESULT = "{{Result}}";

    /**
     * 学员项目详情
     */
    public static final String PROJECT_DETAIL_PC = "/o2o/#/project/detail/";

    /**
     * The constant PROJECT_DETAIL_H5.
     */
    public static final String PROJECT_DETAIL_H5 = "/#/o2o/project/detail/";

    /**
     * The constant STU_PROJECT_DETAIL_H5.
     */
    public static final String STU_PROJECT_DETAIL_H5 = "#/o2o/project/detail/";

    /**
     * The constant TASK_DETAIL_PC.
     */
    public static final String TASK_DETAIL_PC = "/auth/uacd/transfer?isPc=true&projectId={{projectId}}&taskId={{taskId}}&targetId={{targetId}}&actvRegId={{actvRegId}}&taskActvRegId={{taskActvRegId}}&serverPath={{serverPath}}&playType={{playType}}";

    /**
     * The constant TASK_DETAIL_H5.
     */
    public static final String TASK_DETAIL_H5 = "/auth/uacd/transfer?isPc=false&projectId={{projectId}}&taskId={{taskId}}&targetId={{targetId}}&actvRegId={{actvRegId}}&taskActvRegId={{taskActvRegId}}&serverPath={{serverPath}}&playType={{playType}}";

    public static final String GROUP_HONOR_TITLE = "【荣誉发放通知】";
    public static final String GROUP_HONOR_CONTENT = "恭喜【%s】获得荣誉【%s】";
}
