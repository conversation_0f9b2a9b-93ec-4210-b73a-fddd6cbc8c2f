package com.yxt.aom.base.bean.part.title;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.yxt.common.Constants;
import com.yxt.common.annotation.timezone.DateFormatField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.Collections;
import java.util.List;

import static com.yxt.aom.base.common.DefaultValueConstants.Numbers.INT_0;

@Schema(name = "授予称号请求")
@Getter
@Setter
public class GrantTitleRequest {

    @Schema(description = "称号id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long titleId;

    @Schema(description = "项目id")
    private String actvId;

    @Schema(description = "参与id")
    private Long partId;

    @Schema(description = "一句话点评")
    private String comment;

    @Schema(description = "是否消息通知学员：0-否，1-是")
    private Integer notify = INT_0;

    @Schema(description = "小组id")
    private Long groupId;

    @Schema(description = "关键字")
    private String keyword;

    @Schema(description = "账号状态", title = "账号状态 0禁用 1启用 不传取所有", example = "1")
    private Integer status;

    @Schema(description = "部门id")
    private List<String> deptIds;

    @Schema(description = "直属经理id")
    private List<String> managerIds;

    @Schema(description = "岗位id")
    private List<String> positionIds;

    @Schema(description = "1正式学员 0旁听学员", example = "1")
    private Integer formal;

    @Schema(description = "开始学习开始时间", example = "2019-10-08")
    @DateFormatField(format = Constants.SDF_YEAR2DAY)
    private String startTime;

    @Schema(description = "开始学习结束时间", example = "2019-10-08")
    @DateFormatField(format = Constants.SDF_YEAR2DAY)
    private String endTime;

    @Schema(description = "是否全选", example = "true")
    private boolean checkAll;

    @Schema(description = "选择的用户ids列表，当checkAll=false时需要传入")
    private List<String> userIds;

    @Schema(description = "排除学员id")
    private List<String> excludeUserIds = Collections.emptyList();

    @Schema(description = "完成状态 0未开始、1学习中、2已完成、3逾期完成", example = "1")
    private Integer studyStatus;
}
