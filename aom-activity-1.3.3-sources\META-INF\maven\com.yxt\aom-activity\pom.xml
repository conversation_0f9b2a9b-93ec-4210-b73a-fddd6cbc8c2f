<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.yxt.usdk.bom</groupId>
    <artifactId>usdk-bom-waf</artifactId>
    <version>1.2.7-jdk17</version>
  </parent>
  <groupId>com.yxt</groupId>
  <artifactId>aom-activity</artifactId>
  <version>1.3.3</version>
  <name>aom-activity</name>
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0</url>
    </license>
  </licenses>
  <properties>
    <usdk-adapter-version>1.2.7-jdk17</usdk-adapter-version>
    <druid-spring-boot-starter.version>1.2.20</druid-spring-boot-starter.version>
    <aom-base.version>1.3.3</aom-base.version>
    <mysql-connector-java.version>8.0.33</mysql-connector-java.version>
  </properties>
  <dependencies>
    <dependency>
      <groupId>com.yxt.usdk.framework.adapter</groupId>
      <artifactId>usdk-framework-adapter-waf</artifactId>
      <version>${usdk-adapter-version}</version>
    </dependency>
    <dependency>
      <groupId>org.mybatis.spring.boot</groupId>
      <artifactId>mybatis-spring-boot-starter</artifactId>
    </dependency>
    <dependency>
      <groupId>com.baomidou</groupId>
      <artifactId>mybatis-plus-boot-starter</artifactId>
    </dependency>
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>druid-spring-boot-starter</artifactId>
    </dependency>
    <dependency>
      <groupId>mysql</groupId>
      <artifactId>mysql-connector-java</artifactId>
      <version>${mysql-connector-java.version}</version>
    </dependency>
    <dependency>
      <groupId>com.yxt</groupId>
      <artifactId>aom-base</artifactId>
      <version>${aom-base.version}</version>
    </dependency>
  </dependencies>
</project>
