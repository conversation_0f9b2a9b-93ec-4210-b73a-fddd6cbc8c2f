package com.yxt.aom.base.bean.sn;

import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;
import java.util.Objects;

import static com.yxt.aom.base.common.DefaultValueConstants.Numbers.INT_1;

/**
 * @description:
 * @author: dingjh
 * @date: 2025/6/11 17:26
 */
@Data
public class ActivityItemTree {

    @Schema(description = "活动任务明细数据，包含阶段，任务组，活动任务；如果有带教请合并进来", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<ActivityItem> items = Lists.newArrayList();

    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ActivityItem {

        @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED)
        private String id;
        @Schema(description = "父级id", requiredMode = Schema.RequiredMode.REQUIRED)
        private String parentId;
        @Schema(description = "名称", requiredMode = Schema.RequiredMode.REQUIRED)
        private String refName;
        @Schema(description = "节点类型(0-叶节点, 1-目录节点，2-任务组)", requiredMode = Schema.RequiredMode.REQUIRED)
        private Integer itemType;
        @Schema(description = "是否必修,1:是,0:否;学员的动态调整任务需要转化完成传入", requiredMode = Schema.RequiredMode.REQUIRED)
        private Integer required;
        @Schema(description = "阶段内按顺序解锁,1:是,0:否", requiredMode = Schema.RequiredMode.REQUIRED)
        private Integer seqLearning;
        @Schema(description = "是否关卡任务：1：是,0：否", requiredMode = Schema.RequiredMode.REQUIRED)
        private Integer barriered;
        @Schema(description = "是否隐藏：0-否，1-是", requiredMode = Schema.RequiredMode.REQUIRED)
        private Integer hidden;
        @Schema(description = "是否锁定：0-否，1-是", requiredMode = Schema.RequiredMode.REQUIRED)
        private Integer locked;
        @Schema(description = "排序", requiredMode = Schema.RequiredMode.REQUIRED)
        private Integer orderIndex;
        @Schema(description = "任务解锁时间", requiredMode = Schema.RequiredMode.REQUIRED)
        private Long unlockTime;
        @Schema(description = "阶段周期", requiredMode = Schema.RequiredMode.REQUIRED)
        private Integer cycleTime;
        @Schema(description = "阶段周期开始天数", requiredMode = Schema.RequiredMode.REQUIRED)
        private Integer cycleStartTime;
        @Schema(description = "阶段周期结束天数", requiredMode = Schema.RequiredMode.REQUIRED)
        private Integer cycleEndTime;
        @Schema(description = "逾期时间", requiredMode = Schema.RequiredMode.REQUIRED)
        private Long overdueTime;
        @Schema(description = "固定开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
        private Long startTime;
        @Schema(description = "固定截止时间", requiredMode = Schema.RequiredMode.REQUIRED)
        private Long endTime;
        @Schema(description = "任务创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
        private Long createTime;
        @Schema(description = "学员任务是否完成：0-未完成；1-已完成", requiredMode = Schema.RequiredMode.REQUIRED)
        private Integer completed;
        @Schema(description = "学员开始学习时间", requiredMode = Schema.RequiredMode.REQUIRED)
        private Long userStartTime;
        @Schema(description = "子节点列表", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
        private List<ActivityItem> children;

        /**
         * 阶段是否限制顺序学习
         *
         * @return
         */
        public boolean getIsLimitSeq() {
            if (Objects.isNull(seqLearning)) {
                return false;
            }
            return Objects.equals(seqLearning, INT_1);
        }
    }
}
