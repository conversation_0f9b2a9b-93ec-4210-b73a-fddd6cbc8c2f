package com.yxt.aom.activity.mq;

import com.alibaba.fastjson.JSON;
import com.yxt.aom.activity.service.sco.ScoResultService;
import com.yxt.aom.base.enums.ActivityTypeEnum;
import com.yxt.aom.base.manager.common.AomRegistryManager;
import com.yxt.aom.base.util.AomUtils;
import com.yxt.aom.base.wrapper.UacdWrapper;
import com.yxt.aom.common.config.AomDbProperties;
import com.yxt.aom.datamodel.event.ScoResultEvent;
import com.yxt.aom.datamodel.scoresult.BaseScoResult;
import com.yxt.common.util.BeanHelper;
import com.yxt.uacd.facade.bean.RegistryConfigBean;
import com.yxt.uacd.facade.bean.module.DesignerModule4Simple;
import com.yxt.usdk.components.rocketmq.autoconfigure.RocketMQExtProperties;
import com.yxt.usdk.components.rocketmq.core.CustomizedMQPushConsumer;
import com.yxt.usdk.components.rocketmq.support.RocketMQUtil;
import jodd.util.StringPool;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.SmartInitializingSingleton;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
@ConditionalOnProperty(name = "aom.rocketmq.consumer-enabled", havingValue = "true", matchIfMissing = true)
public class ScoResultListener implements SmartInitializingSingleton {
    private final AomRegistryManager aomRegistryManager;
    private final RocketMQExtProperties mqProperties;
    private final ScoResultService scoResultService;
    private final UacdWrapper uacdWrapper;
    private final AomDbProperties aomDbProperties;

    @Value("${rocketmq.name-server}")
    private String nameServer;

    @Value("${spring.application.name}")
    private String serviceName;

    @Override
    public void afterSingletonsInstantiated() {
        consumeScoResult();
    }

    private void consumeScoResult() {

        Map<String, String> dsMap = aomDbProperties.getDsmap();

        if (MapUtils.isEmpty(dsMap)) {
            log.info("ScoResultListener dsMap is empty");
            return;
        }
        log.info("ScoResultListener dsMap : {}", JSON.toJSONString(dsMap));

        Map<String, RegistryConfigBean> registryConfigMap = aomRegistryManager.getRegistryMap();
        if (MapUtils.isEmpty(registryConfigMap)) {
            log.info("ScoResultListener registryConfigMap is empty");
            return;
        }

        dsMap.forEach((regId, dataSource) -> {
            if (!AomUtils.isPrefixMatch(regId, ActivityTypeEnum.ACTV)) {
                log.info("ScoResultListener regId : {} currentConfig is not for project", regId);
                return;
            }
            RegistryConfigBean currentConfig = registryConfigMap.get(regId);
            if (currentConfig == null) {
                log.info("ScoResultListener regId : {} currentConfig is empty", regId);
                return;
            }
            List<DesignerModule4Simple> designerModule4Simples = uacdWrapper.listDesignerModule4Simples(regId);
            if (CollectionUtils.isEmpty(designerModule4Simples)) {
                log.info("ScoResultListener regId : {} designerModule4Simples is empty", regId);
                return;
            }
            Set<String> registryIds = designerModule4Simples.stream().map(DesignerModule4Simple::getRegistryId)
                    .collect(Collectors.toSet());
            log.info("ScoResultListener regId : {} registryIds : {}", regId, registryIds);
            registryConsume(regId, registryIds, registryConfigMap, currentConfig);
        });
    }

    private void registryConsume(String projRegId, Set<String> registryIds,
                                 Map<String, RegistryConfigBean> registryConfigMap, RegistryConfigBean currentConfig) {

        String currentRegId = currentConfig.getId();

        registryIds.forEach(scoRegId -> {
            RegistryConfigBean registryConfigBean = registryConfigMap.get(scoRegId);
            if (registryConfigBean == null) {
                log.info("ScoResultListener regId : {} registryConfigBean is null", projRegId);
                return;
            }
            if (StringUtils.isBlank(registryConfigBean.getResultMqTopic())) {
                log.info("ScoResultListener regId : {} registryConfigBean resultMqTopic is null", projRegId);
                return;
            }
            try {
                final CustomizedMQPushConsumer consumer = new CustomizedMQPushConsumer();
                consumer.setNamesrvAddr(nameServer);
                String currentTopic = RocketMQUtil.handlePostfix(registryConfigBean.getResultMqTopic(), mqProperties);
                consumer.subscribe(currentTopic, StringPool.ASTERISK); // 订阅主题和标签
                consumer.setConsumerGroup(currentConfig.getServiceName() + "-group-" + currentTopic); // 设置消费者组
                consumer.setConsumeThreadMin(2);
                consumer.setConsumeThreadMax(4);
                consumer.registerMessageListener((MessageListenerConcurrently) (msgs, context) -> {
                    for (MessageExt msg : msgs) {
                        try {
                            String body = new String(msg.getBody(), StandardCharsets.UTF_8);
                            ScoResultEvent<BaseScoResult> scoResultEvent = BeanHelper.json2Bean(body, ScoResultEvent.class,
                                    BaseScoResult.class);
                            scoResultService.consumeScoResult(scoResultEvent,body,currentRegId);
                        } catch (Exception e) {
                            log.error("ScoResultListener consume failed: {}", e.getMessage(), e);
                        }
                    }
                    return ConsumeConcurrentlyStatus.CONSUME_SUCCESS; // 消息处理成功
                });
                consumer.start();
                Runtime.getRuntime().addShutdownHook(new Thread(consumer::shutdown));
                log.info("ScoResultListener consumerResult started : {} topic ： {} currentTopic : {}", scoRegId,
                        registryConfigBean.getResultMqTopic(), currentTopic);
            } catch (Exception e) {
                log.error("ScoResultListener Failed to start RocketMQ consumer: {}", e.getMessage(), e);
            }
        });
    }

}
