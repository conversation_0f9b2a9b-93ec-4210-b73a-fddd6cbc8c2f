package com.yxt.aom.base.bean.part.group;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 重新分组
 *
 * @since 2021/6/4 10:01
 */
@Getter
@Setter
public class GroupUserIdDivideBean {
    /**
     * /小组id
     */
    private Long groupId;
    /**
     * / 该小组之前的用户（不包含组长和群主）
     */
    private List<String> oldUserIds;
    /**
     * / 该小组新的用户（不包含组长和群主）
     */
    private List<String> newUserIds;
}
