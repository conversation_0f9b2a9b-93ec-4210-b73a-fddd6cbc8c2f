package com.yxt.aom.activity.mq;

import com.alibaba.fastjson.JSON;
import com.yxt.aom.base.bean.part.PartUserChangeBean;
import com.yxt.aom.base.common.AomConstants;
import com.yxt.aom.base.service.part.impl.ActivityParticipationMemberService;
import com.yxt.aom.common.config.AomDataSourceTypeHolder;
import com.yxt.aom.common.config.AomDbProperties;
import com.yxt.dbrouter.core.toolkit.DBRouteHolder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.Map;

import static com.yxt.aom.base.common.AomMqConstants.TOPIC_ACTIVITY_MEMBER_CHANGE_THIRD;

/**
 * 更新学员
 */
@Slf4j
@RequiredArgsConstructor
@Component
@ConditionalOnProperty(name = "aom.rocketmq.consumer-enabled", havingValue = "true", matchIfMissing = true)
@RocketMQMessageListener(consumerGroup = AomConstants.MQ_GROUP_PREFIX
        + TOPIC_ACTIVITY_MEMBER_CHANGE_THIRD, topic = TOPIC_ACTIVITY_MEMBER_CHANGE_THIRD,
        consumeMode = ConsumeMode.CONCURRENTLY, consumeThreadNumber = 3)
public class ActivityMemberChangeListener implements RocketMQListener<String> {
    private final AomDbProperties aomDbProperties;

    private final ActivityParticipationMemberService activityParticipationMemberService;

    @Override
    public void onMessage(String message) {
        log.info("ActivityMemberChangeListener, message: {}", message);
        try {
            if(StringUtils.isEmpty(message)) {
                return;
            }
            PartUserChangeBean bean = JSON.parseObject(message, PartUserChangeBean.class);
            if (bean == null) {
                log.warn("ActivityMemberChangeListener invalid message: {}", message);
                return;
            }
            Map<String, String> dsmap = aomDbProperties.getDsmap();
            if (!dsmap.containsKey(bean.getRegId())) {
                return;
            }
            AomDataSourceTypeHolder.set(bean.getRegId());
            DBRouteHolder.push(bean.getOrgId());
            activityParticipationMemberService.batchChangeUser(bean);
        } catch (Exception ex) {
            log.error("ActivityMemberChangeListener message failed. message: {} , err: ", message, ex);
        }
    }
}
