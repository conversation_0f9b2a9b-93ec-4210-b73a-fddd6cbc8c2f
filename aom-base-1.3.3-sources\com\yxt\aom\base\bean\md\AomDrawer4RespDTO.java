package com.yxt.aom.base.bean.md;

import com.yxt.common.annotation.timezone.DateFormatField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 时区转换 模型自定义对象
 *
 * <AUTHOR>
 * @since 2024/12/16
 */
@Getter
@Setter
@Schema(description = "模型多实体抽屉类型引用信息")
public class AomDrawer4RespDTO {

    @Schema(description = "应用唯一标识")
    private String unionId;

    @Schema(description = "实体编号")
    private String entityCode;

    @Schema(description = "数据")
    @DateFormatField(isobj = true)
    private List<Object> datas;

}
