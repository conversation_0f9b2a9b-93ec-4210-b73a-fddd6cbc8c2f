package com.yxt.aom.base.bean.md;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yxt.common.annotation.timezone.DateFormatField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

import static com.yxt.common.Constants.SDF_YEAR2SECOND;

/**
 * <AUTHOR>
 * @since 2024/11/20
 */
@Getter
@Setter
@NoArgsConstructor
public class AomUserStatisticsBean {

    @Schema(description = "学员活动完成状态 0-未完成，1-进行中，2-已完成")
    private String actvCompletedStatus;

    @Schema(description = "活动完成进度")
    private BigDecimal actCompletedRate;

    @Schema(description = "必修活动完成率")
    private BigDecimal requiredTaskCompletedRate;

    @Schema(description = "选修活动完成进度")
    private BigDecimal electiveTaskCompletedRate;

    @Schema(description = "第一次学习项目时间", example = "2022-10-09 00:00:00")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = SDF_YEAR2SECOND, timezone = "GMT+8")
    @DateFormatField(isDate = true)
    private Date firstStudyTime;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = SDF_YEAR2SECOND, timezone = "GMT+8")
    @Schema(description = "最近学习时间", example = "2021-08-08 17:11:58")
    @DateFormatField(isDate = true)
    private Date lastStudyTime;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = SDF_YEAR2SECOND, timezone = "GMT+8")
    @Schema(description = "学员学习开始时间", example = "2021-08-08 17:11:58")
    @DateFormatField(isDate = true)
    private Date startTime;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = SDF_YEAR2SECOND, timezone = "GMT+8")
    @Schema(description = "学员学习结束时间", example = "2021-08-08 17:11:58")
    @DateFormatField(isDate = true)
    private Date endTime;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = SDF_YEAR2SECOND, timezone = "GMT+8")
    @Schema(description = "截止学习时间", example = "2021-08-08 17:11:58")
    @DateFormatField(isDate = true)
    private Date endStudyTime;

}
