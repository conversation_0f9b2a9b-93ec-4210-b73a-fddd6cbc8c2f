package com.yxt.aom.base.bean.sn;

import com.yxt.aom.base.util.ActivitySnDateUtils;
import com.yxt.common.util.BeanCopierUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import com.yxt.aom.base.bean.sn.ActivityItemTree.ActivityItem;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.yxt.common.enums.YesOrNo.NO;
import static com.yxt.common.enums.YesOrNo.YES;

/**
 * @description:
 * @author: dingjh
 * @date: 2025/6/12 16:06
 */
public class ActivitySnResponseConvert {
    /**
     * 加锁
     *
     * @param treeNode 任务节点
     * @return 转换响应结果
     */
    public static ActivitySnResponse convertUnlock(ActivityItem treeNode) {
        ActivitySnResponse resp = convertCommon(treeNode);
        resp.setLockStatus(NO.getValue());
        return resp;
    }

    /**
     * 加锁
     *
     * @param nodeList 任务节点
     * @return 转换响应结果
     */
    public static Map<String, ActivitySnResponse> convertCycleUnlock(Map<String, ActivitySnResponse> results,
            List<ActivityItem> nodeList) {
        if (CollectionUtils.isEmpty(nodeList)) {return results;}
        nodeList.forEach(node -> {
            if (ObjectUtils.isNotEmpty(results.get(node.getId()))) {
                results.get(node.getId()).setLockStatus(NO.getValue());
            }
        });
        return results;
    }

    /**
     * 解锁
     *
     * @param treeNode 任务节点
     * @return 转换响应结果
     */
    public static ActivitySnResponse convertLock(ActivityItem treeNode) {
        ActivitySnResponse resp = convertCommon(treeNode);
        resp.setLockStatus(YES.getValue());
        return resp;
    }

    /**
     * 解锁
     *
     * @param nodeList 任务节点
     * @return 转换响应结果
     */
    public static Map<String, ActivitySnResponse> convertCycleLock(Map<String, ActivitySnResponse> results,
            List<ActivityItem> nodeList) {
        if (CollectionUtils.isEmpty(nodeList)) {return results;}
        nodeList.forEach(node -> {
            if (ObjectUtils.isNotEmpty(results.get(node.getId()))) {
                results.get(node.getId()).setLockStatus(YES.getValue());
            }
        });
        return results;
    }

    private static ActivitySnResponse convertCommon(ActivityItem treeNode) {
        ActivitySnResponse resp = new ActivitySnResponse();
        BeanCopierUtil.copy(treeNode, resp, false);
        resp.setUnlockTime(Objects.isNull(treeNode.getUnlockTime()) ? null : treeNode.getUnlockTime());
        resp.setOverdueTime(Objects.isNull(treeNode.getOverdueTime()) ? null : treeNode.getOverdueTime());
        return resp;
    }

    public static ActivitySnResponse convertStageByLockTime(ActivityItem treeNode) {
        ActivitySnResponse resp = convertCommon(treeNode);
        if (Objects.isNull(resp.getUnlockTime())) {
            resp.setLockStatus(YES.getValue());
            return resp;
        }
        if (ActivitySnDateUtils.compareDate(treeNode.getUnlockTime(), ActivitySnDateUtils.currentTime()) > 0) {
            resp.setLockStatus(NO.getValue());
        } else {
            resp.setLockStatus(YES.getValue());
        }
        if (treeNode.getStartTime() != null && treeNode.getStartTime() != 0L) {
            if (ActivitySnDateUtils.compareDate(treeNode.getStartTime(), ActivitySnDateUtils.currentTime()) > 0) {
                resp.setLockStatus(NO.getValue());
            } else {
                resp.setLockStatus(YES.getValue());
            }
        }
        return resp;
    }

    public static ActivitySnResponse convertWithStatus(ActivityItem treeNode, Integer status) {
        ActivitySnResponse resp = convertCommon(treeNode);
        resp.setLockStatus(status);
        return resp;
    }

}
