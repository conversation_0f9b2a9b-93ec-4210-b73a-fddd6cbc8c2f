package com.yxt.aom.activity.entity.arrange;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * ActivityDraft
 */
@Data
@TableName("aom_activity_draft")
public class ActivityDraft implements Serializable {
    @Serial
    private static final long serialVersionUID = 2029791345833436070L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 机构id
     */
    private String orgId;

    /**
     * 活动/项目id
     */
    private String actvId;

    /**
     * 叶节点引用对象的具体类型(UACD注册表中定义)
     */
    private String refRegId;

    /**
     * 叶节点引用对象的id
     */
    private String refId;

    /**
     * 表单数据
     */
    private String formData;

    /**
     * UACD大纲节点id
     */
    private Long itemId;

    /**
     * 草稿数据状态(1-当前草稿数据待转正, 2-当前草稿数据已转正)
     */
    private Integer draftStatus;

    /**
     * 创建人id
     */
    private String createUserId;

    /**
     * 更新人id
     */
    private String updateUserId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除(0-未删除, 1-已删除)
     */
    private Integer deleted;

}
