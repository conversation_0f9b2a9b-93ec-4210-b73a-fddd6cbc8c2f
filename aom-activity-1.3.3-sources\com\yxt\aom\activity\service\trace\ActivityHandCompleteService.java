package com.yxt.aom.activity.service.trace;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.google.common.collect.Lists;
import com.yxt.aom.activity.bean.TaskHandCompleteResp;
import com.yxt.aom.activity.common.AomActvApiError;
import com.yxt.aom.activity.service.rollup.impl.ResultRollUpService;
import com.yxt.aom.base.bean.part.PartMemberPageCriteria;
import com.yxt.aom.base.entity.arrange.ActivityArrange;
import com.yxt.aom.base.entity.arrange.ActivityArrangeItem;
import com.yxt.aom.base.entity.control.BaseActivityResult;
import com.yxt.aom.base.enums.ActvResultStatusEnum;
import com.yxt.aom.base.manager.common.AomRegistryManager;
import com.yxt.aom.base.mapper.arrange.ActivityArrangeItemMapper;
import com.yxt.aom.base.mapper.arrange.ActivityArrangeMapper;
import com.yxt.aom.base.mapper.control.BaseActivityResultMapper;
import com.yxt.aom.base.mapper.part.ActivityParticipationMemberMapper;
import com.yxt.aom.datamodel.activityresult.AssessmentActivityResult;
import com.yxt.aom.datamodel.common.ActionEnum;
import com.yxt.aom.datamodel.common.Actor;
import com.yxt.aom.datamodel.common.TargetObject;
import com.yxt.common.exception.ApiException;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.util.BeanHelper;
import com.yxt.common.util.Validate;
import com.yxt.uacd.facade.bean.RegistryConfigBean;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * <AUTHOR> 活动手动标记完成
 * @date 2024/11/14 20:14
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ActivityHandCompleteService {
    private final ActivityArrangeMapper activityArrangeMapper;
    private final ActivityArrangeItemMapper activityArrangeItemMapper;
    private final BaseActivityResultMapper baseActivityResultMapper;
    private final AomRegistryManager aomRegistryManager;
    private final ActivityParticipationMemberMapper activityParticipationMemberMapper;
    private final ResultRollUpService resultRollUpService;

    /**
     * 标记完成
     *
     * @param userCacheDetail      用户信息
     * @param taskHandCompleteResp 标记条件
     */
    public void handComplete(UserCacheDetail userCacheDetail, TaskHandCompleteResp taskHandCompleteResp) {
        log.info("标记完成开始:{}", JSON.toJSONString(taskHandCompleteResp));

        String orgId = userCacheDetail.getOrgId();
        String operatorUserId = userCacheDetail.getUserId();
        String activityId = taskHandCompleteResp.getActivityId();
        Long itemId = taskHandCompleteResp.getItemId();
        boolean checkAll = taskHandCompleteResp.isCheckAll();
        Long participationId = taskHandCompleteResp.getParticipationId();
        Set<String> userIds = taskHandCompleteResp.getUserIds();
        Set<String> excludeUserIds = taskHandCompleteResp.getExcludeUserIds();

        ActivityArrange activityArrange = activityArrangeMapper.selectById(activityId);
        Validate.isNotNull(activityArrange, AomActvApiError.ACTIVITY_ARRANGE_NOT_FOUND);

        ActivityArrangeItem activityArrangeItem = activityArrangeItemMapper.selectById(itemId);
        Validate.isNotNull(activityArrangeItem, AomActvApiError.ITEM_NOTFOUND);

        //获取任务类型 1-Assessment, 2-Content, 3-Practice
        RegistryConfigBean registryConfig = aomRegistryManager.getRegistryConfig(activityArrangeItem.getRefRegId());
        Integer subType = registryConfig.getSubType();
        // 是否全选
        if (checkAll) {
            userIds = allUser(orgId, activityId, itemId, excludeUserIds, subType, taskHandCompleteResp);
        } else {
            filterUserNotInArrange(orgId, activityId, participationId, userIds);
            List<BaseActivityResult> baseActivityResults = baseActivityResultMapper.getItemResultByUserIds(orgId,
                    activityId, itemId, Lists.newArrayList(userIds));
            if (CollectionUtils.isNotEmpty(baseActivityResults)) {
                baseActivityResults = baseActivityResults.stream().filter(baseActivityResult -> ActvResultStatusEnum.COMPLETED.getValue() != baseActivityResult.getResultStatus()).collect(Collectors.toList());
                userIds = baseActivityResults.stream().map(BaseActivityResult::getUserId).collect(Collectors.toSet());
            } else {
                return;
            }
        }
        this.dataProcessing(orgId, activityId, subType, userIds, operatorUserId);
    }

    /**
     * 标记完成数据处理
     *
     * @param orgId          机构id
     * @param activityId     业务id
     * @param subType        任务类型 1-Assessment, 2-Content, 3-Practice
     * @param userIds        学员
     * @param operatorUserId 操作者
     */
    @Async
    public void dataProcessing(String orgId, String activityId, Integer subType, Set<String> userIds, String operatorUserId) {
        TargetObject targetObject = new TargetObject();
        //目前只有Assessment类型
        targetObject.setTargetId(activityId);
        targetObject.setSourceId(activityId);
        if (subType == 1) {
            userIds.forEach(userId -> {
                Actor actor = new Actor();
                actor.setUserId(userId);
                AssessmentActivityResult assessmentActivityResult = generatedAssessmentActivityResult(orgId);
                resultRollUpService.rollUpAssessmentActivityResult(orgId, ActionEnum.COMPLETED, actor, targetObject, assessmentActivityResult);
            });
        }
    }

    /**
     * 生成结果数据
     *
     * @param orgId 机构id
     * @return 数据
     */
    private AssessmentActivityResult generatedAssessmentActivityResult(String orgId) {
        AssessmentActivityResult generated = new AssessmentActivityResult();
        generated.setHandCompleted(1);
        generated.setCompletedTime(new Date());
        generated.setOrgId(orgId);
        return generated;
    }

    /**
     * 全部学员
     *
     * @param orgId          机构id
     * @param activityId     业务id
     * @param excludeUserIds 过滤学员
     * @param subType        任务类型 1-Assessment, 2-Content, 3-Practice
     * @param search         搜索条件
     * @return 用户ids
     */
    private Set<String> allUser(String orgId, String activityId, Long itemId, Set<String> excludeUserIds, Integer subType, TaskHandCompleteResp search) {
        PartMemberPageCriteria criteria = getCriteria(orgId, activityId, search);
        //目前只有Assessment类型
        if (subType == 1) {
            Set<String> noCompleteUserIds = activityParticipationMemberMapper.getNoCompleteUserIds(orgId, itemId, criteria);
            if (CollectionUtils.isNotEmpty(excludeUserIds)) {
                noCompleteUserIds = noCompleteUserIds.stream().filter(noCompleteUserId -> !excludeUserIds.contains(noCompleteUserId)).collect(Collectors.toSet());
            }
            return noCompleteUserIds;
        }
        return new HashSet<>();
    }


    /**
     * 验证用户是否在参与项目中
     *
     * @param orgId   机构id
     * @param userIds 用户ids
     */
    private void filterUserNotInArrange(String orgId, String actvId, long partId, Set<String> userIds) {
        if (CollectionUtils.isNotEmpty(userIds)) {
            Set<String> existUserIds = activityParticipationMemberMapper.listExistPartUserId(orgId, actvId, partId, userIds, null);
            List<String> exceptionUserIds = userIds.stream().filter(userId -> !existUserIds.contains(userId)).toList();
            if (CollectionUtils.isNotEmpty(exceptionUserIds)) {
                log.info("标记完成异常用户,orgId:{},actvId:{},partId{},exceptionUserIds:{}", orgId, actvId, partId,
                        BeanHelper.bean2Json(exceptionUserIds, JsonInclude.Include.ALWAYS));
                throw new ApiException(AomActvApiError.ACTIVITY_HAND_COMPLETE_USER_EXCEPTION);
            }
        }
    }

    /**
     * 组装查询条件
     *
     * @param orgId  机构id
     * @param actvId actvId
     * @param req    条件
     * @return 条件
     */
    private PartMemberPageCriteria getCriteria(String orgId, String actvId, TaskHandCompleteResp req) {
        final PartMemberPageCriteria criteria = PartMemberPageCriteria.of().setOrgId(orgId)
                .setParticipationId(req.getParticipationId()).setActvId(actvId)
                .setStatus(req.getStatus()).setFormal(req.getFormal());

        if (StringUtils.isNotEmpty(req.getKeyword())) {
            criteria.setKeyword(req.getKeyword());
        }
        // 企业微信环境下
        //        List<String> weixUserIds = orginitWrapper.contactOrgSearch(orgId, req.getKeyword(), soruceCode);
        //        if (CollectionUtils.isNotEmpty(weixUserIds)) {
        //            criteria.setWeixUserIds(weixUserIds.stream().distinct().collect(Collectors.toList()));
        //        }
        if (CollectionUtils.isNotEmpty(req.getDeptIds())) {
            criteria.setDeptIds(req.getDeptIds().stream().distinct().collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(req.getPositionIds())) {
            criteria.setPositionIds(req.getPositionIds().stream().distinct().collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(req.getManagerIds())) {
            criteria.setManagerIds(req.getManagerIds().stream().distinct().collect(Collectors.toList()));
        }
        if (StringUtils.isNotBlank(req.getStartTime())) {
            criteria.setStartTime(req.getStartTime());
        }
        if (StringUtils.isNotBlank(req.getEndTime())) {
            criteria.setEndTime(req.getEndTime());
        }
        if (!req.isCheckAll()) {
            criteria.setUserIds(Lists.newArrayList(req.getUserIds()));
        }
        criteria.setStatus(req.getStatus());
        return criteria;
    }
}
