package com.yxt.aom.base.common;

/**
 * <AUTHOR>
 * @date 2024/10/29  20:33
 * @description 描述
 */
public final class AomPropConstants {
    private AomPropConstants() {
        throw new UnsupportedOperationException();
    }

    public static final String ITEM_ID = "itemId";
    public static final String TIME = "time";

    public static final String CUSTOM_MEMBER_CHANGE_BEAN_NAME_COMPO = "memberChangeProcessorOnlyProject";

    public static final String USER_NAME = "userName";

    public static final String USER_FULL_NAME = "userFullName";

    public static final String FORMAL = "formal";

    public static final String LEADER = "leader";

    public static final String ERROR_INFO = "errorInfo";

    public static final String ORG_ID = "orgId";

    public static final String UUID = "uuId";

    public static final String LOWER_USER_NAME = "username";

    public static final String LOWER_FULL_NAME = "fullname";

    public static final String USER_STATUS = "userStatus";

    public static final String FORMAL_NAME = "formalName";

    public static final String TITLE = "title";

    public static final String DEPT_NAME = "deptName";

    public static final String POSITION_LIST = "positionList";

    public static final String MOBILE = "mobile";

    public static final String ALL_PROGRESS = "actCompletedRateStr";

    public static final String REQUIRED_PROGRESS = "requiredTaskCompletedRateStr";

    public static final String ELECTIVE_PROGRESS = "electiveTaskCompletedRateStr";

    public static final String OJT_PROGRESS = "ojtProgress";

    public static final String PROGRESS = "progress";

    public static final String STATUS_NAME = "statusName";

    public static final String OVERDUE_STATUS = "overdueStatus";

    public static final String CERTIFICATE_NUM = "certificateNum";

    public static final String CREDIT = "credit";
    /**
     * The constant INTEGRAL.
     */
    public static final String INTEGRAL = "integral";

    public static final String START_TIME_STR = "startTimeStr";

    public static final String LAST_STUDY_TIME_STR = "lastStudyTimeStr";
    public static final String PASSED_NAME = "passedName";
    /**
     * The constant PROJECT_SCORE_STR.
     */
    public static final String PROJECT_SCORE_STR = "projectScoreStr";
    /**
     * The constant PROJECT_COMMENTS.
     */
    public static final String PROJECT_COMMENTS = "projectComments";
    public static final String HIRE_DATE_STR = "hireDateStr";
    public static final String USER_NO = "userNo";

    public static final String SEAT_NO_STR = "seatNoStr";
    public static final String GROUP_NAME = "groupName";
    public static final String TUTOR_NAMES = "tutorNames";
    public static final String JOIN_METHOD_STR = "joinMethodStr";
    public static final String JOIN_TIME_STR = "joinTimeStr";
    public static final String PROJECT_END_TIME= "projectEndTime";

    public static final String ACTV_ID= "actvId";

    public static final String USER_ID = "userId";

    public static final String GROUP_ID = "groupId";

    public static final String ACTV_COMPLETED_STATUS = "actvCompletedStatus";

    public static final String TOKEN = "token";

    public static final String CONTENT = "content";

    public static final String SUBJECT = "subject";

    public static final String EM_PUSH_TITLE = "emPushTitle";

    public static final String APP_ID = "appId";

    public static final String DEST_ID = "destId";

    public static final String TYPE = "type";

    public static final String IM_ID = "im_id";

    public static final String OPEN_ID ="openId";

    public static final String IM_EM_PUSH_TITLE_VALUE = "项目建群";

    public static final String REF_ID ="refId";

    public static final String SEARCH_NAME = "name";
    public static final String SEARCH_USER_NAME = "userName";
    public static final String AUTO_END = "autoEnd";
    public static final String AUTO_ARCHIVE = "autoArchive";
    public static final String DATA = "data";
    /**
     * The constant IMAGE.
     */
    public static final String IMAGE = "image";
    /**
     * The constant SHARETYPE.
     */
    public static final String SHARETYPE = "shareType";
    /**
     * The constant WIDTH.
     */
    public static final String WIDTH = "width";
    /**
     * The constant HEIGHT.
     */
    public static final String HEIGHT = "height";
    public static final String TEXT = "text";

    public static final String INTEGRAL_NO_ALLOT_STUDENT = "恭喜%s获得%s积分";
    public static final String INTEGRAL_ALLOT_STUDENT = "恭喜%s及其成员获得%s积分";
    public static final String INTEGRAL_SEND_TITLE = "【积分发放通知】";
    public static final String IM_GROUP_SCORE_ADD_TITLE = "小组积分通知";
}
