package com.yxt.aom.base.bean.common;

import com.yxt.aom.base.common.BaseErrorConsts;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/27  14:41
 * @description 描述
 */
@Data
@Schema(name = "奖励配置批量更新请求")
public class ActivityRewardConfigBatchUpdate {

    @Schema(description = "奖励配置")
    @NotEmpty(message = BaseErrorConsts.ACTV_REWARD_RULE_UPDATE_NOT_EMPTY)
    private List<ActivityRewardConfigUpdate> rewardConfigUpdates;

}
