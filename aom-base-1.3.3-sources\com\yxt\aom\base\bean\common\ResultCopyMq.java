package com.yxt.aom.base.bean.common;

import com.yxt.aom.activity.facade.bean.control.ResultCopyReq;
import com.yxt.aom.base.entity.control.ActivityObjectiveResult;
import com.yxt.aom.base.entity.control.AssessmentActivityResult;
import com.yxt.aom.base.entity.control.BaseActivityResult;
import lombok.Data;

import java.util.List;

/**
 * ResultCopyMq
 */
@Data
public class ResultCopyMq {
    private ResultCopyReq req;
    private List<BaseActivityResult> baseResults;
    private List<AssessmentActivityResult> assessmentResults;
    private List<ActivityObjectiveResult> objectiveResults;
}
