package com.yxt.aom.activity.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;
import java.util.Objects;

/**
 * The enum Check box range enum.
 */
@Getter
@RequiredArgsConstructor
public enum CheckBoxRangeEnum {

    /**
     * 全部
     */
    ALL(0, true),


    /**
     * 部分
     */
    PART(1, false),

    ;

    private final int value;

    /**
     * 是否可以排除部分
     */
    private final boolean excludeAble;

    /**
     * Of check box range enum.
     *
     * @param value the value
     * @return the check box range enum
     */
    public static CheckBoxRangeEnum of(Integer value) {
        return Arrays.stream(values()).filter(e -> Objects.equals(value, e.value)).findAny().orElse(null);
    }
}
