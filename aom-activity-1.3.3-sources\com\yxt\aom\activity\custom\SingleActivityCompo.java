package com.yxt.aom.activity.custom;

import com.yxt.aom.activity.bean.RepeatReq;
import com.yxt.aom.activity.facade.bean.control.ResultCopyReq;
import com.yxt.aom.base.entity.arrange.ActivityArrangeItem;
import com.yxt.aom.base.entity.control.ActivityObjectiveResult;
import com.yxt.aom.base.entity.control.AssessmentActivityResult;
import com.yxt.aom.datamodel.activityresult.BaseActivityResult;
import com.yxt.aom.datamodel.common.Actor;
import com.yxt.aom.datamodel.common.TargetObject;
import com.yxt.aom.datamodel.event.ScoResultEvent;
import com.yxt.aom.datamodel.scoresult.BaseScoResult;

import java.util.List;

/**
 * 单主体活动结果 插槽
 */
public interface SingleActivityCompo {
    /**
     * 结果上报
     * @param result
     */
    <T extends BaseActivityResult> void resultRollUpCallBack(Actor actor , TargetObject targetObject, ActivityArrangeItem item, String traceId,T result);

    /**
     * 指派重学
     */
    void activityRepeat(com.yxt.aom.base.entity.control.BaseActivityResult baseActivityResult, RepeatReq req);

    /**
     * demo复制时由活动方更新老的活动结果数据中相关id字段
     * (比如AssessmentActivityResult的ext，ActivityObjectiveResult的ext+objectiveId+objectiveModeId等)
     *
     * @param bean                 the bean
     * @param oldAssessmentResults the old assessment results
     * @param oldObjectiveResults  the old objective results
     */
    default void updateIdFields4DemoCopy(ResultCopyReq bean, List<AssessmentActivityResult> oldAssessmentResults,
            List<ActivityObjectiveResult> oldObjectiveResults) {
        // do nothing
    }

    /**
     * sco结果插槽，因aom不清楚具体的sco结果子类型，所以使用BaseScoResult作为参数类型
     * 注意：如果需要使用具体的sco结果子类型，需要在实现类中用messageBody做json转换
     *
     * @param event 基于BaseScoResult
     * @param messageBody 消息体 message，根据需要做json转换
     */
    void scoResultCallBack(ScoResultEvent<BaseScoResult> event , String messageBody);
}
