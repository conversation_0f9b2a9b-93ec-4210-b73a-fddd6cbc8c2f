package com.yxt.aom.base.bean.common;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


@Data
public class ActivityTitle4Create {


    @Schema(description = "机构id")
    private String orgId;

    @Schema(description = "活动/项目id")
    private String actvId;

    @Schema(description = "标签类型 (0 岗位能力 1 岗位任务,项目标签)",example = "0",requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer tagType;

    @Schema(description = "标签名称",example = "myname")
    private String tagName;

    @Schema(description = "标签id")
    private String tagId;

    @Schema(description = "排序号")
    private Integer orderIndex;

    @Schema(description = "标签组名称",example = "myname")
    private String tagGroupName;

    @Schema(description = "标签组id")
    private String tagGroupId;
}
