package com.yxt.aom.base.bean.part.cycle;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class AomActivityCancelAutoReleasePostProcessorThird {

    @Schema(description = "活动Id", example = "活动Id")
    private String actvId;

    @Schema(description = "操作人")
    private String optUserId;

    @Schema(description = "机构Id")
    private String orgId;

    @Schema(description = "活动注册id")
    private String regId;

}
