package com.yxt.aom.base.bean.part;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class RefreshMemberTimeBean {

    private String orgId;

    private String optUserId;

    private String actvId;

    private Long partId;

    private Date startTime;

    private Date endTime;

    private Date currentDate;

    private Integer startDayOffset;

    private Integer endDayOffset;
    /**
     * 时间模式(0-固定, 1-相对)
     */
    private Integer timeModel;

    private String regId;

}
