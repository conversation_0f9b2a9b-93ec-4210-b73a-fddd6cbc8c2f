package com.yxt.aom.base.bean.control;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
@SuperBuilder
public class MemberProcessToSp {
    /**
     * 机构Id
     */
    private String orgId;
    /**
     * 用户id
     */
    private String userId;
    /**
     * 活动id
     */
    private String actvId;

    /**
     * 活动完成状态：0-未完成，1-进行中，2-已完成
     * 目前全部任务完成 算整个活动完成
     */
    @TableField("actv_completed_status")
    private Integer actvCompletedStatus;

    /**
     * 活动完成进度
     */
    @TableField("actv_completed_rate")
    private BigDecimal actvCompletedRate;

    /**
     * 活动完成时间
     */
    @TableField("actv_completed_time")
    private Date actvCompletedTime;

    /**
     * 所有任务数量
     */
    @TableField("all_task_count")
    private Integer allTaskCount;

    /**
     * 所有任务完成数量
     */
    @TableField("all_task_completed_count")
    private Integer allTaskCompletedCount;

    /**
     * 所有任务完成率
     */
    @TableField("all_task_completed_rate")
    private BigDecimal allTaskCompletedRate;

    /**
     * 完成所有任务的时间点
     */
    @TableField("all_task_completed_time")
    private Date allTaskCompletedTime;

    /**
     * 必修任务数
     */
    @TableField("required_task_count")
    private Integer requiredTaskCount;

    /**
     * 必修任务完成数
     */
    @TableField("required_task_completed_count")
    private Integer requiredTaskCompletedCount;

    /**
     * 必修任务完成率
     */
    @TableField("required_task_completed_rate")
    private BigDecimal requiredTaskCompletedRate;

    /**
     * 完成所有必修任务的时间点
     */
    @TableField("required_task_completed_time")
    private Date requiredTaskCompletedTime;

}
