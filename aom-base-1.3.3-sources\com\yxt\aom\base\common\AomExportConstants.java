package com.yxt.aom.base.common;

import java.util.Collections;
import java.util.List;

import static com.google.common.collect.Lists.newArrayList;
import static com.yxt.aom.base.common.AomPropConstants.ALL_PROGRESS;
import static com.yxt.aom.base.common.AomPropConstants.DEPT_NAME;
import static com.yxt.aom.base.common.AomPropConstants.FORMAL_NAME;
import static com.yxt.aom.base.common.AomPropConstants.LOWER_FULL_NAME;
import static com.yxt.aom.base.common.AomPropConstants.LOWER_USER_NAME;
import static com.yxt.aom.base.common.AomPropConstants.POSITION_LIST;
import static com.yxt.aom.base.common.AomPropConstants.REQUIRED_PROGRESS;

/**
 * The type Export constants.
 *
 * <AUTHOR>
 */
public final class AomExportConstants {

    private AomExportConstants() {
        throw new UnsupportedOperationException();
    }

    public static final String HEADER = "header";
    public static final String EXCEL_SUFFIX = ".xlsx";
    public static final String ORIG_SUFFIX = "--Orig";

    /**
     * 学员列表数据导出
     */
    public static final String EXPORT_GROUP_MEMBER_MODE_CODE = "groupmember";

    /**
     * 学员异常数据导出
     */
    public static final String EXPORT_ERROR_STUDENTS_TMP_CODE = "studentserror";

    public static final String USER_NAME_IS_NULL = "apis.aom.stu.import.username.is.null";
    public static final String USER_IS_NOT_ALLOW = "apis.aom.stu.import.user.is.allow";
    public static final String USER_DELETED = "apis.aom.stu.import.user.deleted";
    public static final String USER_DISABLE = "apis.aom.stu.import.user.disable";
    public static final String GROUP_NOT_EXIST = "apis.aom.stu.import.group.not.exist";
    public static final String GROUP_TOO_MANY = "apis.aom.stu.import.group.too.many";
    public static final String TOO_MANY_USER = "apis.aom.stu.import.too.many.user";
    public static final String GROUP_LEADER_NOT_CHANGE = "apis.aom.stu.import.group.leader.not.change";
    /**
     * The constant SUB_PROJECT_ERROR.
     */
    public static final String SUB_PROJECT_ERROR = "apis.aom.stu.import.sub.project.error";
    /**
     * The constant USER_FORMAL_ERROR.
     */
    public static final String USER_FORMAL_ERROR = "apis.aom.stu.import.formal.error";
    /**
     * The constant USER_LEADER_ERROR.
     */
    public static final String USER_LEADER_ERROR = "apis.aom.stu.import.leader.error";
    /**
     * The constant USER_LEADER_EXIST.
     */
    public static final String USER_LEADER_EXIST = "apis.aom.stu.import.leader.exist";

    /**
     * The constant USER_LEADER_OVERFLOW.
     */
    public static final String USER_LEADER_OVERFLOW = "apis.aom.stu.import.leader.overflow";

    public static final String EXPORT_ERROR_STUDENTS_FILENAME = "apis.aom.export.header.error.student.filename";

    public static final String EXPORT_ERROR_STUDENTS_HEADER = "apis.aom.export.header.error.student.";

    public static final String EXPORT_ERROR_STUDENTS_HEADER_NAME = "apis.export.header.error.student.header.name";

    public static final String DOWNLOAD_FUNCTIONNAME_PREFIX = "apis.aom.export.functionName.";

    /**
     * 概览进度导出相关
     */
    public static final String EXPORT_RPT_SCHEDULE_MEMBER_HEADER_PREFIX = "apis.aom.export.schedule.member.header.";
    public static final String EXPORT_RPT_SCHEDULE_MEMBER_SHEET_NAME = "apis.aom.export.schedule.member.sheet.name";
    public static final String EXPORT_RPT_SCHEDULE_DEPT_HEADER_PREFIX = "apis.aom.export.schedule.dept.header.";
    public static final String EXPORT_RPT_SCHEDULE_DEPT_SHEET_NAME = "apis.aom.export.schedule.dept.sheet.name";
    public static final String EXPORT_RPT_SCHEDULE_PERIOD_NAME = "apis.aom.export.schedule.periodName";

    public static final String EXPORT_RPT_SCHEDULE_MODE_CODE = "rptSchedule";

    /**
     * 小组学员导入报错keys
     */
    public static final List<String> GROUP_MEMBER_IMPORT_KEYS = Collections.unmodifiableList(
            newArrayList(USER_NAME_IS_NULL, USER_IS_NOT_ALLOW, USER_DELETED, USER_DISABLE, TOO_MANY_USER,
                    SUB_PROJECT_ERROR));

    /**
     * 小组学员导出头部
     */
    public static final List<String> GROUP_MEMBER_EXPORT_HEADERS = Collections.unmodifiableList(
            newArrayList(LOWER_FULL_NAME, LOWER_USER_NAME, FORMAL_NAME, POSITION_LIST, DEPT_NAME, ALL_PROGRESS,
                    REQUIRED_PROGRESS));

    public static final String GROUP_MEMBER_TITLE_NAME = "apis.aom.export.header.group.member.titlename";
}
