package com.yxt.aom.base.bean.part;

import com.yxt.common.annotation.timezone.DateFormatField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;


@Data
public class ActivityMember4StartEndTime {

    @Schema(description = "学员活动开始时间")
    @DateFormatField(isDate = true)
    private Date startTime;

    @Schema(description = "学员活动结束时间")
    @DateFormatField(isDate = true)
    private Date endTime;
}
