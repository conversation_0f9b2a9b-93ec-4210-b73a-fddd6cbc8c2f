package com.yxt.aom.base.bean.common;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class ActivityCopyOption {
    @Schema(description = "流程安排", example = "true")
    private Boolean taskFlag = true;
    @Schema(description = "积分&学分", example = "true")
    private Boolean scoreFlag = true;
    @Schema(description = "证书", example = "true")
    private Boolean certsetFlag = true;
    @Schema(description = "项目学员", example = "true")
    private Boolean memberFlag = false;
    @Schema(description = "消息模板", example = "true")
    private Boolean msgFlag = true;
    @Schema(description = "带教", example = "true")
    private Boolean ojtFlag = false;
    @Schema(description = "报名", example = "true")
    private Boolean enrcFlag = false;
}
