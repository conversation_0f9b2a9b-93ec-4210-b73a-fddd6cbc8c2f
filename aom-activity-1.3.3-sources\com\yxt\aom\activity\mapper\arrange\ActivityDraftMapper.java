package com.yxt.aom.activity.mapper.arrange;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yxt.aom.activity.entity.arrange.ActivityDraft;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * ActivityDraftMapper
 */
@Mapper
public interface ActivityDraftMapper extends BaseMapper<ActivityDraft> {
    /**
     * Batch insert.
     *
     * @param list the list
     */
    void batchInsert(@Param("list") Collection<ActivityDraft> list);

    /**
     * Batch update.
     *
     * @param list the list
     */
    void batchUpdate(@Param("list") List<ActivityDraft> list);

    /**
     * List draft 4 remove list.
     *
     * @param orgId          the org id
     * @param actvId         the actv id
     * @param refRegId       the ref reg id
     * @param excludeItemIds the exclude item ids
     * @return the list
     */
    List<ActivityDraft> listDraft4Remove(@Param("orgId") String orgId, @Param("actvId") String actvId,
                                         @Param("refRegId") String refRegId, @Param("excludeItemIds") Collection<Long> excludeItemIds);

    /**
     * Delete by ids.
     *
     * @param orgId      the org id
     * @param operatorId the operator id
     * @param ids        the ids
     */
    void deleteByIds(@Param("orgId") String orgId, @Param("operatorId") String operatorId, @Param("ids") Collection<Long> ids);

    /**
     * batchUpdateFormData
     */
    void updateFormData(@Param("orgId") String orgId, @Param("actvId") String actvId, @Param("refId") String refId, @Param("formData") String formData);

    /**
     * List draft 4 actv id list.
     *
     * @param orgId     the org id
     * @param actvId    the actv id
     * @param refRegId  the ref reg id
     * @return the list
     */
    List<ActivityDraft> listDraft4ActvId(@Param("orgId") String orgId, @Param("actvId") String actvId, @Param("refRegId") String refRegId);
}
