package com.yxt.aom.activity.mq;

import com.alibaba.fastjson.JSON;
import com.yxt.aom.base.bean.part.PartMemberTimeUpdateBean;
import com.yxt.aom.base.bean.part.RefreshMemberTime4ThirdBean;
import com.yxt.aom.base.common.AomConstants;
import com.yxt.aom.base.mapper.part.ActivityParticipationMemberMapper;
import com.yxt.aom.common.config.AomDataSourceTypeHolder;
import com.yxt.aom.common.config.AomDbProperties;
import com.yxt.dbrouter.core.toolkit.DBRouteHolder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

import static com.yxt.aom.base.common.AomMqConstants.TOPIC_ACTIVITY_MEMBER_TIME_REFRESH_THIRD;

/**
 * 添加学员
 */
@Slf4j
@RequiredArgsConstructor
@Component
@ConditionalOnProperty(name = "aom.rocketmq.consumer-enabled", havingValue = "true", matchIfMissing = true)
@RocketMQMessageListener(consumerGroup = AomConstants.MQ_GROUP_PREFIX
        + TOPIC_ACTIVITY_MEMBER_TIME_REFRESH_THIRD, topic = TOPIC_ACTIVITY_MEMBER_TIME_REFRESH_THIRD, consumeMode = ConsumeMode.CONCURRENTLY, consumeThreadNumber = 3)

public class ActivityTimeCycleChangeMemberUpdateListener implements RocketMQListener<String> {
    private final AomDbProperties aomDbProperties;
    private final ActivityParticipationMemberMapper activityParticipationMemberMapper;


    @Override
    public void onMessage(String message) {
        log.info("ActivityTimeCycleChangeMemberUpdateListener, message: {}", message);
        try {
            if (StringUtils.isEmpty(message)) {
                return;
            }
            RefreshMemberTime4ThirdBean msgBean = JSON.parseObject(message, RefreshMemberTime4ThirdBean.class);
            if (msgBean == null) {
                log.warn("ActivityTimeCycleChangeMemberUpdateListener invalid message: {}", message);
                return;
            }
            String regId = msgBean.getRegId();
            Map<String, String> dsmap = aomDbProperties.getDsmap();
            if (!dsmap.containsKey(regId)) {
                return;
            }

            List<PartMemberTimeUpdateBean> list = msgBean.getList();
            if (CollectionUtils.isNotEmpty(list)) {
                AomDataSourceTypeHolder.set(regId);
                DBRouteHolder.push(msgBean.getOrgId());
                activityParticipationMemberMapper.batchUpdateMemberStartEndTime(list);
            }
        } catch (Exception ex) {
            log.error("ActivityTimeCycleChangeMemberUpdateListener message failed. message: {} , err: ", message, ex);
        }
    }
}
