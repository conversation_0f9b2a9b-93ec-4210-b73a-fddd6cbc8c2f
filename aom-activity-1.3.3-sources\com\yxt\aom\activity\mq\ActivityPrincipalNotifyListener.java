package com.yxt.aom.activity.mq;

import com.alibaba.fastjson.JSON;
import com.yxt.aom.base.bean.common.ActivityRelationBean;
import com.yxt.aom.base.common.AomConstants;
import com.yxt.aom.base.service.part.impl.ActivityParticipationRelationService;
import com.yxt.aom.common.config.AomDataSourceTypeHolder;
import com.yxt.aom.common.config.AomDbProperties;
import com.yxt.dbrouter.core.toolkit.DBRouteHolder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.Map;

import static com.yxt.aom.base.common.AomMqConstants.TOPIC_SC_PRINCIPAL_NOTICE;
import static com.yxt.common.Constants.INT_2;

/**
 * <AUTHOR>
 * @since 2025/2/10
 */
@Slf4j
@RequiredArgsConstructor
@Component
@ConditionalOnProperty(name = "aom.rocketmq.consumer-enabled", havingValue = "true", matchIfMissing = true)
@RocketMQMessageListener(consumerGroup = AomConstants.MQ_GROUP_PREFIX
        + TOPIC_SC_PRINCIPAL_NOTICE, topic = TOPIC_SC_PRINCIPAL_NOTICE, consumeMode = ConsumeMode.CONCURRENTLY, consumeThreadNumber = INT_2)
public class ActivityPrincipalNotifyListener implements RocketMQListener<String> {

    private final AomDbProperties aomDbProperties;
    private final ActivityParticipationRelationService activityParticipationRelationService;

    @Override
    public void onMessage(String message) {
        log.info("收到消息 mes={}", message);
        if (StringUtils.isBlank(message)) {
            return;
        }
        try {
            ActivityRelationBean actvRelationBean = JSON.parseObject(message, ActivityRelationBean.class);
            if (actvRelationBean == null) {
                log.warn("ActivityPrincipalNotifyListener invalid message: {}", message);
                return;
            }
            String regId = actvRelationBean.getRegId();
            Map<String, String> dsmap = aomDbProperties.getDsmap();
            if (!dsmap.containsKey(regId)) {
                log.info("负责人通知未找到对应的regId信息 msg={} dsmap={}", message, JSON.toJSONString(dsmap));
                return;
            }
            AomDataSourceTypeHolder.set(regId);
            DBRouteHolder.push(actvRelationBean.getOrgId());
            activityParticipationRelationService.handlePrincipalChange(actvRelationBean.getOrgId(), actvRelationBean);
        } catch (Exception e) {
            log.error("ActivityPrincipalNotifyListener failed. message: {} , err: ", message, e);
        }
    }

}
