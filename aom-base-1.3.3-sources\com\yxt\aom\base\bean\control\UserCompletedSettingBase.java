package com.yxt.aom.base.bean.control;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
public class UserCompletedSettingBase {

    @Schema(description = "项目完成进度")
    private BigDecimal completeProcessRate;

    @Schema(description = "必修任务数")
    private Integer requiredTaskCount;
    @Schema(description = "必修完成数")
    private Integer requiredCompleteCount;
    @Schema(description = "必修完成进度")
    private BigDecimal requiredProcessRate;

    @Schema(description = "总任务数")
    private Integer allTaskCount;
    @Schema(description = "总任务完成数")
    private Integer completeAllTaskCount;
    @Schema(description = "总完成进度")
    private BigDecimal allProcessRate;

    @Schema(description = "任务总数 ", example = "10")
    private int taskCount;
    @Schema(description = "任务完成数量 ", example = "5")
    private int completeCount;

    //public void calCountBySetting(UserCompletedSettingBase data, List<Period> periods,
    //    ProjectCompStandardBean standardBean){
    //    data.setRequiredProcessRate(new BigDecimal(INT_100).multiply(data.getRequiredProcessRate()).setScale(INT_2, RoundingMode.HALF_UP));
    //    if (isOldStandard(standardBean)) {
    //        calCount4OldStandard(data, standardBean);
    //        return;
    //    }
    //    data.setCompleteProcessRate(new BigDecimal(INT_100).multiply(data.getCompleteProcessRate()).setScale(INT_2, RoundingMode.HALF_UP));
    //    //2-完成项目内指定任务数量
    //    if (Objects.equals(standardBean.getStandard(), ProjectStudyStandardEnum.MINIMUM_TASK.getType())) {
    //        ProjectCompStandardBean.Task taskStandard = standardBean.getTask();
    //        Integer countComTask = INT_0;
    //        Integer comReqTask = INT_0;
    //        Integer comElectiveTask = INT_0;
    //        Integer countElectiveTask = INT_0;
    //        Integer tmpRequiredTaskCount = data.getRequiredTaskCount();
    //        if (isAllRequired(taskStandard)) {
    //            //所有必修任务数
    //            countComTask = countComTask + tmpRequiredTaskCount;
    //            comReqTask += data.getRequiredCompleteCount();
    //        } else {
    //            countComTask = countComTask + standardBean.getTask().getRequired();
    //            comReqTask = Math.min(countComTask, data.getRequiredCompleteCount());
    //        }
    //        if (isAllElective(taskStandard)) {
    //            //所有选修任务数
    //            countElectiveTask = countElectiveTask + (data.getAllTaskCount() - tmpRequiredTaskCount);
    //            comElectiveTask += (data.getCompleteAllTaskCount()-data.getRequiredCompleteCount());
    //        } else {
    //            countElectiveTask = countElectiveTask + standardBean.getTask().getElective();
    //            comElectiveTask = Math.min(countElectiveTask,
    //                data.getCompleteAllTaskCount() - data.getRequiredCompleteCount());
    //        }
    //        data.setCountSettingStr(String.valueOf(countComTask + countElectiveTask));
    //        data.setCompleteCountStr(String.valueOf(comReqTask + comElectiveTask));
    //    }
    //
    //    //3-完成项目内指定阶段数量
    //    if (Objects.equals(standardBean.getStandard(), ProjectStudyStandardEnum.MINIMUM_PERIOD.getType())) {
    //        //完成项目内最少阶段数
    //        Integer periodCount = CollectionUtils.size(periods);
    //        if (!ProjectCompStandardBean.isAll(standardBean.getPeriod())) {
    //            periodCount = standardBean.getPeriod();
    //        }
    //        data.setCountSettingStr(String.valueOf(periodCount));
    //        int count = data.getCompletePeriodCount() == null ? INT_0 : data.getCompletePeriodCount();
    //        data.setCompleteCountStr(String.valueOf(Math.min(periodCount, count)));
    //    }
    //    //4-获得项目内指定学分
    //    if (Objects.equals(standardBean.getStandard(), ProjectStudyStandardEnum.MINIMUM_STUDY_SCORE.getType())) {
    //        BigDecimal studyScore = standardBean.getStudyScore() == null ? new BigDecimal(INT_0) : standardBean.getStudyScore();
    //        data.setCountSettingStr(String.valueOf(standardBean.getStudyScore().setScale(2, RoundingMode.HALF_UP)));
    //        BigDecimal newCompleteStudyScore = data.getCompleteStudyScore() == null ? new BigDecimal(INT_0) : data.getCompleteStudyScore();
    //        if (studyScore.compareTo(newCompleteStudyScore) < INT_0) {
    //            newCompleteStudyScore = studyScore;
    //        }
    //        data.setCompleteCountStr(String.valueOf(newCompleteStudyScore.setScale(2,RoundingMode.HALF_UP)));
    //    }
    //}
    //
    //private void calCount4OldStandard(UserCompletedSettingBase data, ProjectCompStandardBean standardBean) {
    //    data.setCompleteCountStr(String.valueOf(data.getCompleteCount()));
    //    data.setCountSettingStr(String.valueOf(data.getTaskCount()));
    //    if (standardBean == null || Objects.equals(standardBean.getStandard(), ProjectStudyStandardEnum.COMPLETE_REQUIRED_TASK.getType())) {
    //        data.setCompleteProcessRate(data.getRequiredProcessRate());
    //    }
    //    if (standardBean != null && Objects.equals(standardBean.getStandard(), ProjectStudyStandardEnum.COMPLETE_ALL_TASK.getType())) {
    //        data.setCompleteProcessRate(new BigDecimal(INT_100).multiply(data.getAllProcessRate()).setScale(INT_2, RoundingMode.HALF_UP));
    //    }
    //}
    //
    //private boolean isAllElective(ProjectCompStandardBean.Task taskStandard) {
    //    return Objects.isNull(taskStandard) || ProjectCompStandardBean.isAll(taskStandard.getElective());
    //}
    //
    //private boolean isAllRequired(ProjectCompStandardBean.Task taskStandard) {
    //    return Objects.isNull(taskStandard) || ProjectCompStandardBean.isAll(taskStandard.getRequired());
    //}
    //
    //private boolean isOldStandard(ProjectCompStandardBean standardBean) {
    //    return standardBean == null || Objects.equals(standardBean.getStandard(),
    //        ProjectStudyStandardEnum.COMPLETE_REQUIRED_TASK.getType()) || Objects.equals(standardBean.getStandard(),
    //        ProjectStudyStandardEnum.COMPLETE_ALL_TASK.getType());
    //}
}
