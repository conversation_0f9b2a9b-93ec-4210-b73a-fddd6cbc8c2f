package com.yxt.aom.base.bean.part;

import com.yxt.common.annotation.timezone.DateFormatField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class ActivityDelayStudyReq {

    @Schema(description = "学员id")
    private String userId;

    @Schema(description = "开始时间")
    @DateFormatField(isDate = true)
    private Date startTime;

    @Schema(description = "结束时间")
    @DateFormatField(isDate = true)
    private Date endTime;

    @Schema(description = "活动/项目id")
    private String actvId;

    @Schema(description = "参与id")
    private Long participationId;

    @Schema(description = "UACD注册表中定义Id，example=proj_o2o")
    private String regId;
}
