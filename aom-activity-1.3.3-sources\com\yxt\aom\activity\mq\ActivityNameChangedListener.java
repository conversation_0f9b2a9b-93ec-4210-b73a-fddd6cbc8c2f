package com.yxt.aom.activity.mq;

import com.alibaba.fastjson.JSON;
import com.yxt.aom.base.bean.common.ActivityNameChangedMq;
import com.yxt.aom.base.common.AomConstants;
import com.yxt.aom.base.component.common.AomDbComponent;
import com.yxt.aom.base.enums.ActivityTypeEnum;
import com.yxt.aom.base.mapper.common.ActivitySourceMapper;
import com.yxt.dbrouter.core.toolkit.DBRouteHolder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.Set;

import static com.yxt.aom.base.common.AomMqConstants.TOPIC_AOM_ACTIVITY_NAMECHANGED;

/**
 * 活动名称变更
 */
@Slf4j
@RequiredArgsConstructor
@Component
@ConditionalOnProperty(name = "aom.rocketmq.consumer-enabled", havingValue = "true", matchIfMissing = true)
@RocketMQMessageListener(consumerGroup = AomConstants.MQ_GROUP_PREFIX
        + TOPIC_AOM_ACTIVITY_NAMECHANGED, topic = TOPIC_AOM_ACTIVITY_NAMECHANGED, consumeMode = ConsumeMode.CONCURRENTLY, consumeThreadNumber = 1)
public class ActivityNameChangedListener implements RocketMQListener<String> {
    private final ActivitySourceMapper sourceMapper;
    private final AomDbComponent dbComponent;

    @Override
    public void onMessage(String message) {
        log.info("ActivityNameChangedListener, message: {}", message);
        if (StringUtils.isEmpty(message)) {
            return;
        }
        try {
            final ActivityNameChangedMq bean = JSON.parseObject(message, ActivityNameChangedMq.class);
            if (bean == null) {
                log.warn("ActivityNameChangedListener invalid message: {}", message);
                return;
            }
            Set<String> refRegIds = bean.getRefRegIds();
            if (CollectionUtils.isEmpty(refRegIds)) {
                return;
            }
            dbComponent.invokeByAllDataSource(ActivityTypeEnum.ACTV, regId -> {
                if (refRegIds.contains(regId)) {
                    updateSourceName(bean, regId);
                }
            });
        } catch (Exception ex) {
            log.error("ActivityNameChangedListener message failed. message: {} , err: ", message, ex);
        }
    }

    private void updateSourceName(ActivityNameChangedMq bean, String regId) {
        try {
            DBRouteHolder.push(bean.getOrgId());
            sourceMapper.updateSourceName(bean.getOrgId(), bean.getActvId(), bean.getActvName());
        } catch (Exception ex) {
            log.error("ActivityNameChangedListener message for {} failed. message: {} , err: ", regId,
                    JSON.toJSONString(bean), ex);
        } finally {
            DBRouteHolder.poll();
        }
    }
}
