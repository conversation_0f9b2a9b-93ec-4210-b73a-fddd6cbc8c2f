package com.yxt.aom.activity.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.Data;

import java.util.Collections;
import java.util.List;

import static com.yxt.common.Constants.INT_0;
import static com.yxt.common.Constants.INT_1;

/**
 * <AUTHOR>
 * @description 页面选择部分和选择全部的checkbox基础类
 */
@Data
public class CheckBoxBaseBean {

    @Schema(description = "范围：0-全部，1-部分", example = "0")
    @Max(INT_1)
    @Min(INT_0)
    private Integer range = INT_0;

    @Schema(description = "学员id，或协同作业中的小组ID，range=1时必传")
    private List<String> userIds = Collections.emptyList();

}
