package com.yxt.aom.base.bean.part.title;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class AomTitleResp {

    @Schema(description = "称号id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long titleId;

    @Schema(description = "称号名称")
    private String titleName;

    @Schema(description = "创建人id")
    private String createUserId;

    @Schema(description = "创建时间")
    private Date createTime;

}
