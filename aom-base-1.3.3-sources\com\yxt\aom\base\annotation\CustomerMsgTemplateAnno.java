package com.yxt.aom.base.annotation;

import com.yxt.aom.base.enums.DesignerMessageEnum;
import org.intellij.lang.annotations.Language;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * AOM设计器自定义消息模板标注
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface CustomerMsgTemplateAnno {
    /**
     * 活动id表达式
     */
    @Language("SpEL") String value() default "";

    /**
     * 活动对象表达式（value和activity任传一个，优先取activity的值）
     */
    @Language("SpEL") String activity() default "";

    /**
     * 消息模板枚举类
     */
    DesignerMessageEnum[] messageEnum();

    /**
     * 机构id
     */
    @Language("SpEL") String orgId() default "";
}
