package com.yxt.aom.base.bean.common;

import com.yxt.aom.base.enums.ActivitySaveasTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * The type Activity 4 saveas.
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(name = "活动或活动模板另存为新的活动或活动模板请求体")
public class Activity4Saveas extends ActivityCopyOption {
    @Schema(description = "操作类型(1-项目保存为模板, 2-使用项目复制项目, 3-使用模板创建项目, 4-使用模板复制模板)", example = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    private int opeateType;

    @Schema(description = "项目/模板名称", example = "项目1(模板)", requiredMode = Schema.RequiredMode.REQUIRED)
    private String name;

    @Schema(description = "活动/项目的具体类型(UACD注册表中定义)", example = "proj_o2o", requiredMode = Schema.RequiredMode.REQUIRED)
    private String actvRegId;

    @Schema(description = "分类id", example = "1849276367002220000")
    private String categoryId;

    /**
     * Need copy task boolean.
     *
     * @return the boolean
     */
    public boolean needCopyTask() {
        return (null != this.getTaskFlag() && this.getTaskFlag()) || useTemplate();
    }

    /**
     * Need copy certset boolean.
     *
     * @return the boolean
     */
    public boolean needCopyCertset() {
        if (!needCopyTask()) {
            return false;
        }
        return (null != this.getCertsetFlag() && this.getCertsetFlag()) || useTemplate();
    }

    /**
     * Need copy score boolean.
     *
     * @return the boolean
     */
    public boolean needCopyScore() {
        if (!needCopyTask()) {
            return false;
        }
        return (null != this.getScoreFlag() && this.getScoreFlag()) || useTemplate();
    }

    /**
     * Need copy member boolean.
     *
     * @return the boolean
     */
    public boolean needCopyMember() {
        if (1 == this.getOpeateType() || useTemplate()) {
            return false;
        }
        return null != this.getMemberFlag() && this.getMemberFlag();
    }

    /**
     * Need copy msg boolean.
     *
     * @return the boolean
     */
    public boolean needCopyMsg() {
        return (null != this.getMsgFlag() && this.getMsgFlag()) || useTemplate();
    }

    /**
     * Need copy ojt boolean.
     *
     * @return the boolean
     */
    public boolean needCopyOjt() {
        return (null != this.getOjtFlag() && this.getOjtFlag()) || useTemplate();
    }

    private boolean useTemplate() {
        return ActivitySaveasTypeEnum.TEM2PRO.getValue() == this.getOpeateType()
                || ActivitySaveasTypeEnum.TEM2TEM.getValue() == this.getOpeateType();
    }
}
