package com.yxt.aom.base.bean.arrange;

import com.yxt.common.annotation.timezone.DateFormatField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
@Schema(name = "活动大纲")
public class ActivityArrangeTree<T> {
    /**
     * 有无阶段/章节(0-无, 1-有)
     */
    @Schema(description = "有无阶段/章节(0-无, 1-有)")
    private Integer hasFolder = 0;

    /**
     * 时间模式(0-固定, 1-相对)
     */
    @Schema(description = "时间模式(0-固定, 1-相对)", example = "0")
    private Integer timeModel;

    /**
     * 固定开始时间
     */
    @Schema(description = "固定开始时间", example = "2024-10-25 10:56:47")
    @DateFormatField(isDate = true)
    private Date startTime;

    /**
     * 固定截止时间
     */
    @Schema(description = "固定截止时间", example = "2024-11-25 10:56:47")
    @DateFormatField(isDate = true)
    private Date endTime;

    /**
     * 相对开始天数
     */
    @Schema(description = "相对开始天数", example = "0")
    private Integer startDayOffset;

    /**
     * 相对截止天数
     */
    @Schema(description = "相对截止天数", example = "7")
    private Integer endDayOffset;

    /**
     * 大纲树
     */
    @Schema(description = "大纲树")
    @DateFormatField(isobj = true)
    private List<T> datas = new ArrayList<>();
}
