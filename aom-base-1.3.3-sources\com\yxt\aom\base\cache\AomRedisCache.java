package com.yxt.aom.base.cache;

import com.alibaba.fastjson.JSONObject;
import com.google.common.annotations.Beta;
import com.google.common.base.Preconditions;
import com.google.common.base.Splitter;
import io.lettuce.core.protocol.CommandType;
import io.vavr.Function3;
import io.vavr.control.Try;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.dao.InvalidDataAccessApiUsageException;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.connection.RedisSetCommands;
import org.springframework.data.redis.connection.zset.DefaultTuple;
import org.springframework.data.redis.connection.zset.Tuple;
import org.springframework.data.redis.core.BoundHashOperations;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.TimeoutUtils;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.util.Assert;
import org.springframework.util.ClassUtils;
import redis.clients.jedis.Protocol.Command;
import redis.clients.jedis.util.SafeEncoder;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static org.apache.commons.lang3.StringUtils.isNotBlank;

/**
 * The type Redis cache.
 *
 * <AUTHOR>
 */
@Slf4j
public class AomRedisCache implements InitializingBean {

    public static final long INVALID_EXPIRE = -1L;

    private static final String UNLINK_COMMAND = "UNLINK";
    private static final String ENTRYKEY = "entryKey";
    private static final String KEY = "key";

    public static final String ERR_NO_SUCH_KEY = "ERR no such key";

    private static final boolean JEDIS =
        ClassUtils.isPresent(
            "redis.clients.jedis.Protocol", Thread.currentThread().getContextClassLoader());

    private static final boolean LETTUCE =
        ClassUtils.isPresent(
            "com.lambdaworks.redis.protocol.CommandType",
            Thread.currentThread().getContextClassLoader());

    private int redisMajor = 0;

    @Getter
    @Setter
    private RedisTemplate<String, ?> redisTemplate;

    /**
     * Instantiates a new Redis cache.
     */
    public AomRedisCache() {
        // NOSONAR
    }

    @Override
    public void afterPropertiesSet() {
        assertRedisTemplateRequired();

        try {
            String version = getRedisVersion();
            List<String> segments =
                Splitter.on(".").omitEmptyStrings().trimResults().splitToList(version);
            if (!segments.isEmpty()) {
                this.redisMajor = Integer.parseInt(segments.get(0));
            }
        } catch (Exception ex) {
            log.error("getRedisVersion failed", ex);
        }
    }

    /**
     * Get v.
     *
     * @param <V>   the type parameter
     * @param key   the key
     * @param clazz the clazz
     * @return the v
     */
    public <V> V get(String key, Class<V> clazz) {
        return get(key, INVALID_EXPIRE, TimeUnit.SECONDS, null, clazz);
    }

    /**
     * Get v.
     *
     * @param <V>      the type parameter
     * @param key      the key
     * @param supplier the supplier
     * @param clazz    the clazz
     * @return the v
     */
    public <V> V get(String key, Supplier<V> supplier, Class<V> clazz) {
        return get(key, INVALID_EXPIRE, TimeUnit.SECONDS, supplier, clazz);
    }

    /**
     * Get v.
     *
     * @param <V>      the type parameter
     * @param key      the key
     * @param expire   the expire
     * @param timeUnit the time unit
     * @param supplier the supplier
     * @param clazz    the clazz
     * @return the v
     */
    public <V> V get(String key, long expire, TimeUnit timeUnit, Supplier<V> supplier, Class<V> clazz) {
        final byte[] rawKey = rawKey(key);

        final V value;
        byte[] rawValue = redisTemplate.execute(connection -> connection.stringCommands().get(rawKey), true);
        final boolean valueIsEmpty = rawValue == null || rawValue.length == 0;
        if (valueIsEmpty && supplier != null) {
            if ((value = supplier.get()) != null && !doPut(rawKey, value, expire, timeUnit)) {
                log.error("Redis put fail, Key: {}", key);
            }
        } else {
            value = deserializeValue(rawValue, clazz);
        }
        return value;
    }

    /**
     * Put boolean.
     *
     * @param <V>   the type parameter
     * @param key   the key
     * @param value the value
     * @return the boolean
     */
    public <V> boolean put(String key, V value) {
        return put(key, value, INVALID_EXPIRE, TimeUnit.SECONDS);
    }

    /**
     * Put boolean.
     *
     * @param <V>    the type parameter
     * @param key    the key
     * @param value  the value
     * @param expire the expire
     * @return the boolean
     */
    public <V> boolean put(String key, V value, long expire) {
        final byte[] rawKey = rawKey(key);
        return doPut(rawKey, value, expire, TimeUnit.SECONDS);
    }

    /**
     * Put boolean.
     *
     * @param <V>      the type parameter
     * @param key      the key
     * @param value    the value
     * @param expire   the expire
     * @param timeUnit the time unit
     * @return the boolean
     */
    public <V> boolean put(String key, V value, long expire, TimeUnit timeUnit) {
        final byte[] rawKey = rawKey(key);
        return doPut(rawKey, value, expire, timeUnit);
    }

    /**
     * Put boolean.
     *
     * @param <V>      the type parameter
     * @param cacheKey the cache key
     * @param value    the value
     * @return the boolean
     */
    public <V> boolean put(AomCacheKey cacheKey, V value) {
        final byte[] rawKey = rawKey(cacheKey.ofString());
        return doPut(rawKey, value, cacheKey.expire(), cacheKey.timeUnit());
    }

    /**
     * Exists boolean.
     *
     * @param key the key
     * @return the boolean
     */
    public boolean exists(String key) {
        return exists(key, false);
    }

    /**
     * Exists boolean.
     *
     * @param key           the key
     * @param ignoreFailure the ignore failure
     * @return the boolean
     */
    public boolean exists(String key, boolean ignoreFailure) {
        try {
            Boolean exists = redisTemplate.hasKey(key);
            return nonNull(exists);
        } catch (Exception ex) {
            return wrapException("hasKey", false, ex, ignoreFailure);
        }
    }

    /**
     * Expire boolean.
     *
     * @param key    the key
     * @param expire the expire
     * @return the boolean
     */
    public boolean expire(String key, long expire) {
        return expire(key, expire, TimeUnit.SECONDS);
    }

    /**
     * Expire boolean.
     *
     * @param key      the key
     * @param expire   the expire
     * @param timeUnit the time unit
     * @return the boolean
     */
    public boolean expire(String key, long expire, TimeUnit timeUnit) {
        Preconditions.checkArgument(StringUtils.isNotBlank(key), KEY);
        Preconditions.checkNotNull(timeUnit, "timeUnit");

        if (expire <= 0) {
            return false;
        }

        try {
            Boolean val = redisTemplate.expire(key, expire, timeUnit);
            return nonNull(val);
        } catch (Exception ex) {
            log.error("Redis, expire key failed.", ex);
            return false;
        }
    }

    /**
     * Delete boolean.
     *
     * @param key the key
     * @return the boolean
     */
    public boolean delete(String key) {
        Preconditions.checkArgument(StringUtils.isNotBlank(key), KEY);

        Boolean val = redisTemplate.delete(key);
        return nonNull(val);
    }

    /**
     * L push long.
     *
     * @param <V>      the type parameter
     * @param key      the key
     * @param value    the value
     * @param expire   the expire
     * @param timeUnit the time unit
     * @return the long
     */
    public <V> long lPush(String key, V value, long expire, TimeUnit timeUnit) {
        final byte[] rawKey = rawKey(key);
        final byte[] rawValue = rawValue(value);

        Long val = redisTemplate.execute(connection -> connection.listCommands().lPush(rawKey, rawValue), true);
        long retVal = nonNull(val);
        if (retVal > 0) {
            expire(key, expire, timeUnit);
        }
        return retVal;
    }

    /**
     * R push long.
     *
     * @param <V>      the type parameter
     * @param key      the key
     * @param value    the value
     * @param expire   the expire
     * @param timeUnit the time unit
     * @return the long
     */
    public <V> long rPush(String key, V value, long expire, TimeUnit timeUnit) {
        return rPush(key, value, expire, timeUnit, false);
    }

    /**
     * R push long.
     *
     * @param <V>           the type parameter
     * @param key           the key
     * @param value         the value
     * @param expire        the expire
     * @param timeUnit      the time unit
     * @param ignoreFailure the ignore failure
     * @return the long
     */
    public <V> long rPush(String key, V value, long expire, TimeUnit timeUnit, boolean ignoreFailure) {
        try {
            final byte[] rawKey = rawKey(key);
            final byte[] rawValue = rawValue(value);

            Long val = redisTemplate.execute(connection -> connection.listCommands().rPush(rawKey, rawValue), true);
            long retVal = nonNull(val);
            if (retVal > 0L) {
                expire(key, expire, timeUnit);
            }
            return retVal;
        } catch (Exception ex) {
            return wrapException("rPush", 0L, ex, ignoreFailure);
        }
    }

    /**
     * L range list.
     *
     * @param <V>   the type parameter
     * @param key   the key
     * @param start the start
     * @param end   the end
     * @param clazz the clazz
     * @return the list
     */
    public <V> List<V> lRange(String key, long start, long end, Class<V> clazz) {
        return lRange(key, start, end, clazz, false);
    }

    /**
     * L range list.
     *
     * @param <V>           the type parameter
     * @param key           the key
     * @param start         the start
     * @param end           the end
     * @param clazz         the clazz
     * @param ignoreFailure the ignore failure
     * @return the list
     */
    public <V> List<V> lRange(String key, long start, long end, Class<V> clazz, boolean ignoreFailure) {
        Preconditions.checkArgument(StringUtils.isNotBlank(key), KEY);

        try {
            final byte[] rawKey = rawKey(key);
            final List<byte[]> values = redisTemplate
                .execute(connection -> connection.listCommands().lRange(rawKey, start, end), true);

            if (values == null || values.isEmpty()) {
                return Collections.emptyList();
            }

            return values.stream()
                .map(value -> deserializeValue(value, clazz))
                .collect(Collectors.toList());
        } catch (Exception ex) {
            return wrapException("lRange", Collections.emptyList(), ex, ignoreFailure);
        }
    }

    /**
     * 保留指定范围内的元素 从start到end
     *
     * @param key
     * @param start
     * @param end
     */
    public void lTrim(String key, long start, long end) {
        try {
            final byte[] rawKey = rawKey(key);
            redisTemplate.execute((RedisConnection connection) -> {
                connection.listCommands().lTrim(rawKey, start, end);
                return null;
            }, true);
        } catch (Exception ex) {
            log.error("lTrim ex: ", ex);
        }
    }

    /**
     * 删除了list中指定元素
     *
     * @param key
     * @param value
     */
    public void lRemove(String key, Object value) {
        try {
            final byte[] rawKey = rawKey(key);
            final byte[] rawValue = rawValue(value);
            redisTemplate.execute((RedisConnection connection) -> {
                connection.listCommands().lRem(rawKey, 0, rawValue);
                return null;
            }, true);
        } catch (Exception ex) {
            log.error("lRemove ex: ", ex);
        }
    }

    /**
     * 批量删除了list中指定元素
     *
     * @param key
     * @param values
     */
    public <V> void lRemoves(String key, List<V> values) {
        try {
            final byte[] rawKey = rawKey(key);
            redisTemplate.executePipelined((RedisConnection connection) -> {
                for (Object value : values) {
                    final byte[] rawValue = rawValue(value);
                    // 删除列表中所有匹配的元素
                    connection.listCommands().lRem(rawKey, 0, rawValue);
                }
                return null;
            });
        } catch (Exception ex) {
            log.error("lRemoves ex: ", ex);
        }
    }

    /**
     * S is member boolean.
     *
     * @param <V>   the type parameter
     * @param key   the key
     * @param value the value
     * @return the boolean
     */
    public <V> boolean sIsMember(String key, V value) {
        Preconditions.checkArgument(StringUtils.isNotBlank(key), KEY);

        Boolean val = redisTemplate.opsForSet().isMember(key, value);
        return nonNull(val);
    }

    /**
     * S is member boolean.
     *
     * @param <V>           the type parameter
     * @param key           the key
     * @param value         the value
     * @param ignoreFailure the ignore failure
     * @return the boolean
     */
    public <V> boolean sIsMember(String key, V value, boolean ignoreFailure) {
        Preconditions.checkArgument(StringUtils.isNotBlank(key), KEY);
        try {
            Boolean val = redisTemplate.opsForSet().isMember(key, value);
            return nonNull(val);
        } catch (Exception ex) {
            return wrapException("sIsMember", false, ex, ignoreFailure);
        }
    }

    /**
     * S add long.
     *
     * @param <V>    the type parameter
     * @param key    the key
     * @param values the values
     * @return the long
     */
    public <V> long sAdd(String key, List<V> values) {
        return doExecute(key, values, RedisSetCommands::sAdd);
    }

    /**
     * S add long.
     *
     * @param <V>           the type parameter
     * @param key           the key
     * @param values        the values
     * @param ignoreFailure the ignore failure
     * @return the long
     */
    public <V> long sAdd(String key, List<V> values, boolean ignoreFailure) {
        try {
            return sAdd(key, values);
        } catch (Exception ex) {
            return wrapException("sAdd", 0L, ex, ignoreFailure);
        }
    }

    /**
     * S remove long.
     *
     * @param <V>   the type parameter
     * @param key   the key
     * @param value the value
     * @return the long
     */
    public <V> long sRemove(String key, V value) {
        Preconditions.checkArgument(StringUtils.isNotBlank(key), KEY);
        Preconditions.checkNotNull(value, KEY);

        Long val = redisTemplate.opsForSet().remove(key, value);
        return nonNull(val);
    }

    /**
     * S remove long.
     *
     * @param <V>    the type parameter
     * @param key    the key
     * @param values the values
     * @return the long
     */
    public <V> long sRemove(String key, List<V> values) {
        return doExecute(key, values, RedisSetCommands::sRem);
    }

    /**
     * S members set.
     *
     * @param <V>   the type parameter
     * @param key   the key
     * @param clazz the clazz
     * @return the set
     */
    public <V> Set<V> sMembers(String key, Class<V> clazz) {
        Preconditions.checkArgument(StringUtils.isNotBlank(key), KEY);

        final Set<byte[]> set = redisTemplate.execute(connection -> connection.setCommands().sMembers(rawKey(key)), true);
        if (set == null || set.isEmpty()) {
            return Collections.emptySet();
        }

        return set.stream()
            .map(value -> deserializeValue(value, clazz))
            .collect(Collectors.toSet());
    }

    /**
     * S size long.
     *
     * @param key the key
     * @return the long
     */
    public long sSize(String key) {
        Long size = redisTemplate.opsForSet().size(key);
        return nonNull(size);
    }

    /**
     * Increment long.
     *
     * @param key   the key
     * @param delta the delta
     * @return the long
     */
    public long increment(String key, long delta) {
        Long val = redisTemplate.opsForValue().increment(key, delta);
        return nonNull(val);
    }

    /**
     * Gets map ops.
     *
     * @param <V> the type parameter
     * @param key the key
     * @return the map ops
     */
    public <V> BoundHashOperations<String, String, V> getMapOps(String key) {
        Preconditions.checkArgument(isNotBlank(key), KEY);
        return getRedisTemplate().boundHashOps(key);
    }

    /**
     * Map get optional.
     *
     * @param <V>      the type parameter
     * @param key      the key
     * @param entryKey the entry key
     * @return the optional
     */
    public <V> Optional<V> mapGet(String key, String entryKey) {
        Preconditions.checkArgument(isNotBlank(key), KEY);
        Preconditions.checkArgument(isNotBlank(entryKey), ENTRYKEY);
        BoundHashOperations<String, String, V> mapOps = getMapOps(key);
        return Optional.ofNullable(mapOps.get(entryKey));
    }

    public Object mapGetHash(String key, String entryKey) {
        Preconditions.checkArgument(isNotBlank(key), KEY);
        Preconditions.checkArgument(isNotBlank(entryKey), ENTRYKEY);
        return redisTemplate.opsForHash().get(key,entryKey);
    }

    /**
     * Map get optional.
     *
     * @param <V>      the type parameter
     * @param key      the key
     * @param expire   the expire
     * @param timeUnit the time unit
     * @param entryKey the entry key
     * @param supplier the supplier
     * @return the optional
     */
    public <V> Optional<V> mapGet(
        String key, long expire, TimeUnit timeUnit, String entryKey, Supplier<V> supplier) {
        Preconditions.checkArgument(isNotBlank(key), KEY);
        Preconditions.checkArgument(isNotBlank(entryKey), ENTRYKEY);
        Preconditions.checkNotNull(supplier, "supplier");

        BoundHashOperations<String, String, V> mapOps = getMapOps(key);
        V value = mapOps.get(entryKey);

        if (value == null) {
            final V val = (value = supplier.get());
            if (val != null) {
                mapOps.put(entryKey, val);
                if (expire > 0) {
                    mapOps.expire(expire, timeUnit);
                }
            }
        }
        return Optional.ofNullable(value);
    }

    /**
     * Map put.
     *
     * @param <V>        the type parameter
     * @param key        the key
     * @param expire     the expire
     * @param timeUnit   the time unit
     * @param entryKey   the entry key
     * @param entryValue the entry value
     */
    public <V> void mapPut(
        String key, long expire, TimeUnit timeUnit, String entryKey, V entryValue) {
        Preconditions.checkArgument(isNotBlank(key), KEY);
        Preconditions.checkArgument(isNotBlank(entryKey), ENTRYKEY);

        BoundHashOperations<String, String, V> mapOps = getMapOps(key);
        mapOps.put(entryKey, entryValue);
        if (expire > 0) {
            mapOps.expire(expire, timeUnit);
        }
    }

    /**
     * Map put all.
     *
     * @param <V>      the type parameter
     * @param key      the key
     * @param expire   the expire
     * @param timeUnit the time unit
     * @param map      the map
     */
    public <V> void mapPutAll(String key, long expire, TimeUnit timeUnit, Map<String, V> map) {
        Preconditions.checkArgument(isNotBlank(key), KEY);

        BoundHashOperations<String, String, V> mapOps = getMapOps(key);
        mapOps.putAll(MapUtils.emptyIfNull(map));
        if (expire > 0) {
            mapOps.expire(expire, timeUnit);
        }
    }

    public <V> void mapPutAllHash(String key, long expire, TimeUnit timeUnit, Map<String, V> map) {
        Preconditions.checkArgument(isNotBlank(key), KEY);
        redisTemplate.opsForHash().putAll(key,map);
        if (expire > 0) {
            expire(key,expire,timeUnit);
        }
    }

    /**
     * Remove map.
     *
     * @param key the key
     */
    @Beta
    @SuppressWarnings({"rawtypes", "unchecked"})
    public void removeMap(String key) {
        Preconditions.checkArgument(isNotBlank(key), KEY);

        if (JEDIS && hasUnlinkCommand(Command.class)) {
            unlink(key);
        } else if (LETTUCE && hasUnlinkCommand(CommandType.class)) {
            unlink(key);
        } else {
            RedisSerializer keySerializer = keySerializer();
            byte[] curKey = keySerializer.serialize(key);
            byte[] newKey = keySerializer.serialize(UUID.randomUUID().toString().replace("-", ""));

            try {
                redisTemplate.execute(
                    (RedisCallback<Void>)
                        connection -> {
                            connection.keyCommands().rename(curKey, newKey);
                            return null;
                        });
                redisTemplate.execute((RedisCallback<Boolean>) connection -> connection.keyCommands().expire(newKey, 5L));
            } catch (InvalidDataAccessApiUsageException ex) {
                String message = ex.getMessage();
                // noinspection StatementWithEmptyBody
                if (isNotBlank(message) && message.startsWith(ERR_NO_SUCH_KEY)) {
                    // nothing...
                } else {
                    throw ex;
                }
            }
        }
    }

    /**
     * Unlink.
     *
     * @param key the key
     */
    @SuppressWarnings({"rawtypes", "unchecked"})
    public void unlink(String key) {
        Preconditions.checkArgument(isNotBlank(key), KEY);
        RedisSerializer keySerializer = keySerializer();
        byte[] keyBytes = keySerializer.serialize(key);
        redisTemplate.execute(
            (RedisCallback<Object>) connection -> connection.execute(UNLINK_COMMAND, keyBytes));
    }

    /**
     * Remove map entry.
     *
     * @param key      the key
     * @param entryKey the entry key
     */
    public void removeMapEntry(String key, String entryKey) {
        Preconditions.checkArgument(isNotBlank(key), KEY);
        Preconditions.checkArgument(isNotBlank(entryKey), ENTRYKEY);
        getMapOps(key).delete(entryKey);
    }

    /**
     * Key serializer redis serializer.
     *
     * @return the redis serializer
     */
    @SuppressWarnings({"rawtypes", "java:S3740"})
    protected RedisSerializer keySerializer() {
        return this.redisTemplate.getKeySerializer();
    }

    /**
     * Value serializer redis serializer.
     *
     * @return the redis serializer
     */
    @SuppressWarnings({"rawtypes", "java:S3740"})
    protected RedisSerializer valueSerializer() {
        return this.redisTemplate.getValueSerializer();
    }

    /**
     * Deserialize value v.
     *
     * @param <V>   the type parameter
     * @param value the value
     * @param clazz the clazz
     * @return the v
     */
    @SuppressWarnings("unchecked")
    protected <V> V deserializeValue(byte[] value, Class<V> clazz) {
        final RedisSerializer<?> serializer = valueSerializer();
        if (serializer == null) {
            return (V) value;
        }

        final V val = (V) serializer.deserialize(value);
        if (!clazz.equals(Object.class) && val instanceof JSONObject valJson) {
            return valJson.toJavaObject(clazz);
        }
        return val;
    }

    /**
     * Do put boolean.
     *
     * @param <V>      the type parameter
     * @param key      the key
     * @param value    the value
     * @param expire   the expire
     * @param timeUnit the time unit
     * @return the boolean
     */
    protected <V> boolean doPut(byte[] key, V value, long expire, TimeUnit timeUnit) {
        final byte[] bValue = rawValue(value);

        Boolean val = redisTemplate.execute(connection -> {
            Boolean result;
            if (expire > 0) {
                long seconds = toSeconds(expire, timeUnit);
                result = connection.stringCommands().setEx(key, seconds, bValue);
            } else {
                result = connection.stringCommands().set(key, bValue);
            }
            return result;
        }, true);

        return nonNull(val);
    }

    @SuppressWarnings("unchecked")
    private byte[] rawKey(Object key) {
        if (keySerializer() == null && key instanceof byte[] keyByte) {
            return keyByte;
        }
        return keySerializer().serialize(key);
    }

    @SuppressWarnings("unchecked")
    private byte[] rawValue(Object value) {
        if (valueSerializer() == null && value instanceof byte[] valueByte) {
            return valueByte;
        }
        return valueSerializer().serialize(value);
    }

    private <V> long doExecute(String key, List<V> values, Function3<RedisConnection, byte[], byte[][], Long> func) {
        if (values == null || values.isEmpty()) {
            return 0L;
        }

        final byte[] rawKey = rawKey(key);
        final byte[][] rawValues = values.stream()
            .map((this::rawValue))
            .toArray(byte[][]::new);

        Long val = redisTemplate.execute(connection -> func.apply(connection, rawKey, rawValues), true);
        return nonNull(val);
    }

    private <T> T nonNull(T val) {
        return Objects.requireNonNull(val, "The returned value must not be null");
    }

    private long toSeconds(long ttl, TimeUnit timeUnit) {
        return TimeoutUtils.toSeconds(ttl, timeUnit == null ? TimeUnit.SECONDS : timeUnit);
    }

    private void assertRedisTemplateRequired() {
        Assert.notNull(redisTemplate, "RedisTemplate is required");
    }

    private <R> R wrapException(String action, R defaultValue, Exception ex, boolean ignoreFailure) {
        if (ignoreFailure) {
            log.error("Redis {} failed.", action, ex);
            return defaultValue;
        }
        throw new RuntimeException(ex); // NOSONAR
    }

    private <T extends Enum<T>> boolean hasUnlinkCommand(Class<T> clazz) {
        return this.redisMajor > 3
            && Try.ofSupplier(
                () -> {
                    Enum.valueOf(clazz, UNLINK_COMMAND);
                    return true;
                })
            .getOrElse(false);
    }

    private String getRedisVersion() {
        Object info =
            getRedisTemplate()
                .execute(
                    (RedisCallback<Object>)
                        connection -> connection.execute("INFO", SafeEncoder.encode("server")));

        if (!(info instanceof byte[])) {
            return StringUtils.EMPTY;
        }

        String field = "redis_version:";
        InputStream in = new ByteArrayInputStream((byte[]) info);
        BufferedReader reader = new BufferedReader(new InputStreamReader(in));

        String line = null;
        String version;
        do {
            try {
                line = reader.readLine();
                if (isNotBlank(line) && line.startsWith(field)) {
                    version = StringUtils.trim(line.substring(field.length()));
                    log.debug("Current redis server version: {}", version);
                    return version;
                }
            } catch (Exception ex) {
                log.error("getRedisVersion failed", ex);
            }
        } while (line != null);

        log.warn("getRedisVersion failed or not found redis version");
        return StringUtils.EMPTY;
    }

    /**
     * Z set add boolean.
     *
     * @param <V>      the type parameter
     * @param zSetName the z set name
     * @param key      the key
     * @param score    the score
     * @return the boolean
     */
    public <V> Boolean zSetAdd(String zSetName, V key, double score) {
        final byte[] rawKey = rawKey(zSetName);
        final byte[] rawValue = rawValue(key);
        return redisTemplate.execute(connection -> connection.zAdd(rawKey, score, rawValue), true);
    }

    /**
     * 批量新增
     *
     * @param zSetName the z set name
     * @param map      the map
     * @return long
     */
    public Long zSetAddBatch(String zSetName, Map<String, Double> map) {
        final byte[] rawKey = rawKey(zSetName);
        Set<Tuple> set = new HashSet<>();
        map.forEach((k, v) -> {
            Tuple tuple = new DefaultTuple(rawValue(k), v);
            set.add(tuple);
        });
        return redisTemplate.execute(connection -> connection.zAdd(rawKey, set), true);
    }


    /**
     * 获取zSet指定key的数据
     *
     * @param <V>      the type parameter
     * @param zSetName the z set name
     * @param key      the key
     * @return double
     */
    public <V> Double zScore(String zSetName, V key) {
        final byte[] rawKey = rawKey(zSetName);
        final byte[] rawValue = rawValue(key);
        return redisTemplate.execute(connection -> connection.zSetCommands().zScore(rawKey, rawValue), true);
    }

    /**
     * Z set range set.
     *
     * @param zSetName the z set name
     * @param start    the start
     * @param end      the end
     * @return the set
     */
    public Set zSetRange(String zSetName, long start, long end) {
        final byte[] rawKey = rawKey(zSetName);
        return redisTemplate.execute(connection -> connection.zSetCommands().zRange(rawKey, start, end), true);
    }

    /**
     * 获取zSet所有的key和score(分数从高到低)
     *
     * @param zSetName the z set name
     * @param start    the start
     * @param end      the end
     * @return set
     */
    public Set<? extends ZSetOperations.TypedTuple<?>> zRevRangeWithScores(String zSetName, long start, // NOSONAR
        long end) {
        return redisTemplate.opsForZSet().reverseRangeWithScores(zSetName, start, end);

    }

    /**
     * Z set increment score double.
     *
     * @param <V>      the type parameter
     * @param zSetName the z set name
     * @param key      the key
     * @param delta    the delta
     * @return the double
     */
    public <V> Double zSetIncrementScore(String zSetName, V key, double delta) {
        final byte[] rawKey = rawKey(zSetName);
        final byte[] rawValue = rawValue(key);
        return redisTemplate.execute(connection -> connection.zSetCommands().zIncrBy(rawKey, delta, rawValue), true);
    }

    /**
     * 删除zSet的元素
     *
     * @param <V>      the type parameter
     * @param zSetName the z set name
     * @param key      the key
     * @return long
     */
    public <V> Long zRem(String zSetName, V key) {
        final byte[] rawKey = rawKey(zSetName);
        final byte[] rawValue = rawValue(key);
        return redisTemplate.execute(connection -> connection.zSetCommands().zRem(rawKey, rawValue), true);
    }

    /**
     * 获取zSet的size
     *
     * @param zSetName the z set name
     * @return long
     */
    public Long zSetSize(String zSetName){
        return redisTemplate.opsForZSet().size(zSetName);
    }

    /**
     * Get expire long.
     *
     * @param key      the key
     * @param timeUnit the time unit
     * @return the long
     */
    public Long getExpire(String key,TimeUnit timeUnit){
        return redisTemplate.getExpire(key,timeUnit);
    }

}
