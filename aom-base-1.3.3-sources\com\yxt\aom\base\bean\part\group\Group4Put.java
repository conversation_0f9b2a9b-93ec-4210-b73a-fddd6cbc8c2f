package com.yxt.aom.base.bean.part.group;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.google.common.collect.Lists;
import com.yxt.aom.base.common.BaseErrorConsts;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;


/**
 * 更新小组信息
 */
@Getter
@Setter
@NoArgsConstructor
public class Group4Put {

    @Schema(description = "活动id")
    private String actvId;

    @Schema(description = "UACD注册表中定义Id")
    private String regId;

    @Schema(description = "参与id", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long participationId;

    @Schema(description = "小组id")
    @JsonSerialize(using = ToStringSerializer.class)
    @NotNull(message = BaseErrorConsts.GROUP_ID_IS_NULL)
    private long id;

    @Schema(description = "小组名称", example = "123", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = BaseErrorConsts.GROUP_NAME_BLANK)
    @Size(max = 50, message = BaseErrorConsts.GROUP_NAME_SIZE)
    private String name;

    @Schema(description = "小组图标", example = "https://.....", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @Size(max = 500, message = BaseErrorConsts.GROUP_ICON_SIZE)
    private String groupIcon;

    @Schema(description = "小组组长id", example = "id3", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String leaderId;

    @Schema(description = "小组辅导员ids", example = "array")
    @Size(max = 30, message = BaseErrorConsts.GROUP_INSTRUCTOR_SIZE)
    private List<String> userIds = Lists.newArrayList();

}
