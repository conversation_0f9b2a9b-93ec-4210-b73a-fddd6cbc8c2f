package com.yxt.aom.activity.service.trace;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yxt.aom.activity.bean.RepeatReq;
import com.yxt.aom.activity.bean.SubTaskRepeatResp;
import com.yxt.aom.activity.bean.TaskVerifyRepeatResp;
import com.yxt.aom.activity.custom.SingleActivityCompo;
import com.yxt.aom.activity.enums.CheckBoxRangeEnum;
import com.yxt.aom.activity.service.rollup.impl.ResultRollUpService;
import com.yxt.aom.base.common.BaseErrorConsts;
import com.yxt.aom.base.entity.common.Activity;
import com.yxt.aom.base.entity.control.ActivityObjectiveResult;
import com.yxt.aom.base.entity.control.AssessmentActivityResult;
import com.yxt.aom.base.entity.control.BaseActivityResult;
import com.yxt.aom.base.entity.control.SubActivityObjectiveResult;
import com.yxt.aom.base.entity.control.SubAssessmentActivityResult;
import com.yxt.aom.base.entity.control.SubBaseActivityResult;
import com.yxt.aom.base.enums.ActvResultStatusEnum;
import com.yxt.aom.base.enums.AomActivityStatusEnum;
import com.yxt.aom.base.enums.ResultRepeatFlagEnum;
import com.yxt.aom.base.manager.common.ActivityManager;
import com.yxt.aom.base.mapper.arrange.ActivityArrangeItemMapper;
import com.yxt.aom.base.mapper.control.ActivityObjectiveResultMapper;
import com.yxt.aom.base.mapper.control.AssessmentActivityResultMapper;
import com.yxt.aom.base.mapper.control.BaseActivityResultMapper;
import com.yxt.aom.base.mapper.control.SubActivityObjectiveResultMapper;
import com.yxt.aom.base.mapper.control.SubAssessmentActivityResultMapper;
import com.yxt.aom.base.mapper.control.SubBaseActivityResultMapper;
import com.yxt.aom.base.mapper.part.ActivityParticipationMemberMapper;
import com.yxt.aom.common.util.AomBeanNameUtils;
import com.yxt.aom.datamodel.common.ActionEnum;
import com.yxt.aom.datamodel.common.Actor;
import com.yxt.aom.datamodel.common.TargetObject;
import com.yxt.common.enums.YesOrNo;
import com.yxt.common.exception.ApiException;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.Validate;
import com.yxt.leaf.service.SnowflakeKeyGenerator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.yxt.aom.activity.common.AomActvApiError.ACTIVITY_NOT_ING;
import static com.yxt.aom.activity.common.AomActvApiError.CHECKBOX_RANGE_INVALID;
import static com.yxt.aom.activity.common.AomActvApiError.SELECT_STUDENT_FIRST;
import static com.yxt.common.Constants.INT_0;
import static com.yxt.common.Constants.INT_1;
import static com.yxt.common.Constants.INT_1000;
import static com.yxt.common.Constants.INT_2;

@Service
@Slf4j
@RequiredArgsConstructor
public class ActivityRepeatService {

    private final ActivityArrangeItemMapper arrangeItemMapper;
    private final BaseActivityResultMapper baseActivityResultMapper;
    private final AssessmentActivityResultMapper assessmentActivityResultMapper;
    private final SubAssessmentActivityResultMapper subAssessmentActivityResultMapper;
    private final SubBaseActivityResultMapper subBaseActivityResultMapper;
    private final ActivityObjectiveResultMapper activityObjectiveResultMapper;
    private final SubActivityObjectiveResultMapper subActivityObjectiveResultMapper;
    private final ResultRollUpService resultRollUpService;
    private final SnowflakeKeyGenerator snowflakeKeyGenerator;
    private final ActivityManager activityManager;
    private final ActivityParticipationMemberMapper activityParticipationMemberMapper;

    private SubTaskRepeatResp repeat(String orgId, RepeatReq bean) {

        log.info("重复指派开始: {}", JSON.toJSONString(bean));
        CheckBoxRangeEnum range = CheckBoxRangeEnum.of(bean.getRange());
        Validate.isNotNull(range, CHECKBOX_RANGE_INVALID);

        Set<String> userIds = Sets.newHashSet();
        //判断是指定部分学员还是全部
        if (range == CheckBoxRangeEnum.PART) {
            //指定部分学员指派重学
            Validate.isTrue(CollectionUtils.isNotEmpty(bean.getUserIds()), SELECT_STUDENT_FIRST);
            userIds.addAll(bean.getUserIds());
        } else {
            //指定查询条件的全部学员指派重学
            //需要知道任务类型

            //查询项目下的学员数据
            userIds = activityParticipationMemberMapper.listExistPartUserId(orgId,bean.getActvId(),null,null,null);

        }
        Validate.isTrue(CollectionUtils.isNotEmpty(userIds), SELECT_STUDENT_FIRST);
        bean.setUserIds(Lists.newArrayList(userIds));
        return assessmentRepeat(orgId, bean);
    }

    public SubTaskRepeatResp assessmentRepeat( String orgId, RepeatReq bean) {

        Activity activity = activityManager.requireAvailableActivity(orgId, bean.getActvId());

        List<String> userIds = bean.getUserIds();

        SubTaskRepeatResp resp = new SubTaskRepeatResp();
        List<List<String>> partition = Lists.partition(userIds, INT_1000);

        for (List<String> partUserIds : partition) {
            //查询已存在的学习结果 base activity result  sub base 处理
            List<BaseActivityResult> baseActivityResults = baseActivityResultMapper.getByActvIdsAndUserIds(orgId, Lists.newArrayList(activity.getId()), partUserIds);
            baseActivityResults = baseActivityResults.stream()
                    .filter(res -> Objects.equals(res.getResultStatus(), ActvResultStatusEnum.COMPLETED.getValue())).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(baseActivityResults)) {
                continue;
            }

            List<SubBaseActivityResult> copySubBaseResults = BeanCopierUtil.convertList(baseActivityResults, BaseActivityResult.class, SubBaseActivityResult.class);

            //获取order index
            List<SubBaseActivityResult> subBaseActivityResults = subBaseActivityResultMapper.getByActvIdsAndUserIds(orgId, Lists.newArrayList(activity.getId()), partUserIds);

            Map<String, SubBaseActivityResult> subBaseActivityResultMap = subBaseActivityResults.stream()
                    .collect(Collectors.toMap(
                            result -> result.getActvId() + result.getUserId(),
                            Function.identity(), (v1, v2) -> v1
                    ));

            List<AssessmentActivityResult> assessmentActivityResults = assessmentActivityResultMapper.getByActvIdsAndUserIds(orgId, Lists.newArrayList(activity.getId()), partUserIds);
            Map<Long, AssessmentActivityResult> assessmentActivityResultMap = assessmentActivityResults.stream().collect(Collectors.toMap(
                    sub -> sub.getBaseActvResultId(),
                    Function.identity(), (v1, v2) -> v1
            ));
            //objective result 处理
            List<ActivityObjectiveResult> activityObjectiveResults = activityObjectiveResultMapper.getByActvIdsAndUserIds(orgId, Lists.newArrayList(activity.getId()), partUserIds);
            Map<Long, List<ActivityObjectiveResult>> activityObjectiveResultMap = activityObjectiveResults.stream()
                    .collect(Collectors.groupingBy(ActivityObjectiveResult::getBaseActvResultId));
            List<SubBaseActivityResult> insertSubBaseResults = Lists.newArrayList();
            List<SubAssessmentActivityResult> insertSubAssessmentResults = Lists.newArrayList();
            List<SubActivityObjectiveResult> insertSubActivityObjectResults = Lists.newArrayList();

            copySubBaseResults.forEach(result -> {
                AssessmentActivityResult assessmentActivityResult = assessmentActivityResultMap.get(result.getId());
                if(assessmentActivityResult == null) {
                    log.warn("subActivityRepeat  base result: {} assessmentActivityResult is null" ,JSON.toJSONString(result));
                    throw new ApiException(BaseErrorConsts.MISSING_PARAMS);
                }
                SubBaseActivityResult subBaseActivityResult = subBaseActivityResultMap.get(result.getActvId() + result.getUserId());
                result.setOrderIndex(subBaseActivityResult == null ? INT_1 : subBaseActivityResult.getOrderIndex() + INT_1);

                //第一次生成sub task result 保存完成的数据
                if (Objects.equals(result.getOrderIndex(),INT_1)) {
                    insertSubBaseResults.add(result);
                    SubAssessmentActivityResult initSubAssessmentResult = new SubAssessmentActivityResult();
                    BeanCopierUtil.copy(assessmentActivityResult,initSubAssessmentResult);
                    insertSubAssessmentResults.add(initSubAssessmentResult);
                    List<ActivityObjectiveResult> activityObjectiveResults1 = activityObjectiveResultMap.get(result.getId());
                    List<SubActivityObjectiveResult> subActivityObjectiveResults = BeanCopierUtil.convertList(activityObjectiveResults1, ActivityObjectiveResult.class, SubActivityObjectiveResult.class);
                    insertSubActivityObjectResults.addAll(subActivityObjectiveResults);
                }

                SubBaseActivityResult subBaseActivityResult1 = new SubBaseActivityResult();
                BeanCopierUtil.copy(result, subBaseActivityResult1);
                subBaseActivityResult1.setId(snowflakeKeyGenerator.generateKey());
                subBaseActivityResult1.setOrderIndex(Objects.equals(result.getOrderIndex(),INT_1) ? INT_2 : result.getOrderIndex() + INT_1);
                subBaseActivityResult1.setResultStatus(INT_0);
                subBaseActivityResult1.setCompletedTime(null);
                subBaseActivityResult1.setStartTime(null);
                subBaseActivityResult1.setLastStudyTime(null);
                subBaseActivityResult1.setHandCompleted(INT_0);
                insertSubBaseResults.add(subBaseActivityResult1);
                if(assessmentActivityResult != null) {
                    SubAssessmentActivityResult subAssessmentActivityResult = new SubAssessmentActivityResult();
                    BeanCopierUtil.copy(assessmentActivityResult,subAssessmentActivityResult);
                    subAssessmentActivityResult.setId(snowflakeKeyGenerator.generateKey());
                    subAssessmentActivityResult.setBaseActvResultId(subBaseActivityResult1.getId());
                    subAssessmentActivityResult.setResultRepeatFlag(ResultRepeatFlagEnum.REPEAT.getValue());
                    subAssessmentActivityResult.setRepeatCount(subAssessmentActivityResult.getRepeatCount() + INT_1);
                    subAssessmentActivityResult.setPassed(YesOrNo.NO.getValue());
                    subAssessmentActivityResult.setTargetStatus(INT_0);
                    subAssessmentActivityResult.setScore(null);
                    subAssessmentActivityResult.setTotalScore(null);
                    insertSubAssessmentResults.add(subAssessmentActivityResult);
                    //设置批次id
                    assessmentActivityResult.setSubTaskResultId(subAssessmentActivityResult.getId());
                }
            });

            //复制到sub表
            subBaseActivityResultMapper.batchInsertSubBaseActivityResult(insertSubBaseResults);
            subAssessmentActivityResultMapper.batchInsert(insertSubAssessmentResults);
            if(CollectionUtils.isNotEmpty(insertSubActivityObjectResults)) {
                subActivityObjectiveResultMapper.batchInsert(insertSubActivityObjectResults);
            }

            //roll up
            baseActivityResults.forEach(result -> {
                result.setResultStatus(INT_0);
                result.setCompletedTime(null);
                result.setStartTime(null);
                result.setLastStudyTime(null);
                Actor actor = new Actor();
                actor.setUserId(result.getUserId());
                TargetObject targetObject = new TargetObject();
                targetObject.setTargetId(result.getActvId());
                targetObject.setTargetType(bean.getRegType());
                targetObject.setSourceId(bean.getSourceId());
                targetObject.setSourceType(bean.getSourceType());

                com.yxt.aom.datamodel.activityresult.AssessmentActivityResult generated =
                        new com.yxt.aom.datamodel.activityresult.AssessmentActivityResult();
                BeanCopierUtil.copy(result,generated,false);

                AssessmentActivityResult assessmentActivityResult = assessmentActivityResultMap.get(result.getId());

                generated.setResultRepeatFlag(ResultRepeatFlagEnum.REPEAT.getValue());
                generated.setRepeatCount(assessmentActivityResult == null ? INT_1 : assessmentActivityResult.getRepeatCount() + INT_1);
                generated.setScore(null);
                generated.setPassed(null);
                generated.setTargetStatus(INT_0);
                generated.setTotalScore(null);
                generated.setBatchId(assessmentActivityResult.getSubTaskResultId().toString());

                resultRollUpService.rollUpAssessmentActivityResult(orgId, ActionEnum.REPEAT,actor,targetObject,generated);
                SingleActivityCompo singleActivityCompo = AomBeanNameUtils.getCustomBean(SingleActivityCompo.class,
                        bean.getRegType());
                if (singleActivityCompo != null) {
                    singleActivityCompo.activityRepeat(result, bean);
                    log.info("activity subActivityRepeat call back success {}", JSON.toJSONString(bean));
                }
            });


            log.info("重复指派结束new:{}", JSON.toJSONString(resp));

        }
        return resp;
    }


}
