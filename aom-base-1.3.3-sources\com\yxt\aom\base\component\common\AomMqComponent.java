package com.yxt.aom.base.component.common;

import com.alibaba.fastjson.JSON;
import com.yxt.usdk.components.rocketmq.core.RocketMQTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * The type Mq component.
 *
 * <AUTHOR>
 * <AUTHOR>
 */
@Slf4j
@Component
public class AomMqComponent {
    private final RocketMQTemplate rocketMQTemplate;

    private static final String ERROR = "Start send message, topic: {}, message: {}";

    /**
     * Instantiates a new Mq component.
     *
     * @param rocketMQTemplate the rocket mq template
     */
    @SuppressWarnings("AlibabaLowerCamelCaseVariableNaming")
    public AomMqComponent(RocketMQTemplate rocketMQTemplate) {
        this.rocketMQTemplate = rocketMQTemplate;
    }

    /**
     * syncBatchSend
     *
     * @param topic      String
     * @param stringList List
     */
    public void syncBatchSend(String topic, List<String> stringList) {
        long start = System.currentTimeMillis();

        SendResult sendResult;
        try {
            sendResult = rocketMQTemplate.syncBatchSend(topic, stringList);
        } catch (Exception e) {
            log.error("syncBatchSend error, topic: {}, listsize: {}", topic, stringList.size(), e);
            return;
        }
        log.info("syncBatchSend end, topic: {}, listsize: {}, result: {}, cost: {}", topic, stringList.size(),
            sendResult != null ? sendResult.getSendStatus() : null, System.currentTimeMillis() - start);
    }

    /**
     * Send rocket message.
     *
     * @param topic the topic
     * @param json  the json
     */
    public void sendRocketMessage(String topic, String json) {

        log.info(ERROR, topic, json);
        SendResult sendResult = rocketMQTemplate.syncSend(topic, json);

        if (log.isInfoEnabled()) {
            log.info("Send message end, topic: {}, message: {}, result: {}", topic, json,
                JSON.toJSONString(sendResult));
        }
    }

    /**
     * Send rocket message.
     *
     * @param <T>   the type parameter
     * @param topic the topic
     * @param obj   the obj
     */
    public <T> void sendRocketMessage(String topic, T obj) {
        String json = JSON.toJSONString(obj);
        log.info(ERROR, topic, json);
        rocketMQTemplate.syncSend(topic, json);
        if (log.isInfoEnabled()) {
            log.info("Send message end, topic: {}, message: {}", topic, json);
        }
    }


    /**
     * 顺序发送消息
     *
     * @param topic   the topic
     * @param json    the json
     * @param hashKey the hash key
     */
    public void sendRocketMessageOrderly(String topic, String json, Object hashKey) {
        log.info(ERROR, topic, json);
        rocketMQTemplate.syncSendOrderly(topic, json, hashKey);
    }

    /**
     * Send rocket message delay.
     *
     * @param topic      the topic
     * @param json       the json
     * @param delayLevel the delay level
     */
    public void sendRocketMessageDelay(String topic, String json, int delayLevel) {
        rocketMQTemplate.syncDelaySend(topic, json, delayLevel);
        if (log.isInfoEnabled()) {
            log.info("Send delay message end, topic: {}, message: {}", topic, json);
        }
    }
}
