package com.yxt.aom.base.bean.arrange;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ActivityArrangeItem4Stu
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(name = "学员活动大纲节点")
public class ActivityArrangeItem4Stu extends ActivityArrangeItemBase {
    /**
     * 是否最近学习的活动
     */
    @Schema(description = "是否最近学习的活动", example = "true")
    private Boolean recentLearn;
}
