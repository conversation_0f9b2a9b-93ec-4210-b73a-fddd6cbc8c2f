package com.yxt.aom.base.bean.common;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/5/27  20:04
 * @description 描述
 */
@Data
public class ActivityRewardRuleInfoMgr {

    @Schema(description = "奖励规则ID", example = "33")
    private Long id;

    @Schema(description = "业务id", example = "1849276367003750001")
    private String serviceId;

    @Schema(description = "业务类型", example = "1002")
    private Integer serviceType;

    @Schema(description = "动作类型", example = "1010")
    private Integer actionType;

    @Schema(description = "奖励规则tag", example = "1")
    private String tag;

    @Schema(description = "需达到的次数", example = "10")
    private int requireCount;
    
    @Schema(description = "奖励配置ID", example = "33")
    private Long rewardConfigId;

    @Schema(description = "积分")
    private Integer point;

    @Schema(description = "学分")
    private BigDecimal credit;

}
