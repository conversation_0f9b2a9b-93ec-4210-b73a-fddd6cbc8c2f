package com.yxt.aom.base.bean.part;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ActivityPart4Create {

    @Schema(description = "机构id")
    private String orgId;

    @Schema(description = "活动/项目id")
    private String actvId;

    @Schema(description = "参与方式 1-指派(包括手动加入和自动加入) 2-主动报名 3-指派、主动）")
    private Integer participationMethod;

    @Schema(description = "参与配置数据")
    private String configData;

    @Schema(description = "UACD注册表中定义Id example=proj_o2o")
    private String regId;
}
