package com.yxt.aom.base.bean.part.cycle;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yxt.aom.base.bean.part.ActivityStatusChangeBase;
import com.yxt.common.Constants;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class AomActivityReleaseReq extends ActivityStatusChangeBase {

    @Schema(description = "是否开启定时发布, 立即发布时应传 null", allowableValues = "0, 1, null", example = "0")
    private Integer enableAutoRelease;

    @Schema(description = "定时发布时间", example = "2020-06-29 10:00:00")
    @JsonFormat(pattern = Constants.SDF_YEAR2SECOND, timezone = Constants.STR_GMT8)
    private Date autoReleaseTime;

}
