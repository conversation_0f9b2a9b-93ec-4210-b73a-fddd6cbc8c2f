package com.yxt.aom.base.component.common;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yxt.aom.base.annotation.CustomerMsgTemplateAnno;
import com.yxt.aom.base.bean.msg.AomMsgCustomParam;
import com.yxt.aom.base.bean.part.ActvMsg4Student;
import com.yxt.aom.base.common.AomI18nConstants;
import com.yxt.aom.base.common.AomMqConstants;
import com.yxt.aom.base.common.AomMsgConstants;
import com.yxt.aom.base.custom.CustomActivitySendMsgCheckCompo;
import com.yxt.aom.base.entity.common.Activity;
import com.yxt.aom.base.entity.common.UdpUser;
import com.yxt.aom.base.entity.part.ActivityParticipationMember;
import com.yxt.aom.base.enums.AomTimeModelEnum;
import com.yxt.aom.base.enums.DesignerMessageEnum;
import com.yxt.aom.base.mapper.common.ActivityMapper;
import com.yxt.aom.base.mapper.common.AomUdpUserMapper;
import com.yxt.aom.base.mapper.part.ActivityParticipationMemberMapper;
import com.yxt.aom.base.service.AomI18nService;
import com.yxt.aom.base.service.part.impl.ActivityParticipationRelationService;
import com.yxt.aom.base.service.part.impl.ActivityParticipationService;
import com.yxt.aom.base.wrapper.MsgWrapper;
import com.yxt.aom.base.wrapper.TranslatorWrapper;
import com.yxt.aom.common.config.AomDataSourceTypeHolder;
import com.yxt.aom.common.util.AomBeanNameUtils;
import com.yxt.common.Constants;
import com.yxt.common.enums.YesOrNo;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.DateUtil;
import com.yxt.common.util.MapBuilder;
import com.yxt.msgfacade.bean.MsgBean;
import com.yxt.msgfacade.bean.req.MessageSendReq;
import jodd.util.StringPool;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.LocaleUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.FastDateFormat;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.yxt.aom.base.common.AomI18nConstants.GM_FORMAL;
import static com.yxt.aom.base.common.AomI18nConstants.GM_IN_FORMAL;
import static com.yxt.aom.base.common.AomMsgConstants.ACTIVITY_NAME;
import static com.yxt.aom.base.common.AomMsgConstants.FAILURE_REASON;
import static com.yxt.aom.base.common.AomMsgConstants.PROJECT_NAME;
import static com.yxt.aom.base.common.AomMsgConstants.RESULT;
import static com.yxt.aom.base.common.AomMsgConstants.STUDENT_TYPE;
import static com.yxt.aom.base.common.AomMsgConstants.URL;
import static com.yxt.aom.base.enums.DesignerMessageEnum.ACTIVITY_MEMBER_FORMAL_CHANGE_MSG;
import static com.yxt.aom.base.enums.DesignerMessageEnum.OMO_PROJECT_SCORE;
import static com.yxt.aom.base.enums.DesignerMessageEnum.RELEASE_PRINCIPAL_MSG;
import static com.yxt.common.Constants.INT_0;
import static com.yxt.common.Constants.INT_1;
import static com.yxt.common.Constants.INT_1000;
import static com.yxt.common.Constants.INT_5;
import static com.yxt.common.enums.YesOrNo.NO;
import static org.apache.commons.lang3.StringUtils.EMPTY;

/**
 * 消息通知相关方法
 *
 * <AUTHOR>
 * @since 2024 /11/29
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class AomNotifyComponent {
    private final ActivityMapper activityMapper;
    private final AomUdpUserMapper userMapper;
    private final ActivityParticipationMemberMapper partMemberMapper;
    private final MsgWrapper msgWrapper;
    private final TranslatorWrapper translatorWrapper;
    private final AomI18nService i18nService;
    private final AomMqComponent mqComponent;
    private final ActivityParticipationService activityParticipationService;
    private final ActivityParticipationRelationService relationService;


    /**
     * 学员类型变更 发送消息通知学员
     *
     * @param orgId      机构id
     * @param actvId     活动id
     * @param formal     类型 0-旁听 1-正式
     * @param userIds    用户id列表
     * @param operatorId 操作人id
     */
    @CustomerMsgTemplateAnno(value = "#actvId", messageEnum = ACTIVITY_MEMBER_FORMAL_CHANGE_MSG, orgId = "#orgId")
    public void sendFormalChangedMsg(String orgId, String actvId, Integer formal, List<String> userIds,
            String operatorId) {
        Activity activity = activityMapper.findById(orgId, actvId);
        // 给学员发消息 先判断活动状态
        final Map<String, String> tmplParams = Maps.newHashMap();
        tmplParams.put(ACTIVITY_NAME, activity.getActvName());
        String formalType = Objects.equals(INT_1, formal) ?
                i18nService.getI18nValue(GM_FORMAL) :
                i18nService.getI18nValue(GM_IN_FORMAL);
        tmplParams.put(STUDENT_TYPE, formalType);

        MsgBean bean = new MsgBean();
        bean.setOrgId(orgId);
        bean.setTemplateCode(ACTIVITY_MEMBER_FORMAL_CHANGE_MSG.getCode());
        bean.setTargetId(actvId);
        bean.setIsCustomTemplate(YesOrNo.YES.getValue());
        bean.setUserIds(userIds);
        bean.setParams(tmplParams);

        log.info("changeFormal  request params msgParams: {},tmplParams:{}:userId:{}", JSON.toJSONString(bean),
                JSON.toJSONString(tmplParams), operatorId);
        msgWrapper.sendTemMsg(bean);
    }

    /**
     * notifyStudentOnActvReleased
     *
     * @param orgId  String
     * @param actvId String
     * @param regId  String
     */
    public void notifyStudentOnActvReleased(String orgId, String actvId, String regId) {
        AomDataSourceTypeHolder.set(regId);
        Activity activity = activityMapper.findById(orgId, actvId);
        if (activity == null) {
            log.info("notifyStudentOnActvReleased activity not found orgId - {} actvId - {}", orgId, actvId);
            return;
        }

        List<ActivityParticipationMember> members = listActvMember(activity);
        notifyStudentOnActvReleased(activity, members);
    }

    /**
     * notifyStudentOnActvReleased
     *
     * @param activity Activity
     * @param members  List
     */
    public void notifyStudentOnActvReleased(Activity activity, List<ActivityParticipationMember> members) {
        List<MessageSendReq> msgBeanList = generateRlsMsg4Stu(activity, members);
        if (CollectionUtils.isNotEmpty(msgBeanList)) {
            msgBeanList.stream().filter(t -> CollectionUtils.isNotEmpty(t.getReceivers())).forEach(
                    msgBean -> mqComponent.sendRocketMessage(AomMqConstants.TOPIC_AOM_SEND_RELEASE_STUDENT_MSG,
                            new ActvMsg4Student(activity, msgBean, msgBean.getBatchId())));
        }
    }

    /**
     * notifyStudentOnActvDeleted
     *
     * @param orgId     String
     * @param actvId    String
     * @param regId     String
     * @param optUserId String
     */
    public void notifyStudentOnActvDeleted(String orgId, String actvId, String regId, String optUserId) {
        //todo: 判断消息开关

        AomDataSourceTypeHolder.set(regId);
        Activity activity = activityMapper.findById(orgId, actvId);
        if (activity == null) {
            log.info("notifyStudentOnActvDeleted activity not found; orgId - {} actvId - {}", orgId, actvId);
            return;
        }
        List<ActivityParticipationMember> members = listActvMember(activity);
        if (CollectionUtils.isEmpty(members)) {
            log.info("notifyStudentOnActvDeleted members is empty; orgId - {} actvId - {}", orgId, actvId);
            return;
        }
        UdpUser optUser = userMapper.findByUserId(orgId, optUserId);
        if (optUser == null) {
            log.info("notifyStudentOnActvDeleted optUser is null; orgId - {} optUserId - {}", orgId, optUserId);
            return;
        }

        List<MessageSendReq> msgBeanList = generateDelMsg4Stu(activity, members, optUser);
        if (CollectionUtils.isNotEmpty(msgBeanList)) {
            msgBeanList.stream().filter(t -> CollectionUtils.isNotEmpty(t.getReceivers())).forEach(
                    msgBean -> mqComponent.sendRocketMessage(AomMqConstants.TOPIC_AOM_SEND_DELETE_STUDENT_MSG,
                            new ActvMsg4Student(activity, msgBean, msgBean.getBatchId())));
        }
    }

    /**
     * 项目撤回-学员消息
     *
     * @param orgId     机构id
     * @param actvId    actvId
     * @param regId     regId
     * @param optUserId optUserId
     */
    public void notifyStudentOnActvWithdraw(String orgId, String actvId, String regId, String optUserId) {
        AomDataSourceTypeHolder.set(regId);
        Activity activity = activityMapper.findById(orgId, actvId);
        if (activity == null) {
            log.info("notifyStudentOnActvWithdraw activity not found; orgId - {} actvId - {}", orgId, actvId);
            return;
        }
        List<ActivityParticipationMember> members = listActvMember(activity);
        if (CollectionUtils.isEmpty(members)) {
            log.info("notifyStudentOnActvWithdraw members is empty; orgId - {} actvId - {}", orgId, actvId);
            return;
        }
        UdpUser optUser = userMapper.findByUserId(orgId, optUserId);
        if (optUser == null) {
            log.info("notifyStudentOnActvWithdraw optUser is null; orgId - {} optUserId - {}", orgId, optUserId);
            return;
        }

        List<MessageSendReq> msgBeanList = generateWithdrawMsg4Stu(activity, members, optUser);
        if (CollectionUtils.isNotEmpty(msgBeanList)) {
            msgBeanList.stream().filter(t -> CollectionUtils.isNotEmpty(t.getReceivers())).forEach(
                    msgBean -> mqComponent.sendRocketMessage(AomMqConstants.TOPIC_AOM_SEND_WITHDRAW_STUDENT_MSG,
                            new ActvMsg4Student(activity, msgBean, msgBean.getBatchId())));
        }
    }

    /**
     * sendActvReleasedMsgToStudent
     *
     * @param activity Activity
     * @param msgBean  MessageSendReq
     */
    @SuppressWarnings("unused")
    @CustomerMsgTemplateAnno(activity = "#activity", orgId = "#activity.orgId", messageEnum = {
            DesignerMessageEnum.RELEASE_STUDENT_MSG})
    public void sendActvReleasedMsgToStudent(Activity activity, MessageSendReq msgBean) {
        if (msgBean != null) {
            msgWrapper.sendMessage(msgBean);
        }
    }

    /**
     * sendActvReleasedMsgToStudent
     *
     * @param activity Activity
     * @param msgBean  MessageSendReq
     */
    @SuppressWarnings("unused")
    @CustomerMsgTemplateAnno(activity = "#activity", orgId = "#activity.orgId", messageEnum = {
            DesignerMessageEnum.DELETE_STUDENT_MSG})
    public void sendActvDeletedMsgToStudent(Activity activity, MessageSendReq msgBean) {
        if (msgBean != null) {
            msgWrapper.sendMessage(msgBean);
        }
    }

    /**
     * 项目撤回-学员消息
     *
     * @param activity activity
     * @param msgBean  msgBean
     */
    @SuppressWarnings("unused")
    @CustomerMsgTemplateAnno(activity = "#activity", orgId = "#activity.orgId", messageEnum = {
            DesignerMessageEnum.CANCEL_NOTICE})
    public void sendActvWithdraMsgToStudent(Activity activity, MessageSendReq msgBean) {
        if (msgBean != null) {
            msgWrapper.sendMessage(msgBean);
        }
    }

    /**
     * 给项目负责人发送项目自动结束提醒
     *
     * @param activity the activity
     * @param msgBean  the msg bean
     */
    @SuppressWarnings("unused")
    @CustomerMsgTemplateAnno(activity = "#activity", orgId = "#activity.orgId", messageEnum = {
            DesignerMessageEnum.AUTOMATIC_END_REMINDER_MSG})
    public void sendReminderMsg(Activity activity, MessageSendReq msgBean) {
        if (msgBean != null) {
            msgWrapper.sendMessage(msgBean);
        }
    }

    private List<MessageSendReq> generateDelMsg4Stu(Activity activity, List<ActivityParticipationMember> members,
            UdpUser optUser) {
        final LocalDateTime bizTime = LocalDateTime.now();
        final String txtId = msgWrapper.applyTxtId4Batch(activity.getOrgId(),
                DesignerMessageEnum.DELETE_STUDENT_MSG.getCode(), activity.getId(), NO.getValue(), members.size(),
                optUser.getId());
        final Map<String, String> baseParamMap = getMsgParamMap(activity.getActvName(), EMPTY);
        baseParamMap.put(AomMsgConstants.DELTIME, DateUtil.now());

        return Lists.partition(members, Constants.INT_100).stream()
                .map(subMemberList -> toDelMsg4Stu(activity, subMemberList, optUser, bizTime, txtId, baseParamMap))
                .toList();
    }

    private MessageSendReq toDelMsg4Stu(Activity activity, List<ActivityParticipationMember> members, UdpUser optUser,
            LocalDateTime bizTime, String txtId, Map<String, String> baseParamMap) {
        List<UdpUser> users = userMapper.findByUserIds(activity.getOrgId(),
                members.stream().map(ActivityParticipationMember::getUserId).collect(Collectors.toSet()));
        Map<String, UdpUser> userMap = translatorWrapper.transFullname(activity.getOrgId(), users);
        Map<String, String> optFullnameMap = getUserFullnameMap(activity.getOrgId(), optUser.getId(),
                users.stream().map(UdpUser::getLocale).collect(Collectors.toSet()), optUser.getFullname());
        MessageSendReq sendReq = initMessageSendReq(activity, bizTime, txtId, baseParamMap);
        sendReq.setTmplCode(DesignerMessageEnum.DELETE_STUDENT_MSG.getCode());
        sendReq.setReceivers(members.stream().map(member -> {
            UdpUser user = userMap.get(member.getUserId());
            if (user == null) {
                return null;
            }
            Map<String, String> paramMap = new HashMap<>(8);
            paramMap.put(AomMsgConstants.FULL_NAME, user.getFullname());
            paramMap.put(AomMsgConstants.DELUSER, optFullnameMap.getOrDefault(user.getLocale(), EMPTY));
            MessageSendReq.Receiver receiver = new MessageSendReq.Receiver();
            receiver.setRecvId(member.getUserId());
            receiver.setBizTime(bizTime);
            receiver.setParams(paramMap);
            return receiver;
        }).filter(Objects::nonNull).toList());

        return sendReq;
    }

    private MessageSendReq initMessageSendReq(Activity activity, LocalDateTime bizTime, String txtId,
            Map<String, String> baseParamMap) {
        return initMessageSendReq(activity, bizTime, txtId, baseParamMap, activity.getCreateUserId());
    }

    private MessageSendReq initMessageSendReq(Activity activity, LocalDateTime bizTime, String txtId,
            Map<String, String> baseParamMap, String senderId) {
        MessageSendReq sendReq = new MessageSendReq();
        sendReq.setOrgId(activity.getOrgId());
        sendReq.setTxId(txtId);
        sendReq.setBatchId(ApiUtil.getUuid());
        sendReq.setSubjectId(activity.getId());
        sendReq.setParams(baseParamMap);
        sendReq.setTrigTime(LocalDateTime.now());
        sendReq.setUseCustomTmpl(1);
        sendReq.setTargetId(activity.getId());
        sendReq.setBizTime(bizTime);
        sendReq.setSenderId(StringUtils.isBlank(senderId) ? "系统" : senderId);
        return sendReq;
    }

    private List<MessageSendReq> generateRlsMsg4Stu(Activity activity, List<ActivityParticipationMember> members) {
        if (activity == null || CollectionUtils.isEmpty(members)) {
            return Collections.emptyList();
        }
        final UdpUser creator = userMapper.findByUserId(activity.getOrgId(), activity.getCreateUserId());
        if (creator == null) {
            return Collections.emptyList();
        }

        //todo: 判断消息开关提供插槽业务方返回是否能发送
        CustomActivitySendMsgCheckCompo customBean = AomBeanNameUtils.getCustomBean(
                CustomActivitySendMsgCheckCompo.class, activity.getActvRegId());
        if (customBean != null) {
            AomMsgCustomParam aomMsgCustomParam = new AomMsgCustomParam();
            aomMsgCustomParam.setMsgType(INT_1);
            aomMsgCustomParam.setActvId(activity.getId());
            aomMsgCustomParam.setOrgId(activity.getOrgId());
            aomMsgCustomParam.setRegId(activity.getActvRegId());
            Boolean b = customBean.sendMsgCheck(aomMsgCustomParam);
            if (!b) {
                return Collections.emptyList();
            }
        }

        final LocalDateTime bizTime = LocalDateTime.now();
        final String txtId = msgWrapper.applyTxtId4Batch(activity.getOrgId(),
                DesignerMessageEnum.RELEASE_STUDENT_MSG.getCode(), activity.getId(), NO.getValue(), members.size(),
                activity.getCreateUserId());
        //url参数值由CustomMsgAspect自动替换为设计器中设置的
        final Map<String, String> msgParamMap = getMsgParamMap(activity.getActvName(), EMPTY);
        return Lists.partition(members, Constants.INT_100).stream()
                .map(subMemberList -> toRlsMsg4Stu(activity, subMemberList, creator, bizTime, txtId, msgParamMap))
                .toList();
    }

    private Map<String, String> getUserFullnameMap(String orgId, String userId, Set<String> localeSet,
            String defaultFullname) {
        Map<String, String> map = translatorWrapper.getUserFullnameMap(orgId, userId, localeSet);
        return localeSet.stream()
                .collect(Collectors.toMap(locale -> locale, locale -> map.getOrDefault(locale, defaultFullname)));
    }

    private MessageSendReq toRlsMsg4Stu(Activity activity, List<ActivityParticipationMember> members, UdpUser creator,
            LocalDateTime bizTime, String txtId, Map<String, String> msgParamMap) {
        List<UdpUser> users = userMapper.findByUserIds(activity.getOrgId(),
                members.stream().map(ActivityParticipationMember::getUserId).collect(Collectors.toSet()));
        Map<String, UdpUser> userMap = translatorWrapper.transFullname(activity.getOrgId(), users);
        Map<String, String> creatorFullnameMap = getUserFullnameMap(activity.getOrgId(), activity.getCreateUserId(),
                users.stream().map(UdpUser::getLocale).collect(Collectors.toSet()), creator.getFullname());
        MessageSendReq sendReq = initMessageSendReq(activity, bizTime, txtId, msgParamMap);
        sendReq.setTmplCode(DesignerMessageEnum.RELEASE_STUDENT_MSG.getCode());
        sendReq.setReceivers(members.stream()
                .map(member -> toReceiverBean(activity, member, userMap.get(member.getUserId()), creatorFullnameMap,
                        bizTime)).filter(Objects::nonNull).toList());
        return sendReq;
    }

    private Map<String, String> getMsgParamMap(String actvName, String url) {
        final Map<String, String> baseParamMap = new HashMap<>(8);
        baseParamMap.put(PROJECT_NAME, actvName);
        baseParamMap.put(URL, url);
        return baseParamMap;
    }

    private MessageSendReq.Receiver toReceiverBean(Activity activity, ActivityParticipationMember member, UdpUser user,
            Map<String, String> creatorFullnameMap, LocalDateTime bizTime) {
        if (user == null) {
            return null;
        }
        MessageSendReq.Receiver receiver = new MessageSendReq.Receiver();
        receiver.setRecvId(member.getUserId());
        receiver.setBizTime(bizTime);
        receiver.setParams(
                getReceiverParamMap(activity, member, user, creatorFullnameMap.getOrDefault(user.getLocale(), EMPTY)));
        return receiver;
    }

    private Map<String, String> getReceiverParamMap(Activity activity, ActivityParticipationMember member, UdpUser user,
            String creatorFullname) {
        final Map<String, String> paramMap = new HashMap<>(8);
        paramMap.put(AomMsgConstants.FULL_NAME, user.getFullname());
        paramMap.put(AomMsgConstants.CREATE_NAME, creatorFullname);
        paramMap.put(AomMsgConstants.FORMAL_TYPE, i18nService.getI18nValue(
                Objects.equals(member.getFormal(), 1) ? AomI18nConstants.GM_FORMAL : AomI18nConstants.GM_IN_FORMAL,
                LocaleUtils.toLocale(user.getLocale())));
        if (Objects.equals(activity.getTimeModel(), AomTimeModelEnum.FIX.getType())) {
            paramMap.put(AomMsgConstants.START_TIME_LOWER_CASE, activity.getStartTime() != null ?
                    FastDateFormat.getInstance(Constants.SDF_YEAR2MINUTE).format(activity.getStartTime()) :
                    EMPTY);
            paramMap.put(AomMsgConstants.END_TIME_LOWER_CASE, activity.getEndTime() != null ?
                    FastDateFormat.getInstance(Constants.SDF_YEAR2MINUTE).format(activity.getEndTime()) :
                    EMPTY);
        } else if (Objects.equals(activity.getTimeModel(), AomTimeModelEnum.RELATIVE.getType())) {
            paramMap.put(AomMsgConstants.START_TIME_LOWER_CASE, member.getStartTime() != null ?
                    FastDateFormat.getInstance(Constants.SDF_YEAR2MINUTE).format(member.getStartTime()) :
                    EMPTY);
            paramMap.put(AomMsgConstants.END_TIME_LOWER_CASE, member.getEndTime() != null ?
                    FastDateFormat.getInstance(Constants.SDF_YEAR2MINUTE).format(member.getEndTime()) :
                    EMPTY);
        }
        return paramMap;
    }

    private List<ActivityParticipationMember> listActvMember(Activity activity) {
        LambdaQueryWrapper<ActivityParticipationMember> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ActivityParticipationMember::getOrgId, activity.getOrgId())
                .eq(ActivityParticipationMember::getActvId, activity.getId())
                .eq(ActivityParticipationMember::getDeleted, NO.getValue());
        return partMemberMapper.selectList(queryWrapper);
    }

    private List<MessageSendReq> generateWithdrawMsg4Stu(Activity activity, List<ActivityParticipationMember> members,
            UdpUser optUser) {
        final LocalDateTime bizTime = LocalDateTime.now();
        final String txtId = msgWrapper.applyTxtId4Batch(activity.getOrgId(),
                DesignerMessageEnum.CANCEL_NOTICE.getCode(), activity.getId(), NO.getValue(), members.size(),
                optUser.getId());
        final Map<String, String> baseParamMap = getMsgParamMap(activity.getActvName(), EMPTY);

        return Lists.partition(members, Constants.INT_100).stream()
                .map(subMemberList -> toWithdrawMsg4Stu(activity, subMemberList, bizTime, txtId, baseParamMap))
                .toList();
    }

    private MessageSendReq toWithdrawMsg4Stu(Activity activity, List<ActivityParticipationMember> members,
            LocalDateTime bizTime, String txtId, Map<String, String> baseParamMap) {
        List<UdpUser> users = userMapper.findByUserIds(activity.getOrgId(),
                members.stream().map(ActivityParticipationMember::getUserId).collect(Collectors.toSet()));
        Map<String, UdpUser> userMap = translatorWrapper.transFullname(activity.getOrgId(), users);
        MessageSendReq sendReq = initMessageSendReq(activity, bizTime, txtId, baseParamMap);
        sendReq.setTmplCode(DesignerMessageEnum.CANCEL_NOTICE.getCode());
        sendReq.setReceivers(members.stream().map(member -> {
            UdpUser user = userMap.get(member.getUserId());
            if (user == null) {
                return null;
            }
            Map<String, String> paramMap = new HashMap<>(8);
            paramMap.put(AomMsgConstants.FULL_NAME, user.getFullname());
            MessageSendReq.Receiver receiver = new MessageSendReq.Receiver();
            receiver.setRecvId(member.getUserId());
            receiver.setBizTime(bizTime);
            receiver.setParams(paramMap);
            return receiver;
        }).filter(Objects::nonNull).toList());

        return sendReq;
    }

    /**
     * 创建活动群聊失败给发布人发送失败原因消息
     *
     * @param activity the activity
     * @param reason   the reason
     * @param creator  the creator
     * @return the boolean
     */
    @CustomerMsgTemplateAnno(activity = "#activity", orgId = "#activity.orgId", messageEnum = {
            DesignerMessageEnum.OMO_CHATGROUP_CREATE_DEFEATED})
    public boolean notifyCreateChatGroupFailMessage(Activity activity, String reason, String creator) {

        LocalDateTime bizTime = LocalDateTime.now();
        String txtId = msgWrapper.applyTxtId4Batch(activity.getOrgId(),
                DesignerMessageEnum.OMO_CHATGROUP_CREATE_DEFEATED.getCode(), activity.getId(), NO.getValue(), 1,
                creator);
        MessageSendReq sendReq = new MessageSendReq();
        sendReq.setOrgId(activity.getOrgId());
        sendReq.setTxId(txtId);
        sendReq.setBatchId(ApiUtil.getUuid());
        sendReq.setSubjectId(activity.getId());
        Map<String, String> params = MapBuilder.<String, String>newMapBuilder().of(PROJECT_NAME, activity.getActvName())
                .of(FAILURE_REASON, reason).map();
        sendReq.setParams(params);
        sendReq.setTmplCode(DesignerMessageEnum.OMO_CHATGROUP_CREATE_DEFEATED.getCode());
        sendReq.setTrigTime(LocalDateTime.now());
        sendReq.setUseCustomTmpl(1);
        sendReq.setTargetId(activity.getId());
        MessageSendReq.Receiver receiver = new MessageSendReq.Receiver();
        receiver.setRecvId(creator);
        receiver.setBizTime(bizTime);
        sendReq.setReceivers(List.of(receiver));
        sendReq.setBizTime(bizTime);
        sendReq.setSenderId(activity.getCreateUserId());
        try {
            log.info("notifyCreateChatGroupFailMessage  request params msgParams: {},tmplParams:{}:userId:{}",
                    JSON.toJSONString(sendReq), JSON.toJSONString(params), JSON.toJSONString(creator));
            return msgWrapper.sendMessage(sendReq);
        } catch (Exception ex) {
            log.error("notifyCreateChatGroupFailMessage failed, tplCode: {}, UserId: {}",
                    DesignerMessageEnum.OMO_CHATGROUP_CREATE_DEFEATED.getCode(), creator);
            return false;
        }
    }

    @CustomerMsgTemplateAnno(activity = "#activity", orgId = "#orgId", messageEnum = {OMO_PROJECT_SCORE})
    public void notifyMemberScoreMessage(String orgId, Activity activity, String operatorId,
            List<ActivityParticipationMember> memberList) {
        LocalDateTime bizTime = LocalDateTime.now();
        String txtId = msgWrapper.applyTxtId4Batch(orgId, OMO_PROJECT_SCORE.getCode(), activity.getId(), NO.getValue(),
                memberList.size(), operatorId);
        MessageSendReq sendReq = new MessageSendReq();
        sendReq.setTxId(txtId);
        sendReq.setOrgId(activity.getOrgId());
        sendReq.setBatchId(ApiUtil.getUuid());
        sendReq.setSubjectId(activity.getId());
        sendReq.setTmplCode(OMO_PROJECT_SCORE.getCode());
        sendReq.setTrigTime(bizTime);
        sendReq.setUseCustomTmpl(INT_1);
        sendReq.setTargetId(activity.getId());
        sendReq.setBizTime(bizTime);
        sendReq.setSenderId(operatorId);

        List<MessageSendReq.Receiver> receivers = Lists.newArrayListWithCapacity(memberList.size());

        for (ActivityParticipationMember member : memberList) {
            MessageSendReq.Receiver receiver = new MessageSendReq.Receiver();
            receiver.setRecvId(member.getUserId());
            receiver.setBizTime(bizTime);
            Map<String, String> params = Maps.newHashMap();
            params.put(PROJECT_NAME, activity.getActvName());

            BigDecimal activityScore = member.getActivityScore();
            String scoreStr = Objects.nonNull(activityScore) && activityScore.compareTo(BigDecimal.ZERO) > INT_0 ?
                    StringPool.LEFT_BRACKET + activityScore + StringPool.RIGHT_BRACKET :
                    StringPool.EMPTY;
            if (Objects.equals(member.getPassed(), INT_1)) {
                params.put(RESULT, "合格" + scoreStr);
            } else if (Objects.equals(member.getPassed(), INT_0)) {
                params.put(RESULT, "不合格" + scoreStr);
            }
            receiver.setParams(params);

            receivers.add(receiver);
        }

        List<List<MessageSendReq.Receiver>> partition = Lists.partition(receivers, INT_1000);
        partition.forEach(list -> {
            try {
                sendReq.setReceivers(list);
                log.info("notifyMemberScoreMessage request={}", JSON.toJSONString(sendReq));

                msgWrapper.sendMessage(sendReq);
            } catch (Exception ex) {
                log.error("notifyMemberScoreMessage failed request={}", JSON.toJSONString(sendReq), ex);
            }
        });

    }


    /**
     * 项目发布给负责人发送消息
     *
     * @param orgId  机构id
     * @param actvId 活动id
     * @param regId  注册id
     */
    @CustomerMsgTemplateAnno(value = "#actvId", messageEnum = RELEASE_PRINCIPAL_MSG, orgId = "#orgId")
    public void notifyPrincipalOnActvReleased(String orgId, String actvId, String regId, List<String> members) {
        log.info("notifyPrincipalOnActvReleased start orgId={}, actvId={}, members={}", orgId, actvId, CollectionUtils.size(members));
        if (CollectionUtils.isEmpty(members)) {
            log.info("notifyPrincipalOnActvReleased members is null");
            return;
        }
        AomDataSourceTypeHolder.set(regId);
        Activity activity = activityMapper.findById(orgId, actvId);
        if (activity == null) {
            log.info("notifyPrincipalOnActvReleased activity not found orgId - {} actvId - {}", orgId, actvId);
            return;
        }

        // 判断消息开关提供插槽业务方返回是否能发送
        CustomActivitySendMsgCheckCompo customBean = AomBeanNameUtils.getCustomBean(
                CustomActivitySendMsgCheckCompo.class, activity.getActvRegId());
        if (customBean != null) {
            AomMsgCustomParam aomMsgCustomParam = new AomMsgCustomParam();
            aomMsgCustomParam.setMsgType(INT_5);
            aomMsgCustomParam.setActvId(activity.getId());
            aomMsgCustomParam.setOrgId(activity.getOrgId());
            aomMsgCustomParam.setRegId(activity.getActvRegId());
            Boolean b = customBean.sendMsgCheck(aomMsgCustomParam);
            if (!b) {
                return;
            }
        }
        // 查询项目负责人列表
        Long partId = activityParticipationService.getParticipationId(orgId, actvId);
        // 项目创建人
        final UdpUser creator = userMapper.findByUserId(activity.getOrgId(), activity.getCreateUserId());
        if (creator == null) {
            return;
        }

        final LocalDateTime bizTime = LocalDateTime.now();
        final String txtId = msgWrapper.applyTxtId4Batch(activity.getOrgId(),
                DesignerMessageEnum.RELEASE_PRINCIPAL_MSG.getCode(), activity.getId(), NO.getValue(), members.size(),
                activity.getCreateUserId());
        //url参数值由CustomMsgAspect自动替换为设计器中设置的
        final Map<String, String> baseParamMap = new HashMap<>(8);
        baseParamMap.put(AomMsgConstants.PROJECT_NAME, activity.getActvName());
        baseParamMap.put(AomMsgConstants.URL, EMPTY);

        // 100一批一次发送，理论上负责人最多就1000
        Lists.partition(members, Constants.INT_100).forEach(subMemberList -> {
            MessageSendReq messageSendReq = toRlsMsg4Principal(activity, subMemberList, creator, bizTime, txtId,
                    baseParamMap);
            boolean sendMsg = msgWrapper.sendMessage(messageSendReq);
            if (sendMsg) {
                relationService.updateActvPrincipalRemind(orgId, actvId, partId, subMemberList);
            }
        });

        log.info("notifyPrincipalOnActvReleased end orgId={}, actvId={}", orgId, actvId);
    }

    private MessageSendReq toRlsMsg4Principal(Activity activity, List<String> members, UdpUser optUser,
            LocalDateTime bizTime, String txtId, Map<String, String> msgParamMap) {

        List<UdpUser> users = userMapper.findByUserIds(activity.getOrgId(), members);
        Map<String, UdpUser> userMap = translatorWrapper.transFullname(activity.getOrgId(), users);
        Map<String, String> optFullnameMap = getUserFullnameMap(activity.getOrgId(), optUser.getId(),
                users.stream().map(UdpUser::getLocale).collect(Collectors.toSet()), optUser.getFullname());

        MessageSendReq sendReq = new MessageSendReq();
        sendReq.setOrgId(activity.getOrgId());
        sendReq.setTxId(txtId);
        sendReq.setBatchId(ApiUtil.getUuid());
        sendReq.setSubjectId(activity.getId());
        sendReq.setParams(msgParamMap);
        sendReq.setTmplCode(DesignerMessageEnum.RELEASE_PRINCIPAL_MSG.getCode());
        sendReq.setTrigTime(LocalDateTime.now());
        sendReq.setUseCustomTmpl(1);
        sendReq.setTargetId(activity.getId());
        sendReq.setReceivers(members.stream().map(member -> {
            MessageSendReq.Receiver receiver = new MessageSendReq.Receiver();
            UdpUser user = userMap.get(member);
            if (user == null) {
                return null;
            }
            Map<String, String> paramMap = new HashMap<>(8);
            paramMap.put(AomMsgConstants.FULL_NAME, user.getFullname());
            paramMap.put(AomMsgConstants.CREATE_NAME, optFullnameMap.getOrDefault(user.getLocale(), EMPTY));
            receiver.setRecvId(member);
            receiver.setBizTime(bizTime);
            receiver.setParams(paramMap);
            return receiver;
        }).filter(Objects::nonNull).toList());
        sendReq.setBizTime(bizTime);
        sendReq.setSenderId(activity.getCreateUserId());
        return sendReq;
    }
}
