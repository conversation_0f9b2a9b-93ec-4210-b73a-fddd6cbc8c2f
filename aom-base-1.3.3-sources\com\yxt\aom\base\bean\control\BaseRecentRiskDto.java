package com.yxt.aom.base.bean.control;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.yxt.aom.base.entity.arrange.ActivityArrangeItem;
import com.yxt.aom.base.entity.common.Activity;
import com.yxt.aom.base.entity.control.ActivityItemStatistics;
import com.yxt.aom.base.enums.AomActivityStatusEnum;
import com.yxt.aom.base.util.AomNumberUtils;
import com.yxt.common.Constants;
import com.yxt.common.annotation.timezone.DateFormatField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

@Getter
@Setter
public class BaseRecentRiskDto {

    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "任务id")
    private Long taskId;
    @Schema(description = "任务名称")
    private String name;
    @Schema(description = "叶节点引用对象的具体类型(UACD注册表中定义)", example = "actv_exam")
    private String refRegId;
    @Schema(description = "类型国际化key")
    private String typeNameKey;
    @Schema(description = "活动任务类型自定义别名")
    private String actvAlias;

    @JsonFormat(pattern = Constants.SDF_YEAR2MINUTE, timezone = Constants.STR_GMT8)
    @DateFormatField(isDate = true)
    @Schema(description = "开始时间")
    private Date startTime;

    @JsonFormat(pattern = Constants.SDF_YEAR2MINUTE, timezone = Constants.STR_GMT8)
    @DateFormatField(isDate = true)
    @Schema(description = "结束时间")
    private Date endTime;

    @JsonFormat(pattern = Constants.SDF_YEAR2MINUTE, timezone = Constants.STR_GMT8)
    @DateFormatField(isDate = true)
    @Schema(description = "解锁时间")
    private Date unLockTime;

    @Schema(description = "任务完成率")
    private String taskRate;
    @Schema(description = "学习状态：未开始:0 ; 进行中:1;  已结束:2")
    private Integer status;
    @Schema(description = "外部关联id")
    private String targetId;
    @Schema(description = "是否必修 0否 1是 2动态调整")
    private Integer required;
    @Schema(description = "任务重复类型 0：默认 1：多班次任务")
    private Integer repeatFlag;
    @Schema(description = "活动学习链路id")
    private String trackId;
    @JsonIgnore
    private Integer periodOrderIndex;
    @JsonIgnore
    private Integer parentOrderIndex;
    @JsonIgnore
    private Integer orderIndex;
    @JsonIgnore
    private Integer studentNum;
    @JsonIgnore
    private Integer finishNum;

    @JsonIgnore
    public void calRate() {
        if (null == this.getStudentNum() || 0 == this.getStudentNum()) {
            this.setTaskRate("0");
            return;
        }
        this.setTaskRate(AomNumberUtils.dividePercent(BigDecimal.valueOf(this.getFinishNum()),
            BigDecimal.valueOf(this.getStudentNum())).toString());
    }

    @JsonIgnore
    public void calStatus(Date nowTime, Activity activity) {
        //已过结束时间的任务；项目已结束时任务也同步结束；
        if (AomActivityStatusEnum.isOver(activity.getActvStatus())) {
            this.setStatus(2);
            return;
        }
        if (null == this.getEndTime()) {
            // 解锁时间或者直播
            Date compareTime = this.getStartTime();
            if (null != compareTime) {
                if (compareTime.after(nowTime)) {
                    this.setStatus(0);
                    return;
                }
                this.setStatus(1);
                return;
            }
            compareTime = this.getUnLockTime();

            if (compareTime != null && compareTime.after(nowTime)) {
                this.setStatus(0);
                return;
            }

            this.setStatus(1);
            return;
        }
        if (this.getStartTime() != null && this.getStartTime().after(nowTime)) {
            this.setStatus(0);
            return;
        }
        if (this.getEndTime() != null && this.getEndTime().before(nowTime)) {
            this.setStatus(2);
            return;
        }
        this.setStatus(1);
    }

    @JsonIgnore
    public void formatBaseRecentRiskTaskDto(ActivityArrangeItem entity, ActivityArrangeItem periodItem,
            ActivityArrangeItem groupItem, ActivityItemStatistics itemStatistics) {
        this.setTaskId(entity.getId());
        this.setName(entity.getRefName());
        this.setRefRegId(entity.getRefRegId());
        this.setTypeNameKey(entity.getTypeNameKey());
        this.setActvAlias(entity.getActvAlias());
        this.setStartTime(entity.getStartTime());
        this.setEndTime(entity.getEndTime());
        this.setOrderIndex(entity.getOrderIndex());
        this.setTargetId(entity.getRefId());
        this.setRequired(entity.getRequired());
        this.setPeriodOrderIndex(periodItem.getOrderIndex());
        if (Objects.nonNull(groupItem)) {
            this.setParentOrderIndex(groupItem.getOrderIndex());
        } else {
            this.setParentOrderIndex(entity.getOrderIndex());
        }
        this.setFinishNum(itemStatistics.getCompleteCount());
        this.setStudentNum(itemStatistics.getTotalCount());
        if (null == this.getFinishNum()) {
            this.setFinishNum(0);
        }
        this.calRate();
    }

}
