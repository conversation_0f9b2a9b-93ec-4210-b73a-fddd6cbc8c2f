package com.yxt.aom.base.aop;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.yxt.aom.base.annotation.CustomerMsgTemplateAnno;
import com.yxt.aom.base.bean.common.CustomMsgBean;
import com.yxt.aom.base.entity.common.Activity;
import com.yxt.aom.base.enums.DesignerMessageEnum;
import com.yxt.aom.base.manager.common.AomDesignerManager;
import com.yxt.aom.base.service.common.ActivityService;
import com.yxt.aom.base.util.ThreadLocalMsgUtil;
import com.yxt.common.enums.YesOrNo;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.common.service.AuthService;
import com.yxt.common.util.StreamUtil;
import com.yxt.msgfacade.bean.MsgBean;
import com.yxt.msgfacade.bean.req.MessageSendReq;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.expression.AnnotatedElementKey;
import org.springframework.core.annotation.AnnotatedElementUtils;
import org.springframework.core.annotation.Order;
import org.springframework.expression.EvaluationContext;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.yxt.aom.base.common.AomMsgConstants.URL;
import static com.yxt.common.Constants.INT_0;
import static com.yxt.common.aop.AopConsts.BASE_AOP_ORDER;

/**
 * 设计器消息模板切面
 */
@Slf4j
@Aspect
@Order(BASE_AOP_ORDER + 500)
@Component("aomCustomMsgAspect")
@RequiredArgsConstructor
@ConditionalOnProperty(name = "aom.msg.aop.enabled", havingValue = "true", matchIfMissing = true)
public class CustomMsgAspect {
    private final AomDesignerManager designerManager;
    private final ExpressionEvaluator evaluator;
    private final ActivityService activityService;
    private final AuthService authService;

    /**
     * Init.
     */
    @PostConstruct
    public void init() {
        log.debug("Load CustomMsgAspect");
    }

    /**
     * Msg anno point cut.
     */
    @Pointcut("execution(public * com.yxt..*(..))&& @annotation(com.yxt.aom.base.annotation.CustomerMsgTemplateAnno))")
    public void msgAnnoPointCut() {// NOSONAR
    }

    /**
     * Send msg point cut.
     */
    @Pointcut("execution(public * com.yxt.msgfacade.service.MsgFacade.sendTemMsg(com.yxt.msgfacade.bean.MsgBean))")
    public void sendMsgPointCut() {// NOSONAR
    }

    /**
     * 新消息发送
     */
    @Pointcut("execution(public * com.yxt.msgfacade.service.MsgFacade.sendMessage(com.yxt.msgfacade.bean.req.MessageSendReq))")
    public void sendNewMsgPointCut() {// NOSONAR
    }

    /**
     * Share custom msg bean object.
     *
     * @param pj the pj
     * @return the object
     * @throws Throwable the throwable
     */
    @Around(value = "msgAnnoPointCut()")
    public Object shareCustomMsgBean(ProceedingJoinPoint pj) throws Throwable { // NOSONAR
        final Method method = getMethod(pj);

        CustomerMsgTemplateAnno customerMsgTemplateAnno = Optional.ofNullable(
                        method.getAnnotation(CustomerMsgTemplateAnno.class))
                .orElse(AnnotatedElementUtils.findMergedAnnotation(method, CustomerMsgTemplateAnno.class));
        Object result = null;
        if (customerMsgTemplateAnno == null) {
            return pj.proceed();
        }

        try {
            if (customerMsgTemplateAnno.messageEnum().length == INT_0 || (
                    StringUtils.isBlank(customerMsgTemplateAnno.value()) && StringUtils.isBlank(
                            customerMsgTemplateAnno.activity()))) {
                return pj.proceed();
            }

            final Activity activity = getActivity(pj, method, customerMsgTemplateAnno);
            Map<String, CustomMsgBean> codeMap = getCodeMap(customerMsgTemplateAnno, activity);
            Map<String, CustomMsgBean> oldMap = ThreadLocalMsgUtil.get();
            log.info("CustomerMsgTemplateAnno oldMap : {}", JSON.toJSONString(oldMap));
            Map<String, CustomMsgBean> mergeMap = merge(oldMap, codeMap);
            ThreadLocalMsgUtil.set(mergeMap);
            log.info("CustomerMsgTemplateAnno mergeMap : {}", JSON.toJSONString(mergeMap));
            result = pj.proceed();
        } catch (Throwable e) {
            log.error("msgAnnoPointCut error:{}", ExceptionUtils.getStackTrace(e));
        } finally {
            ThreadLocalMsgUtil.remove();
        }
        return result;
    }

    /**
     * Enhance send msg method object.
     *
     * @param pj the pj
     * @return the object
     * @throws Throwable the throwable
     */
    @Around(value = "sendMsgPointCut()")
    public Object enhanceSendMsgMethod(ProceedingJoinPoint pj) throws Throwable { // NOSONAR
        return replaceCustomInfo(pj);
    }

    /**
     * Enhance send new msg method object.
     *
     * @param pj the pj
     * @return the object
     * @throws Throwable the throwable
     */
    @Around(value = "sendNewMsgPointCut()")
    public Object enhanceSendNewMsgMethod(ProceedingJoinPoint pj) throws Throwable { // NOSONAR
        return replaceCustomInfo(pj);
    }

    private Map<String, CustomMsgBean> getCodeMap(CustomerMsgTemplateAnno customerMsgTemplateAnno, Activity activity) {
        List<String> msgTempCodes = Stream.of(customerMsgTemplateAnno.messageEnum()).map(DesignerMessageEnum::getCode)
                .distinct().toList();

        UserCacheBasic user = authService.getUserCacheBasic();
        String actvOrgId =
                activity != null ? Objects.toString(activity.getOrgId(), StringPool.EMPTY) : StringPool.EMPTY;
        List<CustomMsgBean> msgBeans = designerManager.getCustomMsgBean(activity, msgTempCodes,
                Optional.ofNullable(user).map(UserCacheBasic::getOrgId).orElse(actvOrgId));
        return StreamUtil.list2map(msgBeans, CustomMsgBean::getDefaultTemplateCode);
    }

    private Activity getActivity(ProceedingJoinPoint pj, Method method,
            CustomerMsgTemplateAnno customerMsgTemplateAnno) {
        final Activity activity;
        if (StringUtils.isBlank(customerMsgTemplateAnno.activity())) {
            String actvId = getObjectBySpel(customerMsgTemplateAnno.value(), pj.getTarget(), pj.getArgs(),
                    pj.getTarget().getClass(), method, String.class);
            String orgId = getObjectBySpel(customerMsgTemplateAnno.orgId(), pj.getTarget(), pj.getArgs(),
                    pj.getTarget().getClass(), method, String.class);
            activity = activityService.findById(orgId, actvId);
        } else {
            activity = getObjectBySpel(customerMsgTemplateAnno.activity(), pj.getTarget(), pj.getArgs(),
                    pj.getTarget().getClass(), method, Activity.class);
        }
        return activity;
    }

    private Object replaceCustomInfo(ProceedingJoinPoint pj) throws Throwable {
        Object result;
        Object[] args = pj.getArgs();
        try {
            Map<String, CustomMsgBean> map = ThreadLocalMsgUtil.get();
            String tempCode = getParam(args);
            if (map == null || StringUtils.isBlank(tempCode)) {
                return pj.proceed();
            }

            CustomMsgBean customMsgBean = map.get(tempCode);
            log.info("sendNewMsgPointCut customMsgBean==> tempCode:{}, map: {}", tempCode, JSON.toJSONString(map));
            if (customMsgBean == null || Objects.equals(customMsgBean.getEnabled(), YesOrNo.NO.getValue())) {
                return null;
            }
            replaceValue(args, customMsgBean);
            log.info("sendNewMsgPointCut replaceMsgBean==> tempCode:{}, args: {}", tempCode, JSON.toJSONString(args));
            result = pj.proceed(args);

        } catch (Exception e) {
            log.error("sendNewMsgPointCut enhanceSendMsgMethod_error:{}", ExceptionUtils.getStackTrace(e));
            throw e;
        }
        return result;
    }

    private Method getMethod(JoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();

        if (method.getDeclaringClass().isInterface()) {
            try {
                method = joinPoint.getTarget().getClass()
                        .getDeclaredMethod(joinPoint.getSignature().getName(), method.getParameterTypes());
            } catch (SecurityException | NoSuchMethodException ex) {
                throw new RuntimeException(ex); // NOSONAR
            }
        }
        return method;
    }

    private <T> T getObjectBySpel(String expressionKey, Object object, Object[] args, Class<?> targetClz, Method method,
            Class<T> objClz) {
        EvaluationContext evaluationContext = evaluator.createEvaluationContext(object, targetClz, method, args);
        AnnotatedElementKey methodKey = new AnnotatedElementKey(method, targetClz);
        return evaluator.condition(expressionKey, methodKey, evaluationContext, objClz);
    }

    private void replaceValue(Object[] args, CustomMsgBean customMsg) {
        for (int i = 0; i < args.length; i++) {
            Object param = args[i];
            if (param instanceof MsgBean msgBean) {
                replaceMsgBean(customMsg, msgBean);
                args[i] = msgBean;
            } else if (param instanceof MessageSendReq messageSendReq) {
                replaceMessageSendReq(customMsg, messageSendReq);
                args[i] = messageSendReq;
            }
        }
    }

    private void replaceMessageSendReq(CustomMsgBean customMsg, MessageSendReq messageSendReq) {
        messageSendReq.setTmplCode(
                Optional.ofNullable(customMsg.getTemplateCode()).orElse(customMsg.getDefaultTemplateCode()));
        Map<String, String> params = messageSendReq.getParams();
        if (StringUtils.isNotBlank(customMsg.getShortUrl())) {
            params.put(URL, customMsg.getShortUrl());
        }
        messageSendReq.setDesignId(String.valueOf(customMsg.getDesignerId()));
    }

    private void replaceMsgBean(CustomMsgBean customMsg, MsgBean msgBean) {
        msgBean.setTemplateCode(
                Optional.ofNullable(customMsg.getTemplateCode()).orElse(customMsg.getDefaultTemplateCode()));
        Map<String, String> params = msgBean.getParams();
        if (StringUtils.isNotBlank(customMsg.getShortUrl())) {
            params.put(URL, customMsg.getShortUrl());
        }
        msgBean.setDesignId(String.valueOf(customMsg.getDesignerId()));
    }

    private String getParam(Object[] args) {
        for (Object arg : args) {
            if (arg instanceof MsgBean msgBean) {
                return msgBean.getTemplateCode();
            }
            if (arg instanceof MessageSendReq messageSendReq) {
                return messageSendReq.getTmplCode();
            }

        }
        return StringPool.EMPTY;
    }

    private Map<String, CustomMsgBean> merge(Map<String, CustomMsgBean> oldMap, Map<String, CustomMsgBean> currentMap) {
        if (MapUtils.isEmpty(oldMap)) {
            return currentMap;
        }
        if (MapUtils.isEmpty(currentMap)) {
            return oldMap;
        }

        return Stream.of(oldMap, currentMap).flatMap(map -> map.entrySet().stream())
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (v1, v2) -> v2));
    }
}
