package com.yxt.aom.activity.common;

/**
 * AomActvApiError
 */
public final class AomActvApiError {
    private AomActvApiError() {
        //Hide Constant Class Constructor
    }

    public static final String CHECKBOX_RANGE_INVALID = "api.aom.actv.checkBox.range.invalid";

    public static final String MISSING_PARAMS = "apis.aom.common.params.missing";

    public static final String ITEM_NOTFOUND = "apis.aom.item.NotFound";
    /**
     * 先选择学员
     */
    public static final String SELECT_STUDENT_FIRST = "api.aom.activity.select.student.first";

    public static final String ACTIVITY_NOT_ING = "api.aom.activity.not.ing";

    public static final String ACTIVITY_ARRANGE_NOT_FOUND = "api.aom.activity.arrange.not.found";

    public static final String ACTIVITY_HAND_COMPLETE_USER_EXCEPTION = "api.aom.hand.complete.exist.user.notIn.actv";

    /**
     * The constant DRAFT_ITEM_NOT_FOUND.
     */
    public static final String DRAFT_ITEM_NOT_FOUND = "apis.aom.draft.item.notfound";
}
