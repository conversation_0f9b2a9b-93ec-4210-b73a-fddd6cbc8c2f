package com.yxt.aom.base.bean.sn;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Objects;

import static com.yxt.aom.base.common.DefaultValueConstants.Numbers.INT_0;
import static com.yxt.aom.base.common.DefaultValueConstants.Numbers.INT_1;

/**
 * @description: 解锁配置
 * @author: dingjh
 * @date: 2025/6/11 17:41
 */
@Data
public class ActivitySnConfig {
    /**
     * 解锁模式1自由学习、2按时间解锁、3闯关解锁
     */
    @Schema(description = "解锁模式1自由学习、2按时间解锁、3闯关解锁", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer modeType;

    /**
     * 解锁单元1 按任务 2按阶段
     */
    @Schema(description = "解锁单元1 按任务 2按阶段", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer unitType;

    /**
     * 固定时间1 每隔N天 2 每个周N 3 按阶段周期解锁4
     */
    @Schema(description = "固定时间1 每隔N天 2 每个周N 3 按阶段周期解锁4", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer timeType;

    /**
     * 每个N天或者周N，周一 = 1，周二 = 2
     */
    @Schema(description = "每个N天或者周N，周一 = 1，周二 = 2", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer timeValue;

    /**
     * 解锁条件 1完成之前所有必修任务 2完成之前指定关卡任务
     */
    @Schema(description = "解锁条件 1完成之前所有必修任务 2完成之前指定关卡任务", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer passConditionType;
    /**
     * 阶段间按顺序学习 0否 1是
     */
    @Schema(description = "阶段间按顺序学习 0否 1是", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer sequenceStages;
    /**
     * 前置阶段完成后允许提前开始下一阶段学习 0否 1是
     */
    @Schema(description = "前置阶段完成后允许提前开始下一阶段学习 0否 1是", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer prePhaseNext;

    /**
     * 阶段周期模式 0:周期天数模式，1：设置周期起止模式
     */
    @Schema(description = "阶段周期模式 0:周期天数模式，1：设置周期起止模式", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer cycleModel;

    public Integer getPassConditionType() {
        if (Objects.equals(passConditionType, INT_0)) {
            return INT_1;
        }
        return passConditionType;
    }

}
