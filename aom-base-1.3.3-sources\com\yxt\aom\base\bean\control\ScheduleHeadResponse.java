package com.yxt.aom.base.bean.control;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.yxt.aom.base.entity.arrange.ActivityArrangeItem;
import com.yxt.common.Constants;
import com.yxt.common.annotation.timezone.DateFormatField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.ObjectUtils;

import java.util.Date;
import java.util.Map;

@Getter
@Setter
public class ScheduleHeadResponse {

    @Schema(description = "叶节点引用对象的具体类型(UACD注册表中定义)", example = "actv_exam")
    private String refRegId;
    @Schema(description = "类型国际化key")
    private String typeNameKey;
    @Schema(description = "活动任务类型自定义别名")
    private String actvAlias;
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "阶段、任务id")
    private Long id;
    @Schema(description = "阶段、任务名称")
    private String name;
    @Schema(description = "外部关联id")
    private String targetId;
    @Schema(description = "阶段id")
    private Long periodId;
    @Schema(description = "阶段排序号")
    private Integer periodOrderIndex;

    @JsonFormat(pattern = Constants.SDF_YEAR2MINUTE, timezone = Constants.STR_GMT8)
    @Schema(description = "开始时间")
    @DateFormatField(isDate = true)
    private Date startTime;

    @JsonFormat(pattern = Constants.SDF_YEAR2MINUTE, timezone = Constants.STR_GMT8)
    @Schema(description = "结束时间")
    @DateFormatField(isDate = true)
    private Date endTime;

    @JsonFormat(pattern = Constants.SDF_YEAR2MINUTE, timezone = Constants.STR_GMT8)
    @Schema(description = "解锁时间")
    @DateFormatField(isDate = true)
    private Date unLockTime;

    @Schema(description = "是否是阶段")
    private boolean isPeriod;

    @JsonIgnore
    public static ScheduleHeadResponse headByTask(ActivityArrangeItem entity, ActivityArrangeItem period, Map<String,String> i18Map) {
        ScheduleHeadResponse dto = new ScheduleHeadResponse();
        dto.setId(entity.getId());
        dto.setName(ObjectUtils.defaultIfNull(entity.getItemName(), entity.getRefName()));
        dto.setPeriod(false);
        dto.setRefRegId(entity.getRefId());
        dto.setTypeNameKey(entity.getTypeNameKey());
        dto.setActvAlias(entity.getActvAlias());
        dto.setTargetId(entity.getRefRegId());
        dto.setPeriodId(period.getId());
        dto.setStartTime(entity.getStartTime());
        dto.setEndTime(entity.getEndTime());
        return dto;
    }

    @JsonIgnore
    public static ScheduleHeadResponse headByPeriod(ActivityArrangeItem entity) {
        ScheduleHeadResponse dto = new ScheduleHeadResponse();
        dto.setId(entity.getId());
        dto.setName(ObjectUtils.defaultIfNull(entity.getItemName(), entity.getRefName()));
        dto.setTypeNameKey(entity.getTypeNameKey());
        dto.setActvAlias(entity.getActvAlias());
        dto.setPeriodId(entity.getId());
        dto.setPeriodOrderIndex(entity.getOrderIndex());
        dto.setPeriod(true);
        return dto;
    }
}
