package com.yxt.aom.base.bean.part.group;

import com.alibaba.excel.annotation.ExcelProperty;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;

import static com.yxt.aom.base.common.AomPropConstants.ERROR_INFO;
import static com.yxt.aom.base.common.AomPropConstants.USER_FULL_NAME;
import static com.yxt.aom.base.common.AomPropConstants.USER_NAME;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GroupMemberImportBean {

    public static final List<String> COLUMN = Collections.unmodifiableList(
            Lists.newArrayList(USER_NAME, USER_FULL_NAME, ERROR_INFO));

    @ExcelProperty(index = 0)
    private String userName;

    @ExcelProperty(index = 1)
    private String userFullName;

    private String errorInfo;
}
