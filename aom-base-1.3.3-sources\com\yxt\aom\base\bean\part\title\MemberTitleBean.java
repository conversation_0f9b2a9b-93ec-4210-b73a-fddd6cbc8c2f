package com.yxt.aom.base.bean.part.title;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

/**
 * 学员称号
 *
 * <AUTHOR>
 * @since 2025/6/6
 */
@Getter
@Setter
@NoArgsConstructor
public class MemberTitleBean {

    @Schema(description = "学员姓名")
    private String fullName;

    @Schema(description = "学员账号")
    private String userName;

    @Schema(description = "称号名称")
    private String title;

    @Schema(description = "点评")
    private String comment;

    @Schema(description = "是否消息通知学员")
    private String notify;

    private int userNotify;

    /**
     * 更新时间
     */
    private Date updateTime;

    private String userId;

    @Schema(description = "称号id")
    private Long titleId;

    @Schema(description = "是否为优秀，0-否，1-是")
    private Integer outstanding;

}
