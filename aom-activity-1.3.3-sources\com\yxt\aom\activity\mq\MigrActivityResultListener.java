package com.yxt.aom.activity.mq;

import com.alibaba.fastjson.JSON;
import com.yxt.aom.base.component.common.AomDataMigrComponent;
import com.yxt.aom.base.enums.ActivityTypeEnum;
import com.yxt.aom.base.service.control.MigrActivityResultService;
import com.yxt.aom.common.config.AomDataSourceTypeHolder;
import com.yxt.aom.common.config.AomDbProperties;
import com.yxt.aom.migr.common.MigrStepEnum;
import com.yxt.aom.migr.mq.MigrActivityResultDataMq;
import com.yxt.dbrouter.core.toolkit.DBRouteHolder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import static com.yxt.aom.migr.common.MqConstants.TOPIC_AOM_MIGR_ACTIVITY_RESULT_DATA;

/**
 * MigrActivityResultListener
 */
@Slf4j
@RequiredArgsConstructor
@Component
@ConditionalOnProperty(name = "aom.rocketmq.consumer-enabled", havingValue = "true", matchIfMissing = true)
@RocketMQMessageListener(consumerGroup = "${spring.application.name}-group4actv-"
        + TOPIC_AOM_MIGR_ACTIVITY_RESULT_DATA, topic = TOPIC_AOM_MIGR_ACTIVITY_RESULT_DATA, consumeMode = ConsumeMode.CONCURRENTLY, consumeThreadNumber = 3)
public class MigrActivityResultListener implements RocketMQListener<String> {
    private final AomDbProperties dbProperties;
    private final AomDataMigrComponent migrComponent;
    private final MigrActivityResultService migrActivityResultService;

    @Override
    public void onMessage(String message) {
        log.info("MigrActivityResultListener, message: {}", message);
        if (StringUtils.isEmpty(message)) {
            return;
        }
        MigrActivityResultDataMq mq = null;
        try {
            mq = JSON.parseObject(message, MigrActivityResultDataMq.class);
            if (mq != null && dbProperties.getDsmap().containsKey(mq.getRefRegId())) {
                long startTime = System.currentTimeMillis();
                AomDataSourceTypeHolder.set(mq.getRefRegId());
                DBRouteHolder.push(mq.getOrgId());
                migrActivityResultService.migrActivityResult(mq, ActivityTypeEnum.ACTV.getType());
                log.info("MigrActivityResultListener migrActivityResult cost : {}",
                        System.currentTimeMillis() - startTime);
            }
        } catch (Exception ex) {
            log.error("MigrActivityResultListener failed. message: {}, err: ", message, ex);
            if (mq != null) {
                migrComponent.sendMigrFailedMq(mq.getOrgId(), mq.getActvId(), mq.getActvRegId(), MigrStepEnum.RESULT,
                        ExceptionUtils.getStackTrace(ex));
            }
        }
    }
}
