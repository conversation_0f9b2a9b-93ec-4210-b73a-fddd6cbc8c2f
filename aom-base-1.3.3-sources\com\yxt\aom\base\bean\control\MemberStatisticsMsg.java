package com.yxt.aom.base.bean.control;

import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/11/20  18:40
 * @description 计算学员进度消息体
 */
@Data
public class MemberStatisticsMsg {

    /**
     * 组织id
     */
    private String orgId;

    /**
     * 活动id
     */
    private String actvId;

    /**
     * 活动regId
     */
    private String actvRegId;

    /**
     * item完成情况，key->任务id, value->学员ids
     */
    private Map<Long, List<String>> baseResultMap;

    /**
     * 学员id
     */
    private String userId;

    /**
     * 是否更新任务完成数
     */
    private Boolean refreshCompleteCount;

    /**
     * 0 ---> 只刷新学员表的任务完成数等数据
     * 1 --> 学员任务完成
     */
    private Integer optType;

    /**
     * 操作人
     */
    private String optUserId;

    /**
     * 操作时间
     */
    private Date optTime;

}
