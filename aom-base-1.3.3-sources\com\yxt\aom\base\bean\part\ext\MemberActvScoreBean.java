package com.yxt.aom.base.bean.part.ext;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 学员活动成绩
 *
 * <AUTHOR>
 * @since 2025/5/21
 */
@Getter
@Setter
@NoArgsConstructor
public class MemberActvScoreBean {

    @Schema(description = "学员ID")
    private String userId;

    @Schema(description = "学员活动成绩")
    private BigDecimal activityScore;

    @Schema(description = "学员活动评语")
    private String activityComment;

    @Schema(description = "是否通过(0-否 1-是 -1-未提交)")
    private Integer passed;

}
