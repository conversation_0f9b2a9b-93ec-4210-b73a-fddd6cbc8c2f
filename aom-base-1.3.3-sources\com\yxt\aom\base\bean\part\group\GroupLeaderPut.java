package com.yxt.aom.base.bean.part.group;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.yxt.aom.base.common.BaseErrorConsts;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 更新小组组长
 *
 * <AUTHOR>
 * @since 2024/12/17
 */
@Getter
@Setter
@NoArgsConstructor
public class GroupLeaderPut {

    @Schema(description = "活动id")
    private String actvId;

    @Schema(description = "UACD注册表中定义Id")
    private String regId;

    @Schema(description = "参与id", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long participationId;

    @Schema(description = "小组id")
    @JsonSerialize(using = ToStringSerializer.class)
    @NotNull(message = BaseErrorConsts.GROUP_ID_IS_NULL)
    private long id;

    @Schema(description = "小组组长id", example = "id3", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String leaderId;

}
