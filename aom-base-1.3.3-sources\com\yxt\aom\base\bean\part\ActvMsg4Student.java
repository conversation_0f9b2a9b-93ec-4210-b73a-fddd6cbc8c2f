package com.yxt.aom.base.bean.part;

import com.yxt.aom.base.entity.common.Activity;
import com.yxt.msgfacade.bean.req.MessageSendReq;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * ActvMsg4Student
 */
@Getter
@Setter
@NoArgsConstructor
public class ActvMsg4Student {
    /**
     * Constructor
     *
     * @param activity Activity
     * @param data     MessageSendReq
     * @param batchId  String
     */
    public ActvMsg4Student(Activity activity, MessageSendReq data, String batchId) {
        this.activity = activity;
        this.data = data;
        this.batchId = batchId;
    }

    private Activity activity;
    private MessageSendReq data;
    private String batchId;
}
