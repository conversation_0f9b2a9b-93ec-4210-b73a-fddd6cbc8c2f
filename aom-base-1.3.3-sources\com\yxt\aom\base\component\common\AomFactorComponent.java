package com.yxt.aom.base.component.common;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yxt.aom.base.wrapper.CoreWrapper;
import com.yxt.common.exception.ApiException;
import com.yxt.coreapi.client.bean.sale.OrgVerifyFactorBean;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

import static com.yxt.aom.base.common.BaseErrorConsts.FACTOR_CHECK_DISABLED;
import static com.yxt.aom.base.common.BaseErrorConsts.FACTOR_CHECK_NEED_UPGRADE;
import static com.yxt.aom.base.common.DefaultValueConstants.Numbers.INT_0;
import static com.yxt.aom.base.common.DefaultValueConstants.Numbers.INT_1;
import static com.yxt.coreapi.client.bean.em.OrgFactorStateEnum.NOT_PURCHASED;
import static com.yxt.coreapi.client.bean.em.OrgFactorStateEnum.PURCHASED_EXPIRED;
import static com.yxt.coreapi.client.bean.em.OrgFactorStateEnum.PURCHASED_NOT_EXPIRED;
import static com.yxt.coreapi.client.bean.em.OrgFactorStateEnum.UNSHELVES;
import static java.lang.Integer.parseInt;
import static org.apache.commons.collections4.CollectionUtils.isEmpty;
import static org.apache.commons.lang3.StringUtils.isBlank;


/**
 * AomFactorComponent
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class AomFactorComponent {
    private final CoreWrapper coreWrapper;

    /**
     * 多个要素code校验
     *
     * @param orgId          the org id
     * @param orCondition    the or condition
     * @param factorCodes    the factor codes
     * @param throwException the throw exception
     * @return the boolean
     * @throws ApiException 如果要素校验不通过则抛出该异常
     */
    @SuppressWarnings("squid:S3776")
    public boolean checkFactors(@NonNull String orgId, boolean orCondition, List<String> factorCodes,
            boolean throwException) {
        Map<String, Integer> codeMap = factorCodes(factorCodes);
        List<OrgVerifyFactorBean> factorBeans = coreWrapper.getOrgFactors(orgId, Lists.newArrayList(codeMap.keySet()));
        if (isEmpty(factorBeans)) {
            if (throwException) {
                throw new ApiException(FACTOR_CHECK_NEED_UPGRADE);
            } else {
                return false;
            }
        }

        // 如果没有通过，优先展示禁用的提示用语
        boolean pass = false;
        boolean disabled = false;
        if (orCondition) {
            for (OrgVerifyFactorBean factorBean : factorBeans) { // NOSONAR
                Boolean check = check(codeMap, factorBean);
                if (check == null) {
                    pass = true;
                    break;
                }
                if (check) {
                    disabled = true;
                    break;
                }
            }
        } else {
            pass = true;
            for (OrgVerifyFactorBean factorBean : factorBeans) {
                Boolean check = check(codeMap, factorBean);
                if (pass && check != null) {
                    pass = false;
                }
                if (check != null && check) {
                    disabled = true;
                    break;
                }
            }
        }

        log.debug("Factor check result:orgId{},codes{},pass{},disabled{}", orgId, factorCodes, pass, disabled);

        if (pass) {
            return true;
        }

        if (throwException) {
            if (disabled) {
                throw new ApiException(FACTOR_CHECK_DISABLED);
            } else {
                throw new ApiException(FACTOR_CHECK_NEED_UPGRADE);
            }
        } else {
            return false;
        }
    }

    /**
     * 单个要素 code 校验
     *
     * @param orgId      the org id
     * @param factorCode the factor code
     * @throws ApiException 如果要素校验不通过则抛出该异常
     */
    public void checkFactor(String orgId, String factorCode) {
        checkFactors(orgId, true, Lists.newArrayList(factorCode), true);
    }

    /**
     * 单个要素 code 校验
     *
     * @param orgId      the org id
     * @param factorCode the factor code
     * @return the boolean
     */
    public boolean checkFactorWithReturn(String orgId, String factorCode) {
        return checkFactors(orgId, true, Lists.newArrayList(factorCode), false);
    }

    /**
     * 如果通过返回 null，如果因为禁用、过期不通过返回 true，如果未购买不通过返回 false
     */
    @SuppressWarnings("squid:S2447")
    private Boolean check(Map<String, Integer> codeMap, OrgVerifyFactorBean fb) {
        if (fb.getFactorState() == PURCHASED_NOT_EXPIRED.getState()) {
            return null;
        }

        if (fb.getFactorState() == PURCHASED_EXPIRED.getState() || fb.getFactorState() == UNSHELVES.getState()) {
            if (codeMap.get(fb.getFactorCode()) == INT_1) {
                return null;
            }
            return true;
        }

        if (fb.getFactorState() == NOT_PURCHASED.getState()) {
            return false;
        }

        return null;
    }

    private Map<String, Integer> factorCodes(List<String> factorCodes) {
        Map<String, Integer> codeMap = Maps.newHashMap();
        if (isEmpty(factorCodes)) {
            return codeMap;
        }
        for (String code : factorCodes) {
            if (isBlank(code)) {
                continue;
            }
            String[] split = code.split("\\|");
            codeMap.put(split[INT_0], split.length > INT_1 ? parseInt(split[1]) : INT_0);
        }
        return codeMap;
    }
}
