package com.yxt.aom.base.bean.arrange;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.yxt.common.Constants;
import com.yxt.common.annotation.timezone.DateFormatField;
import com.yxt.common.component.SpringContextHolder;
import com.yxt.common.service.MessageSourceService;
import io.swagger.v3.oas.annotations.media.Schema;
import jodd.util.StringPool;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;

import java.util.Date;
import java.util.Locale;

import static com.yxt.common.Constants.INT_1;

/**
 * ActvCheckBean
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@Slf4j
public class ActvCheckBean {
    private static MessageSourceService messageSourceService = SpringContextHolder.getBean(MessageSourceService.class);

    @Schema(description = "0：验证通过；" + "1：您不是这个项目的学员；" + "2：阶段锁定；" + "3：项目未开始（message有项目开始时间）；"
            // 已废弃
            + "4：活动未开始（message有活动开始时间）；" + "5：前面存在不合格的活动；" + "6：前面存在未完成的活动；"
            + "7：项目未发布；" + "8：活动未找到；" + "9：使用平台移动端/H5进行签到（签退）；" + "10：签到已结束，未签到（签退）；"
            + "11：{yyyy-MM-dd HH:mm:ss} 已签到（迟到签到 / 签退 / 早退）；" + "12：阶段未到解锁时间（message有阶段解锁时间）；"
            + "13：活动未到解锁时间（message有活动解锁时间）；" + "14：项目已结束；" + "15: 学员不属于当前带教阶段"
            + "16: 周期模式学员未到开始学习时间" + "17: 新考勤 请扫描考勤二维码" + "18：内容一体化 不能占用有效订单"
            + "19：项目负责人/带教导师已指派您重复学习此活动，请退出后重新学习" + "20: 管理员还没为您分配班次,请耐心等待"
            + "21: 项目已归档")
    private int code;

    @Schema(description = "错误消息")
    private String message;

    @JsonIgnore
    private Locale locale;

    @Schema(description = "学员活动结果状态 true 完成 false 未完成")
    private Boolean isFinish = false;

    @Schema(description = "加入项目时间")
    @DateFormatField(format = Constants.SDF_YEAR2MINUTE, isDate = true)
    private Date joinTime;

    public ActvCheckBean() {
        // NOSONAR
    }

    public static ActvCheckBean of(Locale locale) {
        return new ActvCheckBean().setLocale(locale);
    }

    public ActvCheckBean setMessage(String code) {
        this.message = code;
        if (this.locale != null) {
            this.message = getMessage(code, this.locale);
        }
        return this;
    }

    public void setMessage(String message, boolean isMessage) {
        if (isMessage) {
            this.message = message;
        }
    }

    public ActvCheckBean setIsFinish(boolean isFinish) {
        this.isFinish = isFinish;
        return this;
    }

    public ActvCheckBean setMessage(String code, Object... args) {
        this.message = code;
        if (this.locale != null) {
            this.message = getMessage(code, args, this.locale);
        }
        return this;
    }

    private static String getMessage(String code, Locale locale) {
        return getMessage(code, null, locale);
    }

    private static String getMessage(String code, Object[] args, Locale locale) {
        String message;
        try {
            message = messageSourceService.getMessage(code, args, locale);
        } catch (Exception e) {
            log.error("messageSourceService getMessage error: {}", ExceptionUtils.getStackTrace(e));
            return StringPool.EMPTY;
        }
        if (StringUtils.isBlank(message)) {
            return message;
        }

        if (message.contains(StringPool.SEMICOLON)) {
            return message.substring(message.lastIndexOf(StringPool.SEMICOLON) + INT_1).trim();
        }

        return message;
    }
}
