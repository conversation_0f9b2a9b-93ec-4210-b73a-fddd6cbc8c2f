package com.yxt.aom.base.bean.part.file;

import com.yxt.aom.base.common.BaseErrorConsts;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 人员导入bean
 *
 * <AUTHOR>
 * @since 2024/12/18
 */
@Getter
@Setter
@NoArgsConstructor
public class MemberImportReq {

    @Schema(description = "文件id")
    @NotBlank(message = BaseErrorConsts.MISSING_PARAMS)
    private String fileId;

}
