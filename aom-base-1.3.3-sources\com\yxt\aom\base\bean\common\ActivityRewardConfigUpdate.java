package com.yxt.aom.base.bean.common;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/5/28  9:14
 * @description 描述
 */
@Data
@Schema(name = "奖励规则更新请求")
public class ActivityRewardConfigUpdate {

    @Schema(description = "奖励规则ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @Schema(description = "来源id")
    private String sourceId;

    /**
     * @see com.yxt.business.action.common.enums.SourceTypeEnum
     */
    @Schema(description = "来源类型(1001-培训)")
    private Integer sourceType;

    @Schema(description = "业务id")
    private String serviceId;

    /**
     * @see com.yxt.business.action.common.enums.ModuleTypeEnum
     */
    @Schema(description = "业务类型(1023-任务外部链接, 1024-任务面授, 1025-任务活动, 1026-任务考勤, 1040-任务课件, "
            + "1041-任务课程, 1042-任务考试, 1043-任务练习, 1044-任务直播, 1045-任务作业, 1046-任务问卷, 1074-任务鉴定, "
            + "1089-考核, 1091-话术训练, 1092-讨论, 1098-演讲训练, 1099-实战演练)")
    private Integer serviceType;

    /**
     * @see com.yxt.business.action.common.enums.ActionTypeEnum
     */

    @Schema(description = "动作类型(1010-完成, 1101-通过, 1031-评论, 1019-点赞, 1027-设为精华, 1070-成绩优秀, "
            + "1106-带教评价合格, 1107-带教评价不合格)")
    private Integer actionType;

    @Schema(description = "奖励规则的触发条件，默认值是1：如完成就发奖励，该值就是1；点赞5次发奖励，该值就是5")
    private Integer requireCount = 1;

    @Schema(description = "积分")
    private int point;

    @Schema(description = "学分")
    private BigDecimal credit;

}
