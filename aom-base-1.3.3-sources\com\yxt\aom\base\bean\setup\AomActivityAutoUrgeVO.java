package com.yxt.aom.base.bean.setup;

import com.alibaba.fastjson.JSON;
import com.yxt.aom.base.util.AomUtils;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@Getter
@Setter
public class AomActivityAutoUrgeVO {

    private static final long serialVersionUID = 1175978995515036663L;

    private Long urgeId;

    private String actvId;

    private Integer urgeType;

    private Integer urgeTime = 8;//NOSONAR

    private Integer urgeBeforeDays = 1;//NOSONAR

    private Integer effective = 0;//NOSONAR

    private Integer fixTimeFlag = 0;//NOSONAR

    private String fixTime;

    private Integer urgeAudit;

    private String urgeSubType;

    private Integer urgeTimeType = 0;//NOSONAR

    private  String timeRange;

    private Integer urgeRangeWithManager;

    private Integer urgeTimeCategory;

    private Integer urgeCycleType;

    private String urgeCycleTimeRange;

    private String customCycleExt;

    public AomActivityAutoUrgeCreate generateAutoUrgeCreate(){
        AomActivityAutoUrgeCreate result = new AomActivityAutoUrgeCreate();
        result.setUrgeId(this.getUrgeId());
        result.setActvId(this.getActvId());
        result.setUrgeType(this.getUrgeType());
        result.setUrgeTime(this.getUrgeTime());
        result.setUrgeBeforeDays(this.getUrgeBeforeDays());
        result.setEffective(this.getEffective());
        result.setFixTimeFlag(this.getFixTimeFlag());
        result.setFixTime(AomUtils.convertJsonObject(this.getFixTime(), AomActivityFixTimeExt.class));
        result.setUrgeAudit(this.getUrgeAudit());
        result.setUrgeTimeType(this.getUrgeTimeType());
        result.setUrgeRangeWithManager(this.getUrgeRangeWithManager());
        result.setUrgeTimeCategory(this.getUrgeTimeCategory());
        result.setUrgeCycleType(this.getUrgeCycleType());
        if(StringUtils.isNotEmpty(this.getUrgeSubType())){
            result.setUrgeSubType(JSON.parseArray(this.getUrgeSubType(),Integer.class));
        }
        if(StringUtils.isNotEmpty(this.getTimeRange())){
            result.setTimeRange(JSON.parseArray(this.getTimeRange(),Integer.class));
        }
        if(StringUtils.isNotEmpty(this.getUrgeCycleTimeRange())){
            result.setUrgeCycleTimeRange(JSON.parseArray(this.getUrgeCycleTimeRange(),Integer.class));
        }
        if (StringUtils.isNotEmpty(this.getCustomCycleExt())) {
            List<AomActivityUrgeCustomCycleExtObj> aomActivityUrgeCustomCycleExtObjs = AomUtils.convertJsonArray(
                    this.getCustomCycleExt(), AomActivityUrgeCustomCycleExtObj.class);
            result.setCustomCycleExtList(aomActivityUrgeCustomCycleExtObjs);
        }

        return result;
    }

}
