package com.yxt.aom.base.bean.part;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.yxt.aom.base.bean.order.OrderFields;
import com.yxt.common.Constants;
import com.yxt.common.annotation.timezone.DateFormatField;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

import java.util.Collections;
import java.util.Date;
import java.util.List;

 /**
 * 参与学员相关入参
 */
@Data
@Tag(name = "参与学员相关入参")
public class PartMemberReq {

    @Schema(description = "活动id")
    private String actvId;

    @Schema(description = "UACD注册表中定义Id")
    private String regId;

    @Schema(description = "参与id", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long participationId;

    @Schema(description = "小组id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long groupId;


    @Schema(description = "1正式学员 0旁听学员", example = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer formal;

    @Schema(description = "完成状态 0-未开始 1-进行中 2-已完成", example = "1")
    private Integer studyStatus;

    @Schema(description = "完成状态集合 0-未开始 1-进行中 2-已完成")
    private List<Integer> studyStatusList;

    @Schema(description = "是否逾期 0-未逾期 1-已逾期")
    private Integer overdue;

    @Schema(description = "部门id")
    private List<String> deptIds;

    @Schema(description = "岗位id")
    private List<String> positionIds;

    @Schema(description = "直属经理id")
    private List<String> managerIds;

    @Schema(description = "职级id列表")
    private List<String> gradeIds;

    @Schema(description = "账号状态账号状态 0-禁用 1-启用 2-删除 不传取所有", example = "1")
    private Integer status;

    @Schema(description = "开始学习开始时间", example = "2019-10-08")
    @DateFormatField(format = Constants.SDF_YEAR2DAY)
    private String startTime;

    @Schema(description = "开始学习结束时间", example = "2019-10-08")
    @DateFormatField(format = Constants.SDF_YEAR2DAY)
    private String endTime;

    @Schema(description = "关键字")
    private String keyword;

    @Schema(description = "搜索类型 1-姓名 2-账号")
    private Integer searchType;

    @Schema(description = "排序")
    private OrderFields orderFieldObj;


    @Schema(description = "是否全选", example = "true")
    private boolean checkAll;

    @Schema(description = "选择的学员ids列表，当checkAll=false时需要传入")
    private List<String> userIds;

    @Schema(description = "排除学员id")
    private List<String> excludeUserIds = Collections.emptyList();

    @Schema(description = "通知学员类型变化", example = "false",requiredMode = Schema.RequiredMode.REQUIRED)
    private boolean msg = false;

    /**
     * 删除学员 使用学员账号
     */
    @Schema(description = "学员账号", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> userNames;

     @Schema(description = "毕业状态(0-未毕业 1-已毕业 2-取消毕业) -- V6.1", example = "1")
     private Integer graduated;

     @Schema(description = "是否发送消息(0-不发送 1-发送)")
     private Integer sendMsg;

     @Schema(description = "设置的开始学习时间")
     @DateFormatField(isDate = true)
     private Date setStartTime;

     @Schema(description = "设置的结束学习时间")
     @DateFormatField(isDate = true)
     private Date setEndTime;

     @Schema(description = "称号id")
     private Long titleId;

     @Schema(description = "评论")
     private String comment;

     /**
      * 操作类型 0 批量更换  1单个学员更换
      */
     @Schema(description = "操作类型 0 批量更换  1单个学员更换")
     private Integer operateType;
}
