package com.yxt.aom.base.component.part;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.aom.base.bean.part.group.GroupBeanRes;
import com.yxt.aom.base.bean.part.group.GroupSearch;
import com.yxt.aom.base.service.part.impl.ActivityGroupService;
import com.yxt.aom.base.util.AomUtils;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.util.BeanCopierUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 活动参与小组component
 *
 * <AUTHOR>
 * @since 2024/12/12
 */
@Component
@RequiredArgsConstructor
public class PartGroupComponent {

    private final ActivityGroupService groupService;

    /**
     * 小组分页列表
     *
     * @param page   分页信息
     * @param orgId  机构id
     * @param search 入参信息
     * @param locale 多语言
     * @return 小组信息
     */
    public PagingList<GroupBeanRes> listPagePartMember(Page<GroupBeanRes> page, String orgId, GroupSearch search,
                   String locale) {
        IPage<GroupBeanRes> iPage = groupService.listPagePartGroup(page, orgId, search, locale);
        return BeanCopierUtil.toPagingList(iPage);
    }
}
