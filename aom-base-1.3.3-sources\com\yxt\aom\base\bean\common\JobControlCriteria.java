package com.yxt.aom.base.bean.common;

import com.yxt.common.enums.YesOrNo;
import lombok.Data;

import java.util.Set;

/**
 * JobControlCriteria
 */
@Data
public class JobControlCriteria {
    /**
     * 机构id
     */
    private String orgId;

    /**
     * 活动/项目id
     */
    private String actvId;

    /**
     * 任务类型(0-定时发布项目, 1-自动结束项目)
     */
    private Integer jobType;

    /**
     * 是否已执行(0-否, 1-是; 默认为0)
     */
    private Integer executed;

    /**
     * 是否启用(0-否, 1-是; 默认为0)
     */
    private Integer enabled;

    /**
     * 是否删除(0-否, 1-是; 默认为0)
     */
    private Integer deleted = YesOrNo.NO.getValue();

    /**
     * 活动/项目状态set(用于筛选活动/项目数据)
     */
    private Set<Integer> actvStatusSet;

    /**
     * 今天(格式为:yyyy-MM-dd)
     */
    private String today;

    /**
     * 指定查询的数据条数
     */
    private Integer limit;

    /**
     * 排序，默认 id ASC
     */
    private String orderBy;
}
