package com.yxt.aom.base.bean.part.honor;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @createDate 2025/5/26 11:35:57
 */
@Data
@Schema(description = "颁发荣誉请求参数")
public class ConferHonor4Req {

    @NotNull
    @Schema(description = "UACD注册表中定义Id")
    private String regId;
    @NotNull
    @Schema(description = "小组id")
    private long groupId;
    @NotNull
    @Schema(description = "荣誉id")
    private long honorId;
    @Schema(description = "是否分享群聊 默认0不分享 1分享")
    private Integer sharedIm = 0;
}
