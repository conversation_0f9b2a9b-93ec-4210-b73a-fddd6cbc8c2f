package com.yxt.aom.activity.handle;

import com.yxt.aom.base.bean.part.ActivityStatusChange4ProMsg;
import com.yxt.aom.base.enums.AomActivityStatusEnum;
import com.yxt.aom.base.wrapper.UacdWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class AomActivityStatusChange4ActvHandle {
    private final UacdWrapper uacdWrapper;

    //发布
    public void release(ActivityStatusChange4ProMsg msgBean) {
        String orgId = msgBean.getOrgId();
        Integer newStatus = msgBean.getNewStatus();
        List<String> actvIds = msgBean.getActvIds();
        String optUserId = msgBean.getOptUserId();
        actvIds.forEach(actvId -> {
            try {
                int uacdStatus = AomActivityStatusEnum.getUacdType(newStatus);
                uacdWrapper.arrangeStatusChange(orgId, optUserId, actvId, uacdStatus, msgBean.getParams());
            } catch (Exception e) {
                log.error("cycleChangeListener actv release message failed. actvId: {} , err: ", actvId, e);
            }
        });
    }

    //撤回
    public void withdraw(ActivityStatusChange4ProMsg msgBean) {
        String orgId = msgBean.getOrgId();
        Integer newStatus = msgBean.getNewStatus();
        List<String> actvIds = msgBean.getActvIds();
        String optUserId = msgBean.getOptUserId();
        actvIds.forEach(actvId -> {
            try {
                int uacdStatus = AomActivityStatusEnum.getUacdType(newStatus);
                uacdWrapper.arrangeStatusChange(orgId, optUserId, actvId, uacdStatus, msgBean.getParams());
            } catch (Exception e) {
                log.error("cycleChangeListener actv withdraw message failed. actvId: {} , err: ", actvId, e);
            }
        });
    }

    //结束
    public void end(ActivityStatusChange4ProMsg msgBean) {
        String orgId = msgBean.getOrgId();
        Integer newStatus = msgBean.getNewStatus();
        List<String> actvIds = msgBean.getActvIds();
        String optUserId = msgBean.getOptUserId();
        actvIds.forEach(actvId -> {
            try {
                int uacdStatus = AomActivityStatusEnum.getUacdType(newStatus);
                uacdWrapper.arrangeStatusChange(orgId, optUserId, actvId, uacdStatus, msgBean.getParams());
            } catch (Exception e) {
                log.error("cycleChangeListener actv end message failed. actvId: {} , err: ", actvId, e);
            }
        });
    }

    //归档
    public void archive(ActivityStatusChange4ProMsg msgBean) {
        String orgId = msgBean.getOrgId();
        Integer newStatus = msgBean.getNewStatus();
        List<String> actvIds = msgBean.getActvIds();
        String optUserId = msgBean.getOptUserId();
        actvIds.forEach(actvId -> {
            try {
                int uacdStatus = AomActivityStatusEnum.getUacdType(newStatus);
                uacdWrapper.arrangeStatusChange(orgId, optUserId, actvId, uacdStatus, msgBean.getParams());
            } catch (Exception e) {
                log.error("cycleChangeListener actv archive message failed. actvId: {} , err: ", actvId, e);
            }
        });

    }

    //删除
    public void del(ActivityStatusChange4ProMsg msgBean) {
        String orgId = msgBean.getOrgId();
        Integer newStatus = msgBean.getNewStatus();
        List<String> actvIds = msgBean.getActvIds();
        String optUserId = msgBean.getOptUserId();
        actvIds.forEach(actvId -> {
            try {
                int uacdStatus = AomActivityStatusEnum.getUacdType(newStatus);
                uacdWrapper.arrangeStatusChange(orgId, optUserId, actvId, uacdStatus, msgBean.getParams());
            } catch (Exception e) {
                log.error("cycleChangeListener actv del message failed. actvId: {} , err: ", actvId, e);
            }
        });
    }

}
