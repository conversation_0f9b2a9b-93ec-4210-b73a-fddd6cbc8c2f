package com.yxt.aom.base.cache;

import com.google.common.base.Preconditions;
import jodd.template.MapTemplateParser;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static java.util.concurrent.TimeUnit.SECONDS;

/**
 * <AUTHOR>
 * @date 2024/10/29  13:53
 * @description 描述
 */
public class AomCacheKey {

    private final String key;
    private final long expire;
    private final TimeUnit timeUnit;
    private final Map<String, Object> macroMap;

    private AomCacheKey(String key, long expire, Map<String, Object> macroMap) {
        this(key, expire, SECONDS, macroMap);
    }

    private AomCacheKey(String key, long expire, TimeUnit timeUnit, Map<String, Object> macroMap) {
        this.key = key;
        this.expire = expire;
        this.timeUnit = timeUnit == null ? SECONDS : timeUnit;
        this.macroMap = macroMap == null ? Collections.emptyMap() : Collections.unmodifiableMap(macroMap);
    }

    /**
     * Of cache key.
     *
     * @param key      the key
     * @param expire   the expire
     * @param macroMap the macro map
     * @return the cache key
     */
    public static AomCacheKey of(String key, long expire, Map<String, Object> macroMap) {
        return new AomCacheKey(key, expire, macroMap);
    }

    /**
     * Of cache key.
     *
     * @param key      the key
     * @param expire   the expire
     * @param unit     the unit
     * @param macroMap the macro map
     * @return the cache key
     */
    public static AomCacheKey of(String key, long expire, TimeUnit unit, Map<String, Object> macroMap) {
        return new AomCacheKey(key, expire, unit, macroMap);
    }

    /**
     * Key string.
     *
     * @return the string
     */
    public String key() {
        return this.key;
    }

    /**
     * Expire long.
     *
     * @return the long
     */
    public long expire() {
        return this.expire;
    }

    /**
     * Time unit time unit.
     *
     * @return the time unit
     */
    public TimeUnit timeUnit() {
        return this.timeUnit;
    }

    /**
     * Of string string.
     *
     * @return the string
     */
    public String ofString() {
        if (MapUtils.isEmpty(macroMap)) {
            return this.key;
        }
        return this.parse(this.key, macroMap);
    }

    private String parse(String template, Map<String, Object> macroMap) {
        Preconditions.checkArgument(StringUtils.isNotEmpty(template), "template");
        Map<String, Object> map = macroMap == null ? Collections.emptyMap() : macroMap;
        return new MapTemplateParser().of(map).parse(template);
    }

    @Override
    public String toString() {
        return ofString();
    }
}
