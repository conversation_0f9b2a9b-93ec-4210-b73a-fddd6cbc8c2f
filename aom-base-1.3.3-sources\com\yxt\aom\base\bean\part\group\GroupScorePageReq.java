package com.yxt.aom.base.bean.part.group;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @createDate 2025/5/27 10:33:04
 */
@Data
public class GroupScorePageReq {
    @NotNull
    @Schema(description = "活动id")
    private String actvId;
    @NotNull
    @Schema(description = "UACD注册表中定义Id")
    private String regId;
    @NotNull
    @Schema(description = "小组id")
    private String groupId;
}
