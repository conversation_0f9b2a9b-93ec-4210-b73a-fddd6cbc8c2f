package com.yxt.aom.base.component.common;

import com.google.common.collect.Lists;
import com.yxt.aom.base.entity.common.Activity;
import com.yxt.aom.base.mapper.arrange.ActivityArrangeItemMapper;
import com.yxt.aom.base.service.arrange.ArrangeSyncService;
import com.yxt.aom.base.service.common.ActivityService;
import com.yxt.aom.base.wrapper.UacdWrapper;
import com.yxt.aom.common.config.AomDataSourceTypeHolder;
import com.yxt.dbrouter.core.toolkit.DBRouteHolder;
import com.yxt.uacd.facade.bean.activity.ActivityArrangeItem;
import com.yxt.uacd.facade.bean.activity.ActivityInitFolderRequest;
import com.yxt.uacd.facade.bean.activity.ActivityItem4CreateRequest;
import com.yxt.uacd.facade.bean.activity.ActivityItem4UpdateRequest;
import com.yxt.uacd.facade.bean.activity.ActivityItemBatchBeanRequest;
import com.yxt.uacd.facade.bean.activity.ActivityItemMoveReq;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

/**
 * AomArrangeComponent
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AomArrangeComponent {
    private static final String REG_ID_MUST_NOT_BE_NULL = "Argument actvRegId must not be null.";
    private static final String BEAN_MUST_NOT_BE_NULL = "Argument bean must not be null.";
    private final ArrangeSyncService syncService;
    private final ActivityService activityService;
    private final ActivityArrangeItemMapper itemMapper;
    private final UacdWrapper uacdWrapper;

    /**
     * 初始化目录设置
     *
     * @param bean the bean
     * @return 默认阶段id
     */
    public Long initFolder(ActivityInitFolderRequest bean) {
        return uacdWrapper.initFolder(bean);
    }

    /**
     * 获取默认第一个阶段信息
     *
     * @param orgId  the org id
     * @param actvId the actv id
     * @return the default node
     */
    public ActivityArrangeItem getDefaultNode(String orgId, String actvId) {
        return uacdWrapper.getDefaultNode(orgId, actvId);
    }

    /**
     * 创建大纲节点
     *
     * @param actvRegId the actv reg id
     * @param bean      the bean
     * @return the activity arrange item
     */
    public ActivityArrangeItem createItem(String actvRegId, ActivityItem4CreateRequest bean) {
        Assert.notNull(actvRegId, REG_ID_MUST_NOT_BE_NULL);
        Assert.notNull(bean, BEAN_MUST_NOT_BE_NULL);
        AomDataSourceTypeHolder.set(actvRegId);
        DBRouteHolder.push(bean.getOrgId());
        Activity activity = activityService.requireAvailableActivity(bean.getOrgId(), bean.getActvId());
        ActivityArrangeItem item = uacdWrapper.createItem(bean);
        syncActivityArrangeItem(activity, item, bean.isSyncDraft());
        return item;
    }

    /**
     * 修改大纲节点
     *
     * @param actvRegId the actv reg id
     * @param bean      the bean
     * @return the activity arrange item
     */
    public ActivityArrangeItem updateItem(String actvRegId, ActivityItem4UpdateRequest bean) {
        Assert.notNull(actvRegId, REG_ID_MUST_NOT_BE_NULL);
        Assert.notNull(bean, BEAN_MUST_NOT_BE_NULL);
        AomDataSourceTypeHolder.set(actvRegId);
        DBRouteHolder.push(bean.getOrgId());
        Activity activity = activityService.requireAvailableActivity(bean.getOrgId(), bean.getActvId());
        ActivityArrangeItem item = uacdWrapper.updateItem(bean);
        syncActivityArrangeItem(activity, item, bean.isSyncDraft());
        return item;
    }

    /**
     * 批量删除大纲节点
     *
     * @param actvRegId the actv reg id
     * @param bean      the bean
     */
    public void deleteItems(String actvRegId, ActivityItemBatchBeanRequest bean) {
        Assert.notNull(actvRegId, REG_ID_MUST_NOT_BE_NULL);
        Assert.notNull(bean, BEAN_MUST_NOT_BE_NULL);
        if (CollectionUtils.isEmpty(bean.getIds())) {
            return;
        }

        AomDataSourceTypeHolder.set(actvRegId);
        DBRouteHolder.push(bean.getOrgId());
        Activity activity = activityService.requireAvailableActivity(bean.getOrgId(), bean.getActvId());
        uacdWrapper.deleteItems(bean);
        if (bean.isSyncDraft()) {
            syncService.syncActivityArrangeItem(activity, bean.getUserId(), bean.getIds());
        }
    }

    /**
     * 大纲节点移动排序
     *
     * @param bean the bean
     */
    public void moveItem(ActivityItemMoveReq bean) {
        if (bean == null) {
            return;
        }
        uacdWrapper.moveItem(bean);
    }

    /**
     * UACD大纲保存并下发
     *
     * @param orgId  the org id
     * @param userId the user id
     * @param actvId the actv id
     */
    public void releaseAllDrafts(String orgId, String userId, String actvId) {
        if (StringUtils.isBlank(orgId) || StringUtils.isBlank(userId) || StringUtils.isBlank(actvId)) {
            return;
        }
        uacdWrapper.release(orgId, userId, actvId);
    }

    private void syncActivityArrangeItem(Activity activity, ActivityArrangeItem item, boolean syncDraft) {
        if (syncDraft && item != null) {
            syncService.syncActivityArrangeItem(activity, item.getCreateUserId(), Lists.newArrayList(item),
                    () -> itemMapper.selectBatchIds(Lists.newArrayList(item.getId())));
        }
    }
}
