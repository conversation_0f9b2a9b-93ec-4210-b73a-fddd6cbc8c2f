package com.yxt.aom.base.bean.common;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 活动额外信息
 * @createDate 2025/5/22 16:21:18
 */
@Data
public class ActivityExt {
    /**
     * 群聊ID
     */
    private String imId;

    /**
     * 生态群聊OpenId
     */
    private String openId;

    /**
     * 是否包含个人积分标记(0不包含，1包含),存在o2o_project表的ext字段中
     * 2022 3.8 不显示小组积分的设置了，历史数据不动，新项目则默认都包含小组个人积分；--周瑜
     */
    private Integer hasPersonIntegral;

    /**
     * 开启多班次任务(0-关闭, 1-开启)
     */
    private Integer enableMultiShiftTasks;

    /**
     * 皮肤id
     */
    private String skinId;

    /**
     * 自定义json
     */
    private JSONObject custom;
}
