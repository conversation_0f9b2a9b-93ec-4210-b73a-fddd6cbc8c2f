package com.yxt.aom.base.common;

/**
 * BaseErrorConsts
 */
public class BaseErrorConsts {
    /**
     * The constant MISSING_PARAMS.
     */
    public static final String MISSING_PARAMS = "apis.aom.common.params.missing";
    /**
     * The constant ID_INVALID.
     */
    public static final String ID_INVALID = "apis.aom.common.id.invalid";
    /**
     * The constant REQUEST_EXPORT_TOO_FREQUENT.
     */
    public static final String REQUEST_EXPORT_TOO_FREQUENT = "apis.aom.common.request.export.too.frequent";
    /**
     * The constant MISSING_IMPLEMENT.
     */
    public static final String MISSING_IMPLEMENT = "apis.aom.custom.implement.missing";
    /**
     * The constant DESIGNER_SETTING_NOT_EXIST.
     */
    public static final String DESIGNER_SETTING_NOT_EXIST = "apis.aom.designer.setting.not.exist";
    /**
     * The constant ACTV_NAME_SIZE.
     */
    public static final String ACTV_NAME_SIZE = "apis.aom.activity.actvName.Size";
    /**
     * The constant ACTV_CODE_SIZE.
     */
    public static final String ACTV_CODE_SIZE = "apis.aom.activity.actvCode.Size";
    /**
     * The constant ACTV_TYPE_SCOPE.
     */
    public static final String ACTV_TYPE_SCOPE = "apis.aom.activity.actvType.IntegerScope";
    /**
     * The constant ACTV_TIMEMODEL_SCOPE.
     */
    public static final String ACTV_TIMEMODEL_SCOPE = "apis.aom.activity.timeModel.IntegerScope";
    /**
     * The constant ACTV_AUTOEND_SCOPE.
     */
    public static final String ACTV_AUTOEND_SCOPE = "apis.aom.activity.autoEnd.IntegerScope";
    /**
     * The constant ACTV_AUTOEND_SCOPE.
     */
    public static final String ACTV_AUTOARCHIVE_SCOPE = "apis.aom.activity.autoArchive.IntegerScope";
    /**
     * The constant ACTV_AUDITENABLED_SCOPE.
     */
    public static final String ACTV_AUDITENABLED_SCOPE = "apis.aom.activity.auditEnabled.IntegerScope";
    /**
     * The constant ACTV_VERYIMPORTANT_SCOPE.
     */
    public static final String ACTV_VERYIMPORTANT_SCOPE = "apis.aom.activity.veryImportant.IntegerScope";
    /**
     * The constant ACTV_PUBLICACTV_SCOPE.
     */
    public static final String ACTV_PUBLICACTV_SCOPE = "apis.aom.activity.publicActv.IntegerScope";
    /**
     * The constant ACTV_PROGRESSSYNC_SCOPE.
     */
    public static final String ACTV_PROGRESSSYNC_SCOPE = "apis.aom.activity.progressSync.IntegerScope";
    /**
     * The constant ACTV_USAGETYPE_SCOPE.
     */
    public static final String ACTV_USAGETYPE_SCOPE = "apis.aom.activity.usageType.IntegerScope";
    /**
     * The constant ACTV_SOURCETYPE_SCOPE.
     */
    public static final String ACTV_SOURCETYPE_SCOPE = "apis.aom.activity.sourceType.IntegerScope";
    /**
     * The constant ACTV_RANGE_SCOPE.
     */
    public static final String ACTV_RANGE_SCOPE = "apis.aom.activity.range.IntegerScope";
    /**
     * The constant ACTV_REWARDENTITYTYPE_SCOPE.
     */
    public static final String ACTV_REWARDENTITYTYPE_SCOPE = "apis.aom.activity.reward.entity.type.IntegerScope";
    /**
     * The constant ACTV_REWARDTYPE_SCOPE.
     */
    public static final String ACTV_REWARDTYPE_SCOPE = "apis.aom.activity.reward.type.IntegerScope";
    /**
     * The constant ACTV_IMAGEURL_SIZE.
     */
    public static final String ACTV_IMAGEURL_SIZE = "apis.aom.activity.imageUrl.Size";
    /**
     * The constant ACTV_DESC_SIZE.
     */
    public static final String ACTV_DESC_SIZE = "apis.aom.activity.description.Size";
    /**
     * The constant ACTV_REGID_INVALID.
     */
    public static final String ACTV_REGID_INVALID = "apis.aom.activity.actvRegId.Invalid";
    /**
     * The constant ACTV_NOT_EXIST.
     */
    public static final String ACTV_NOT_EXIST = "apis.aom.activity.not.exist";
    /**
     * The constant ACTV_IS_END.
     */
    public static final String ACTV_IS_END = "apis.aom.activity.is.end";
    /**
     * participation not exist
     */
    public static final String PART_NOT_EXIST = "apis.aom.activity.part.not.exist";
    /**
     * The constant ACTV_ORGANIZATION_NOT_EXIST.
     */
    public static final String ACTV_ORGANIZATION_NOT_EXIST = "apis.aom.activity.organization.not.exist";
    /**
     * The constant ITEM_NOT_EXIST.
     */
    public static final String ITEM_NOT_EXIST = "apis.aom.item.not.exist";
    /**
     * The constant EXCEL_FILE_PARSE_FAILED.
     */
    public static final String EXCEL_FILE_PARSE_FAILED = "apis.aom.import.file.parse.failed";
    /**
     * The constant EXCEL_FILE_NOT_FOUND.
     */
    public static final String EXCEL_FILE_NOT_FOUND = "apis.aom.import.file.not.found";
    /**
     * //操作频繁，60分钟内仅能做一次批量类型转换
     */
    public static final String AM_BATCH_FORMAL_CHANGE_ONE_HOUR = "apis.aom.am.batch.formal.change.one.hour";
    /**
     * //操作频繁，10分钟内单学员仅能做一次类型转换
     */
    public static final String AM_FORMAL_CHANGE_TEN_MIN = "apis.aom.am.formal.change.ten.min";
    /**
     * 操作间隔一分钟，请稍后再试
     */
    public static final String OPERATE_ONE_MIN = "apis.aom.common.operate.one.min";
    /**
     * 活动学员导出太过频繁
     */
    public static final String AM_REQUEST_TOO_FREQUENT = "apis.aom.member.request.too.frequent";
    /**
     * activity已归档
     */
    public static final String AOM_ACTIVITY_ARCHIVED = "apis.aom.activity.archived";
    /**
     * activity已结束
     */
    public static final String AOM_ACTIVITY_END = "apis.aom.activity.end";
    /**
     * activity未发布
     */
    public static final String AOM_ACTIVITY_NOT_RELEASED = "apis.aom.activity.not.released";
    /**
     * 学员不在项目中
     */
    public static final String AOM_MEMBER_NOTIN_ACTIVITY = "apis.aom.member.not.in.activity";
    /**
     * 未到活动开始时间
     */
    public static final String ERROR_MSG_NOT_START_TIME = "apis.aom.activity.not.start.time";
    /**
     * The constant ARRANGE_NOT_EXIST.
     */
    public static final String ARRANGE_NOT_EXIST = "apis.aom.arrange.not.exist";
    /**
     * 未找到活动参与人员信息
     */
    public static final String AOM_PART_MEMBER_NOT_FOUND = "apis.aom.part.member.not.found";
    /**
     * The constant GROUP_NAME_BLANK.
     */
    public static final String GROUP_NAME_BLANK = "apis.aom.part.group.name.is.blank";
    /**
     * The constant GROUP_NAME_SIZE.
     */
    public static final String GROUP_NAME_SIZE = "apis.aom.part.group.name.size";
    /**
     * The constant GROUP_ICON_SIZE.
     */
    public static final String GROUP_ICON_SIZE = "apis.aom.part.group.groupIcon.size";
    /**
     * The constant GROUP_INSTRUCTOR_SIZE.
     */
    public static final String GROUP_INSTRUCTOR_SIZE = "apis.aom.part.group.instructor.size";
    /**
     * The constant GROUP_NOTFOUND.
     */
    public static final String GROUP_NOTFOUND = "apis.aom.part.group.notFound";
    /**
     * The constant GROUP_ID_IS_NULL.
     */
    public static final String GROUP_ID_IS_NULL = "apis.aom.part.group.id.is.null";

    /**
     * The constant GROUP_IS_EMPTY.
     */
    public static final String GROUP_IS_EMPTY = "apis.aom.part.group.is.empty";
    /**
     * 称号不存在
     */
    public static final String TITLE_IS_NOT_EXIST = "apis.aom.part.title.notFound";
    /**
     * 称号名称已存在
     */
    public static final String TITLE_NAME_REPEAT = "apis.aom.part.title.name.repeat";

    /**
     * The constant GROUP_NO_LEADER.
     */
    public static final String GROUP_NO_LEADER = "apis.aom.part.group.no.leader";
    /**
     * The constant GROUP_NO_IM_OWNER.
     */
    public static final String GROUP_NO_IM_OWNER = "apis.aom.part.group.no.im.owner";

    /**
     * The constant GROUP_IM_OWNER_NOT_MEMBER.
     */
    public static final String GROUP_IM_OWNER_NOT_MEMBER = "apis.aom.part.group.im.owner.not.member";

    /**
     * 人员导入超过限额 5000
     */
    public static final String AOM_MEMBER_IMPORT_EXCEEDING_LIMIT = "apis.aom.part.member.import.exceeding.limit";

    /**
     * 已经发起过审核
     */
    public static final String AOM_AUDIT_EXEC_EXIST = "apis.aom.audit.exec.exist";

    /**
     * The constant AOM_ACTV_RELEASE_CHECK_NOTPASS.
     */
    public static final String AOM_ACTV_RELEASE_CHECK_NOTPASS = "apis.aom.activity.release.check.not.pass";

    /**
     * The constant HONOR_NOTFOUND.
     */
    public static final String HONOR_NOTFOUND = "apis.aom.honor.notFound";

    /**
     * The constant HONOR_NAME_EXIST.
     */
    public static final String HONOR_NAME_EXIST = "apis.aom.honor.name.exist";

    /**
     * The constant GROUPHONOR_NAME_EXIST.
     */
    public static final String GROUPHONOR_NAME_EXIST = "apis.aom.grouphonor.exist";

    /**
     * The constant ACTIVITY_NOT_OPEN_IM.
     */
    public static final String ACTIVITY_NOT_OPEN_IM = "apis.aom.activity.not.open.im";

    /**
     * The constant NO_IM_MEMBER.
     */
    public static final String NO_IM_MEMBER = "apis.aom.no.im.member";

    /**
     * 该功能需付费升级后使用，请联系云学堂工作人员进行升级
     */
    public static final String FACTOR_CHECK_NEED_UPGRADE = "apis.aom.common.factor.check.need.upgrade";

    /**
     * 该功能已被禁用，无法操作
     */
    public static final String FACTOR_CHECK_DISABLED = "apis.aom.common.factor.check.disabled";

    /**
     * 奖励配置不能为空
     */
    public static final String ACTV_REWARD_RULE_UPDATE_NOT_EMPTY = "apis.aom.activity.reward.rule.update.not.empty";

    /**
     * 小组积分记录不存在
     */
    public static final String ACTV_GROUP_SCORE_NOT_EXIST = "apis.aom.activity.group.score.not.exist";
    /**
     * 小组积分发放总积分不等于小组内学员积分之和
     */
    public static final String GROUP_SCORE_STUDENT_SUM_NO_EQUAL = "apis.aom.group.score.student.sum.no.equal";
    /**
     * 该组长不在小组内
     */
    public static final String GROUPMEMBER_OUTRANGE = "apis.aom.groupmember.outRange";
    /**
     * 小组内有学员不能删除小组
     */
    public static final String GROUP_IS_NOT_EMPTY = "apis.aom.group.is.not.empty";
    private BaseErrorConsts() {
    }

}

