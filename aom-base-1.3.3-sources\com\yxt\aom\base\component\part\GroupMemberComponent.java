package com.yxt.aom.base.component.part;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yxt.aom.base.bean.part.ActivityLastStudyTime;
import com.yxt.aom.base.bean.part.ActivityParticipationMember4Change;
import com.yxt.aom.base.bean.part.AomUserInfo;
import com.yxt.aom.base.bean.part.PartMemberIdUser;
import com.yxt.aom.base.bean.part.PartMemberPageCriteria;
import com.yxt.aom.base.bean.part.PartMemberReq;
import com.yxt.aom.base.bean.part.PartUserChangeBean;
import com.yxt.aom.base.bean.part.file.MemberImportResponse;
import com.yxt.aom.base.bean.part.group.GroupMember4List;
import com.yxt.aom.base.bean.part.group.GroupMemberImport4Req;
import com.yxt.aom.base.bean.part.group.GroupMemberImportBean;
import com.yxt.aom.base.bean.part.group.GroupMemberRemove4Req;
import com.yxt.aom.base.bean.part.group.GroupMemberSearch4Req;
import com.yxt.aom.base.component.common.AomNotifyComponent;
import com.yxt.aom.base.entity.common.Activity;
import com.yxt.aom.base.entity.part.ActivityParticipation;
import com.yxt.aom.base.entity.part.ActivityParticipationMember;
import com.yxt.aom.base.entity.part.ActivityParticipationMemberExt;
import com.yxt.aom.base.enums.ActivityTypeEnum;
import com.yxt.aom.base.enums.DesignerDataAuthorityEnum;
import com.yxt.aom.base.manager.common.AomDesignerManager;
import com.yxt.aom.base.manager.part.ParticipationMemberManager;
import com.yxt.aom.base.mapper.part.ActivityParticipationMemberExtMapper;
import com.yxt.aom.base.mapper.part.ActivityParticipationMemberMapper;
import com.yxt.aom.base.service.AomI18nService;
import com.yxt.aom.base.service.common.ActivityService;
import com.yxt.aom.base.service.export.GroupMemberErrorExportService;
import com.yxt.aom.base.service.part.impl.ActivityParticipationMemberService;
import com.yxt.aom.base.service.part.impl.ActivityParticipationService;
import com.yxt.aom.base.service.part.impl.GroupMemberService;
import com.yxt.aom.base.util.AomUtils;
import com.yxt.aom.base.wrapper.OrginitWrapper;
import com.yxt.aom.base.wrapper.TranslatorWrapper;
import com.yxt.common.exception.ApiException;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.service.AuthService;
import com.yxt.common.util.BatchOperationUtil;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.DateUtil;
import com.yxt.common.util.StreamUtil;
import com.yxt.leaf.service.SnowflakeKeyGenerator;
import com.yxt.udpfacade.bean.user.es.EsScopeBean;
import jodd.util.StringPool;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.yxt.aom.base.common.AomExportConstants.GROUP_MEMBER_IMPORT_KEYS;
import static com.yxt.aom.base.common.AomExportConstants.USER_DELETED;
import static com.yxt.aom.base.common.AomExportConstants.USER_DISABLE;
import static com.yxt.aom.base.common.AomExportConstants.USER_IS_NOT_ALLOW;
import static com.yxt.aom.base.common.AomExportConstants.USER_NAME_IS_NULL;
import static com.yxt.aom.base.common.AomMqConstants.TOPIC_ACTIVITY_MEMBER;
import static com.yxt.aom.base.common.BaseErrorConsts.ACTV_NOT_EXIST;
import static com.yxt.aom.base.common.BaseErrorConsts.PART_NOT_EXIST;
import static com.yxt.aom.base.common.DefaultValueConstants.Numbers.INT_0;
import static com.yxt.aom.base.common.DefaultValueConstants.Numbers.INT_1;
import static com.yxt.common.Constants.INT_1000;
import static com.yxt.common.Constants.INT_2;
import static com.yxt.common.enums.YesOrNo.NO;
import static com.yxt.common.enums.YesOrNo.YES;
import static java.util.stream.Collectors.toList;

/**
 * The type Group member component.
 *
 * <AUTHOR> @yxt.com
 * @description
 * @createDate 2024 /12/12 14:17:42
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class GroupMemberComponent {

    private final ActivityParticipationMemberMapper activityParticipationMemberMapper;
    private final OrginitWrapper orginitWrapper;
    private final TranslatorWrapper translator;
    private final AuthService authService;
    private final AomI18nService aomI18nService;
    private final GroupMemberService groupMemberService;
    private final ParticipationMemberManager partMemberManager;
    private final ActivityService activityService;
    private final AomDesignerManager aomDesignerManager;
    private final ActivityParticipationMemberService activityParticipationMemberService;
    private final GroupMemberErrorExportService groupMemberErrorExportService;
    private final ActivityParticipationMemberExtMapper activityParticipationMemberExtMapper;
    private final AomNotifyComponent aomNotifyComponent;
    private final ActivityParticipationService activityParticipationService;
    private final SnowflakeKeyGenerator snowflakeKeyGenerator;



    /**
     * Gets group members.
     *
     * @param page       the page
     * @param orgId      the org id
     * @param req        the req
     * @param sourceCode the source code
     * @return the group members
     */
    public PagingList<GroupMember4List> getGroupMembers(Page<GroupMember4List> page, String orgId,
            GroupMemberSearch4Req req, String sourceCode) {
        AomUtils.initDBRouteAndSource(orgId, req.getRegId());
        PartMemberPageCriteria criteria = getCriteria(orgId, req, sourceCode);
        IPage<GroupMember4List> result = activityParticipationMemberMapper.listPageByGroupId(page, orgId, criteria);
        if (CollectionUtils.isEmpty(result.getRecords())) {
            return BeanCopierUtil.toPagingList(result);
        }
        //多语言
        Locale locale = authService.getLocale();
        translator.translate(orgId, locale.toString(), GroupMember4List.class, result.getRecords());

        return BeanCopierUtil.toPagingList(result);
    }

    private PartMemberPageCriteria getCriteria(String orgId, GroupMemberSearch4Req req, String sourceCode) {
        PartMemberPageCriteria criteria = PartMemberPageCriteria.of().setOrgId(orgId).setGroupId(req.getGroupId())
                .setKeyword(req.getKeyword()).setFormal(req.getFormal());
        if (StringUtils.isNotEmpty(req.getKeyword())) {
            criteria.setKeyword(req.getKeyword());
        }
        // 企业微信环境下
        List<String> weixUserIds = orginitWrapper.contactOrgSearch(orgId, req.getKeyword(), sourceCode);
        if (CollectionUtils.isNotEmpty(weixUserIds)) {
            criteria.setWeixUserIds(weixUserIds.stream().distinct().toList());
        }

        return criteria;
    }

    /**
     * Remove group members.
     *
     * @param orgId     the org id
     * @param optUserId the opt user id
     * @param req       the req
     */
    public void removeGroupMembers(String orgId, String optUserId, GroupMemberRemove4Req req) {
        AomUtils.initDBRouteAndSource(orgId, req.getRegId());
        if (CollectionUtils.isEmpty(req.getUserIds())) {
            return;
        }
        PartUserChangeBean changeBean = PartUserChangeBean.builder().orgId(orgId).actvId(req.getActvId())
                .actvType(ActivityTypeEnum.PROJ.getType()).participationId(req.getParticipationId())
                .regId(req.getRegId()).userIds(req.getUserIds()).groupId(0L).optUserId(optUserId)
                .currentTime(new Date()).build();

        groupMemberService.removeGroupMembers(changeBean);

    }

    /**
     * Move group members.
     *
     * @param orgId     the org id
     * @param optUserId the user id
     * @param req       the req
     */
    public void moveGroupMembers(String orgId, String optUserId, GroupMemberRemove4Req req) {
        AomUtils.initDBRouteAndSource(orgId, req.getRegId());
        if (CollectionUtils.isEmpty(req.getUserIds())) {
            return;
        }
        PartUserChangeBean changeBean = PartUserChangeBean.builder().orgId(orgId).actvId(req.getActvId())
                .actvType(ActivityTypeEnum.PROJ.getType()).participationId(req.getParticipationId())
                .regId(req.getRegId()).userIds(req.getUserIds()).groupId(req.getGroupId()).optUserId(optUserId)
                .currentTime(new Date()).build();

        groupMemberService.moveGroupMembers(changeBean);
    }

    /**
     * Import group members member import response.
     *
     * @param userInfo the user info
     * @param req      the req
     * @param datas    the datas
     * @return the member import response
     */
    public MemberImportResponse importGroupMembers(UserCacheDetail userInfo, GroupMemberImport4Req req,
            List<GroupMemberImportBean> datas) {

        // 根据账号查询
        List<AomUserInfo> excelUsers = partMemberManager.listUserInfoByUserName(userInfo.getOrgId(),
                        datas.stream().map(GroupMemberImportBean::getUserName).filter(StringUtils::isNotEmpty)
                                .collect(Collectors.toList())).stream().filter(user -> user.getDeleted() == NO.getValue())
                .collect(Collectors.toList());
        Map<String, AomUserInfo> excelUserNameMap = StreamUtil.list2map(excelUsers, AomUserInfo::getUserNameLowerCase);

        Activity activity = activityService.requireAvailableActivity(userInfo.getOrgId(), req.getActvId());

        // 查询数据权限
        EsScopeBean scopeBean = aomDesignerManager.getUserDataPermission(userInfo, activity.getActvRegId(),
                DesignerDataAuthorityEnum.TRAINEE_ADD_EXTENT, StreamUtil.mapList(excelUsers, AomUserInfo::getUserId));

        List<String> userNames;
        if (!scopeBean.isAllPermission()) {
            userNames = excelUsers.stream().filter(t -> scopeBean.getDatas().contains(t.getUserId()))
                    .map(AomUserInfo::getUsername).distinct().collect(Collectors.toList());
        } else {
            userNames = Lists.newArrayList();
        }

        List<ActivityParticipationMember> insertMembers = Lists.newArrayList();
        List<GroupMemberImportBean> errorBeans = Lists.newArrayList();

        Map<String, String> i18nMap = aomI18nService.getI18nMap(GROUP_MEMBER_IMPORT_KEYS);
        Map<String, Long> addGroups = Maps.newHashMap();
        Date now = DateUtil.currentTime();
        for (GroupMemberImportBean groupMemberImportBean : datas) {
            // 处理errorBeans
            if (!buildErrorBeans(groupMemberImportBean, errorBeans, userNames, excelUserNameMap, scopeBean, i18nMap)) {
                log.info("project id is {}, fail member is {}", req.getActvId(),
                        JSON.toJSONString(groupMemberImportBean));
                continue;
            }
            AomUserInfo udpUser = excelUserNameMap.get(groupMemberImportBean.getUserName().toLowerCase());

            String uId = udpUser.getUserId();
            ActivityParticipationMember groupMember = partMemberManager.generatePartMember(userInfo.getOrgId(),
                    req.getActvId(), req.getParticipationId(), uId, req.getFormal(), INT_1, userInfo.getUserId(),
                    req.getGroupId(), now, null);
            insertMembers.add(groupMember);
        }
        // 调用保存加人公共接口
        ActivityParticipationMember4Change bean = ActivityParticipationMember4Change.builder()
                .orgId(userInfo.getOrgId()).actvId(req.getActvId()).regId(req.getRegId())
                .participationId(req.getParticipationId()).actvType(activity.getActvType())
                .optUserId(userInfo.getUserId()).retryTopic(TOPIC_ACTIVITY_MEMBER).build();
        activityParticipationMemberService.addMemberV2(insertMembers, bean);
        // 异常数据导出
        groupMemberErrorExportService.export(userInfo, errorBeans);
        // 封装response
        return MemberImportResponse.builder().failCount(errorBeans.size()).successCount(insertMembers.size()).build();
    }

    private boolean buildErrorBeans(GroupMemberImportBean memberImportBean, List<GroupMemberImportBean> errorBeans,
            List<String> userNames, Map<String, AomUserInfo> excelUserNameMap, EsScopeBean scopeBean,
            Map<String, String> i18nMap) {
        String userName = memberImportBean.getUserName();
        String fullName = memberImportBean.getUserFullName();
        // 帐号必填
        if (StringUtils.isBlank(userName)) {
            errorBeans.add(new GroupMemberImportBean(userName, fullName, i18nMap.get(USER_NAME_IS_NULL)));
            return false;
        }
        // 是否存在学员并且是否被删除
        AomUserInfo udpUser = excelUserNameMap.get(StringUtils.lowerCase(userName));
        if (udpUser == null || udpUser.getDeleted() == YES.getValue()) {
            errorBeans.add(new GroupMemberImportBean(userName, fullName, i18nMap.get(USER_DELETED)));
            return false;
        }
        if (udpUser.getStatus() == INT_0) {
            errorBeans.add(new GroupMemberImportBean(userName, fullName, i18nMap.get(USER_DISABLE)));
            return false;
        }
        //.不可导入不在自己数据权限范围内的账号
        if (!scopeBean.isAllPermission() && !userNames.contains(memberImportBean.getUserName())) {
            errorBeans.add(new GroupMemberImportBean(userName, fullName, i18nMap.get(USER_IS_NOT_ALLOW)));
            return false;
        }
        return true;
    }


    public Map<String, Date> getMaxLastStudyTimeMap(String orgId, Collection<String> actvIds) {
        Map<String, Date> result = Maps.newHashMap();
        List<ActivityLastStudyTime> activityLastStudyTimes = BatchOperationUtil.batchQuery(new ArrayList<>(actvIds),
                x -> activityParticipationMemberMapper.getActivityLastStudyTime(orgId, x));
        for (ActivityLastStudyTime activityLastStudyTime : activityLastStudyTimes) {
            result.put(activityLastStudyTime.getActvId(), activityLastStudyTime.getLastStudyTime());
        }
        return result;
    }


    public List<String> batchGraduatedStudent(UserCacheDetail userCache, PartMemberReq req, boolean isCancel) {

        String orgId = userCache.getOrgId();
        String actvId = req.getActvId();

        Activity activity = activityService.findById(orgId, req.getActvId());
        if (activity == null) {
            throw new ApiException(ACTV_NOT_EXIST);
        }

        Long partId = req.getParticipationId();
        ActivityParticipation participation = activityParticipationService.getActivityPart(orgId, partId);
        if (!Objects.equals(participation.getActvId(), activity.getId())){
            throw new ApiException(PART_NOT_EXIST);
        }

        List<PartMemberIdUser> members = filterGraduatedPartMembers(userCache, req, orgId, actvId, partId, isCancel);
        if(CollectionUtils.isEmpty(members)) {
            return Lists.newArrayList();
        }

        List<Long> ids = members.stream().map(PartMemberIdUser::getId).toList();
        BatchOperationUtil.batchExecute(ids, INT_1000, x -> {
            if (!isCancel){
                activityParticipationMemberMapper.updateGraduatedByIds(INT_1, userCache.getUserId(), x);
            } else {
                activityParticipationMemberMapper.updateGraduatedByIds(INT_2, userCache.getUserId(), x);
            }});

        return members.stream().map(PartMemberIdUser::getUserId).distinct().collect(toList());
    }

    @Nullable
    private List<PartMemberIdUser> filterGraduatedPartMembers(UserCacheDetail userCache, PartMemberReq req, String orgId,
            String actvId, Long partId, boolean isCancel) {
        List<PartMemberIdUser> allUserIds = Lists.newArrayList();
        //选择全部
        if (req.isCheckAll()) {
            // 毕业
            if (!isCancel){
                // 筛选条件为已毕业的则直接返回（已毕业学员无需再次毕业）
                if (req.getGraduated() != null && req.getGraduated() == INT_1){
                    return Lists.newArrayList();
                }
                req.setGraduated(INT_0);
            } else {
                // 取消毕业
                // 筛选条件为未毕业的则直接返回（未毕业学员无需取消）
                if (req.getGraduated() != null && req.getGraduated() == INT_0){
                    return Lists.newArrayList();
                }
                req.setGraduated(INT_1);
            }

            allUserIds = activityParticipationMemberService.listPartMemberIdUser(orgId, actvId, req, userCache.getSourceCode());

            if (CollectionUtils.isNotEmpty(allUserIds) && CollectionUtils.isNotEmpty(req.getExcludeUserIds())) {
                allUserIds = allUserIds.stream().filter(e -> !req.getExcludeUserIds().contains(e.getUserId())).collect(toList());
            }
        } else if (CollectionUtils.isNotEmpty(req.getUserIds())){

            if (!isCancel){
                allUserIds = activityParticipationMemberMapper.listPactUserIdByGraduated(orgId, actvId, partId, req.getUserIds(), INT_0);
            } else {
                allUserIds = activityParticipationMemberMapper.listPactUserIdByGraduated(orgId, actvId, partId, req.getUserIds(), INT_1);
            }
        }
        return allUserIds;
    }

    @NotNull
    private ActivityParticipationMemberExt initMemberExt(String orgId, Long partId, String actvId,
            String userId, String optUserId, Date optTime) {
        ActivityParticipationMemberExt memberExt = new ActivityParticipationMemberExt();
        memberExt.setId(snowflakeKeyGenerator.generateKey());
        memberExt.setOrgId(orgId);
        memberExt.setActvId(actvId);
        memberExt.setParticipationId(partId);
        memberExt.setUserId(userId);

        memberExt.setActivityScore(new BigDecimal(-1));
        memberExt.setActivityComment(StringPool.EMPTY);
        memberExt.setActivityScoreTime(null);
        memberExt.setPassed(-1);
        memberExt.setGraduated(INT_0);
        memberExt.setDeleted(INT_0);

        memberExt.setCreateUserId(optUserId);
        memberExt.setCreateTime(optTime);
        memberExt.setUpdateUserId(optUserId);
        memberExt.setUpdateTime(optTime);
        return memberExt;
    }
}
