package com.yxt.aom.base.bean.part;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.google.common.collect.Lists;
import com.yxt.aom.base.common.AomI18nConstants;
import com.yxt.bifrost.udp.localization.annotation.L10NProperty;
import com.yxt.bifrost.udp.localization.enums.ShapeEnum;
import com.yxt.common.Constants;
import com.yxt.common.annotation.timezone.DateFormatField;
import com.yxt.common.enums.YesOrNo;
import com.yxt.common.util.DateUtil;
import com.yxt.udpfacade.bean.enums.ResourceTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jodd.util.StringPool;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.*;

import static com.google.common.collect.Lists.newArrayList;
import static com.yxt.aom.base.common.AomPropConstants.*;
import static com.yxt.aom.base.common.DefaultValueConstants.DOUBLE_HYPHEN;
import static com.yxt.common.Constants.*;

/**
 * <AUTHOR>
 * @since 2024/11/7
 */
@Getter
@Setter
@NoArgsConstructor
public class PartMemberExportBean {

    public static final List<String> STUDENT_LIST_HEADERS = Collections.unmodifiableList(
            newArrayList(LOWER_FULL_NAME, LOWER_USER_NAME, USER_STATUS, FORMAL_NAME,
                    ALL_PROGRESS, REQUIRED_PROGRESS,
                    DEPT_NAME, POSITION_LIST, MOBILE, STATUS_NAME, OVERDUE_STATUS,
                    START_TIME_STR, LAST_STUDY_TIME_STR, JOIN_METHOD_STR, JOIN_TIME_STR,
                    HIRE_DATE_STR, USER_NO, PROJECT_END_TIME));

    @Schema(description = "用户id", example = "1210805511352545282")
    @L10NProperty(resourceType = ResourceTypeEnum.USER, shape = ShapeEnum.KEY)
    private String userId;

    @Schema(description = "账号", example = "sok")
    private String username;

    @Schema(description = "姓名", example = "张三")
    @L10NProperty(resourceType = ResourceTypeEnum.USER, shape = ShapeEnum.VALUE)
    private String fullname;

    @Schema(description = "视图用户状态；0：已禁用；1：启用")
    private Integer status;
    @Schema(description = "视图用户删除状态；0：正常；1：已删除；")
    private Integer deleted;
    /**
     * 列表用户状态；0：正常；1：已禁用；2：已删除
     */
    private String userStatus;

    @Schema(description = "1正式学员 0旁听学员", example = "1")
    private Integer formal;
    private String formalName;

    @Schema(description = "授予称号")
    private String title;

    @L10NProperty(resourceType = ResourceTypeEnum.DEPT, shape = ShapeEnum.KEY)
    private String deptId;
    @Schema(description = "部门", example = "测试部门")
    @L10NProperty(resourceType = ResourceTypeEnum.DEPT, shape = ShapeEnum.VALUE)
    private String deptName;

    @L10NProperty(resourceType = ResourceTypeEnum.POSITION, shape = ShapeEnum.KEY)
    private String positionId;
    @Schema(description = "岗位", example = "测试岗位")
    @L10NProperty(resourceType = ResourceTypeEnum.POSITION, shape = ShapeEnum.VALUE)
    private String positionList;

    @Schema(description = "总体进度，项目内全部任务包含带教的进度，形如：3/10")
    private String allProgress;

    @Schema(description = "必修进度，形如：3/10")
    private String requiredProgress;

    @Schema(description = "选修进度，形如：3/10，无选修显示--")
    private String electiveProgress;

    @Schema(description = "带教进度，形如：3/10,无带教显示--")
    private String ojtProgress;

    @Schema(description = "完成状态", title = "进度为0%时，为未开始；进度大于0小于100%时，为进行中；进度等于100%，为已完成；" +
            "学员的学习结束时间过了，再完成时记为逾期", example = "2022-10-09 00:00:00")
    private String statusName;

    @Schema(description = "证书")
    private int certificateNum;

    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "学分", example = "1000")
    private BigDecimal credit;

    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "积分", example = "50")
    private BigDecimal integral;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = SDF_YEAR2SECOND, timezone = "GMT+8")
    @Schema(description = "学员学习开始时间", example = "2021-08-08 17:11:58")
    private Date startTime;
    private String startTimeStr;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = SDF_YEAR2SECOND, timezone = "GMT+8")
    @Schema(description = "最近学习时间", example = "2021-08-08 17:11:58")
    private Date lastStudyTime;
    private String lastStudyTimeStr;

    @Schema(description = "是否合格", example = "1")
    private int passed;
    private String passedName;

    @Schema(description = "项目成绩", example = "123")
    private double projectScore;
    private String projectScoreStr;

    @Schema(description = "项目评价", example = "123")
    private String projectComments;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = SDF_YEAR2DAY, timezone = "GMT+8")
    @Schema(description = "入职时间", example = "2021-08-08")
    private Date hireDate;
    private String hireDateStr;

    @Schema(description = "工号", example = "sok")
    private String userNo;

    @Schema(description = "座位号", example = "17")
    private String seatNo = "-1";
    private String seatNoStr;

    @Schema(description = "小组id", example = "1210805511352545282")
    @JsonSerialize(using = ToStringSerializer.class)
    private long groupId;
    @Schema(description = "小组名", example = "测试小组")
    private String groupName;

    @Schema(description = "辅导员名字,以逗号隔开", example = "张三,李四,王五")
    private String tutorNames;

    @Schema(description = "加入方式（0：Unknown（默认）；1：管理员手动加入；2：自动加入；3：通过报名加入；5：通过动态用户组加入）")
    private Integer joinMethod;
    private String joinMethodStr;

    @Schema(description = "加入时间", example = "2022-10-01 00:00:00")
    @DateFormatField(format = SDF_YEAR2MINUTE,isDate = true)
    private Date joinTime;
    private String joinTimeStr;

    @Schema(description = "学员项目结束时间", example = "2022-10-09 00:00:00")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = Constants.SDF_YEAR2SECOND, timezone = Constants.STR_GMT8)
    @DateTimeFormat(pattern = Constants.SDF_YEAR2SECOND)
    private Date endTime;
    private String projectEndTime;





    @Schema(description = "电话", example = "13878232412")
    private String mobile;

    @Schema(description = "任务完成数量", example = "5")
    private int completeCount;

    @Schema(description = "是否优秀学员 0：不是 1：是 ", example = "0")
    private int outstanding;

    @Schema(description = "组长 0：不是 1：是 ", example = "0")
    private int leader;

    @Schema(description = "项目任务数", example = "10")
    private int taskCount;

    @Schema(description = "头像")
    private String avatarUrl;

    @Schema(description = "是否是讲师；0：否；1：是；")
    private Integer wasTeacher = YesOrNo.NO.getValue();

    @Schema(description = "国籍")
    private String nationality;

    @Schema(description = "完成所有任务的时间", example = "2022-10-09 00:00:00")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = Constants.SDF_YEAR2SECOND, timezone = Constants.STR_GMT8)
    @DateTimeFormat(pattern = Constants.SDF_YEAR2SECOND)
    private Date finishedTime;

    @Schema(description = "是否逾期,0 未逾期,1逾期", example = "1")
    private Integer overdue = INT_0;

    /**
     * 是否逾期字符串
     */
    private String overdueStatus;

    @Schema(description = "所属主平台名称")
    private String mainOrgName;

    @Schema(description = "所属主平台ID")
    private String mainOrgId;

    @Schema(description = "职级", example = "P1")
    private String gradeName;

    @Schema(description = "学员收到项目时间", example = "2022-10-09 00:00:00")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = Constants.SDF_YEAR2SECOND, timezone = Constants.STR_GMT8)
    @DateTimeFormat(pattern = Constants.SDF_YEAR2SECOND)
    private Date acceptedTime;

    @Schema(description = "第一次学习项目时间", example = "2022-10-09 00:00:00")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = Constants.SDF_YEAR2SECOND, timezone = Constants.STR_GMT8)
    @DateTimeFormat(pattern = Constants.SDF_YEAR2SECOND)
    private Date firstStudyTime;

    @Schema(description = "选修任务+必修任务完成数量", example = "10")
    private Integer completeAllTaskCount;

    @Schema(description = "选修任务+必修任务总数量", example = "30")
    private Integer allTaskCount;

    @Schema(description = "必修任务完成数量", example = "10")
    private Integer completeRequiredCount;

    @Schema(description = "必修任务总数量", example = "30")
    private Integer requiredTaskCount;

    @Schema(description = "完成标准 默认0 完成所有必修 1完成所有任务 2完成项目内指定任务数量 3完成项目内指定阶段数量 4获得项目内指定学分")
    private Integer studyStandard;

    @Schema(description = "必修完成率")
    private BigDecimal requiredProcessRate;

    @Schema(description = "全部完成率")
    private BigDecimal allProcessRate;

    @Schema(description = "带教任务完成数")
    private Integer ojtCompleteAllCount;

    @Schema(description = "带教任务总数")
    private Integer ojtAllTotalCount;

    @Schema(description = "选修任务数")
    private Integer electiveCount;

    @Schema(description = "选修完成数")
    private Integer completeElectiveCount;

    @Schema(description = "项目完成进度")
    private BigDecimal completeProcessRate;

    @Schema(description = "0-未完成，1-进行中，2-已完成 3-已逾期")
    private int completeStatus;

    @Schema(description = "项目完成时间", example = "2022-10-09 00:00:00")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = Constants.SDF_YEAR2SECOND, timezone = Constants.STR_GMT8)
    @DateTimeFormat(pattern = Constants.SDF_YEAR2SECOND)
    private Date completeTime;

    private Integer completePeriodCount;

    @Schema(description = "整体进度，根据完成标准做呈现")
    private String fullProgress;

    /**
     * 项目下阶段数量
     */
    private int periodCount = 0;

    /**
     * 已获得学分
     */
    private BigDecimal completeStudyScore;

    @Schema(description = "活动完成率")
    private BigDecimal actCompletedRate;
    private String actCompletedRateStr;

    @Schema(description = "必修活动完成率")
    private BigDecimal requiredTaskCompletedRate;
    private String requiredTaskCompletedRateStr;

    @Schema(description = "选修活动完成进度")
    private BigDecimal electiveTaskCompletedRate;
    private String electiveTaskCompletedRateStr;


    public void initExport(Map<String, String> columnMap) {
        formalName = StringPool.DASH;
        if (formal == YesOrNo.YES.getValue()) {
            formalName = columnMap.get(AomI18nConstants.MemberColumn.FORMAL);
        } else if (formal == YesOrNo.NO.getValue()) {
            formalName = columnMap.get(AomI18nConstants.MemberColumn.IN_FORMAL);
        }

        passedName = StringPool.DASH;
        if (passed == YesOrNo.YES.getValue()) {
            passedName = columnMap.get(AomI18nConstants.TaskResultColumn.PASS_PASS);
        } else if (passed == YesOrNo.NO.getValue()) {
            passedName = columnMap.get(AomI18nConstants.TaskResultColumn.PASS_UN_PASS);
        }

        projectScoreStr = projectScore + "";
        if (projectScore == INT_NEGATIVE_1) {
            projectScoreStr = StringPool.DASH;
        }

        seatNoStr = seatNo;
        if (StringUtils.isBlank(seatNo) || StringUtils.equals(seatNo, String.valueOf(INT_NEGATIVE_1))) {
            seatNoStr = StringPool.DASH;
        }

        if(StringUtils.isBlank(groupName)){
            groupName = StringPool.DASH + StringPool.DASH;
        }
        // 用户账号状态
        initUserStatus(columnMap);
        // 加入方式
        initJoinMethodStr(columnMap);

        allProgress = completeCount + StringPool.SLASH + taskCount;

//        if (null != allTaskCount && null != requiredTaskCount) {
//            electiveCount = allTaskCount - requiredTaskCount > 0 ? allTaskCount - requiredTaskCount : INT_0;
//        }
//        if (null != completeAllTaskCount && null != completeRequiredCount) {
//            completeElectiveCount =
//                    completeAllTaskCount - completeRequiredCount > 0 ? completeAllTaskCount - completeRequiredCount : INT_0;
//        }

        initProgress();
    }

    private void initUserStatus(Map<String, String> columnMap) {
        if (deleted == null || deleted == INT_1) {
            //用户状态已删除
            userStatus = columnMap.get(AomI18nConstants.MemberColumn.STATUS_DELETED);
        } else {
            //用户状态为 已禁用
            if (status == INT_0) {
                //用户状态已禁用
                userStatus = columnMap.get(AomI18nConstants.MemberColumn.STATUS_DISABLED);
            } else {
                //用户状态已启用
                userStatus = columnMap.get(AomI18nConstants.MemberColumn.STATUS_ENABLED);
            }
        }
    }

    private void initJoinMethodStr(Map<String, String> columnMap) {
        if (null != joinMethod) {
            if (joinMethod == INT_1) {
                joinMethodStr = columnMap.get(AomI18nConstants.MemberColumn.JOINMETHOD_BY_ADMIN);
            } else if (joinMethod == INT_2) {
                joinMethodStr = columnMap.get(AomI18nConstants.MemberColumn.JOINMETHOD_AUTO_JOIN);
            } else if (joinMethod == INT_3) {
                joinMethodStr = columnMap.get(AomI18nConstants.MemberColumn.JOINMETHOD_BY_ENROLL);
            } else if (joinMethod == INT_4) {
                joinMethodStr = columnMap.get(AomI18nConstants.MemberColumn.JOINMETHOD_BY_SIGN);
            } else if (joinMethod == INT_5) {
                joinMethodStr = columnMap.get(AomI18nConstants.MemberColumn.JOINMETHOD_DYNAMIC_GROUP);
            } else {
                joinMethodStr = StringPool.DASH;
            }
        } else {
            joinMethodStr = StringPool.DASH;
        }
    }

    private void initProgress() {
        //必修进度
        if (null != requiredTaskCount && 0 != requiredTaskCount) {
            requiredProgress = completeRequiredCount + StringPool.SLASH + requiredTaskCount;
        } else {
            requiredProgress = "0/0";
        }
        //选修进度
        if (null != electiveCount && 0 != electiveCount) {
            electiveProgress = completeElectiveCount + StringPool.SLASH + electiveCount;
        } else {
            electiveProgress = "0/0";
        }
        //带教进度
        if (null != ojtAllTotalCount && 0 != ojtAllTotalCount) {
            ojtProgress = ojtCompleteAllCount + StringPool.SLASH + ojtAllTotalCount;
        } else {
            ojtProgress = "0/0";
        }
    }

    public void wrapMultiLanguage(Map<String, String> columnMap) {
        this.joinTimeStr = Optional.ofNullable(this.joinTime).map(DateUtil::formatDate).orElse(DOUBLE_HYPHEN);
        if (this.overdue == INT_0) {
            this.overdueStatus = columnMap.get(AomI18nConstants.MemberColumn.NOT_OVERDUE);
        } else {
            this.overdueStatus = columnMap.get(AomI18nConstants.MemberColumn.HAS_OVERDUE);
        }

        if (this.completeStatus == INT_0) {
            this.statusName = columnMap.get(AomI18nConstants.MemberColumn.STATUS_NOTSTARTED);
        } else if (completeStatus == INT_1) {
            this.statusName = columnMap.get(AomI18nConstants.MemberColumn.STATUS_UNDERWAY);
        } else if (completeStatus == INT_2) {
            this.statusName = columnMap.get(AomI18nConstants.MemberColumn.STATUS_COMPLETED);
        } else if (completeStatus == INT_3){
            this.statusName = columnMap.get(AomI18nConstants.MemberColumn.STATUS_HAS_DELAY);
        } else if (completeStatus == INT_4) {
            this.statusName = columnMap.get(AomI18nConstants.MemberColumn.STATUS_DELAY_COMPLETED);
        }
    }

    public static List<String> getI18nKey() {
        List<String> keys = Lists.newArrayList();
        keys.addAll(AomI18nConstants.MemberColumn.KEYS());
        keys.addAll(AomI18nConstants.TaskResultColumn.KEYS());
        return keys;
    }

}
