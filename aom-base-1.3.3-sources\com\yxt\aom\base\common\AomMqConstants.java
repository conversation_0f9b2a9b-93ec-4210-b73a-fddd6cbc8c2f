package com.yxt.aom.base.common;

/**
 * <AUTHOR>
 * @date 2024/10/29  20:33
 * @description desc
 * <p></p>
 * topic 使用两个字母来区分此topic是监听还是发送
 * _C_ :代表我们是监听方，监听了这个topic
 * _S_ ：代表我们是发送方，会向这个topic中推消息
 * _SC_ ：代表我们既是发送方，又是监听方，一般用于mq解耦，削峰
 */
public final class AomMqConstants {
    private AomMqConstants() {
        throw new UnsupportedOperationException();
    }

    /**
     * 同步redis中item的完成数至数据库mq
     */
    public static final String TOPIC_ITEM_COMPLETE_COUNT_SYNC = "aom-item-complete-count-sync";

    /**
     * 对账redis中item的完成数
     */
    public static final String TOPIC_ITEM_COMPLETE_COUNT_CHECK = "aom-item-complete-count-check";

    /**
     * 阶段item完成数计算mq
     */
    public static final String TOPIC_ITEM_FOLDER_COMPLETE_COUNT_CALC = "aom-item-folder-complete-count-calc";

    /**
     * 计算学员进度mq(学员端)
     */
    public static final String TOPIC_MEMBER_STATISTICS_CALC = "aom-member-statistics-calc";

    /**
     * 批量计算学员进度mq（批量，一般是管理端触发）
     * 1、管理端变更任务
     * 2、管理端批量新增学员
     *
     */
    public static final String TOPIC_MEMBER_STATISTICS_CALC_BATCH = "aom-member-statistics-calc-batch";

    /**
     * 刷新item和学员统计表数据
     */
    public static final String TOPIC_REFRESH_STATISTICS = "aom-refresh-statistics";

    /**
     * 推送学员统计变更mq
     */
    public static final String TOPIC_MEMBER_STATISTICS_CHANGE= "aom-member-statistics-change";

    /**
     * 项目添加人员
     */
    public static final String TOPIC_ACTIVITY_MEMBER = "aom-activity-member";

    /**
     * 项目添加人员通知第三方
     */
    public static final String TOPIC_ACTIVITY_MEMBER_ADD_THIRD = "activity-member-add-third";

    /**
     * 项目开始结束时间变更刷活动数据
     */
    public static final String TOPIC_ACTIVITY_MEMBER_TIME_REFRESH_THIRD = "activity-member-time-refresh-third";

    /**
     * 项目人员信息变更通知第三方
     */
    public static final String TOPIC_ACTIVITY_MEMBER_CHANGE_THIRD = "activity-member-change-third";

    /**
     * 参与人员类型变更topic
     */
    public static final String TOPIC_SC_MEMBER_FORMAL_CHANGE = "aom-participation-member-formal-change";

    /**
     * 参与人员删除topic
     */
    public static final String TOPIC_SC_MEMBER_DELETE = "aom-participation-member-delete";

    /**
     * 负责人通知topic
     */
    public static final String TOPIC_SC_PRINCIPAL_NOTICE = "aom-activity-principal-notice";

    /**
     * activity 结果状态变更topic 目前只支持多主题活动
     */
    public static final String TOPIC_AOM_ACTV_RESULT_STATUS_CHANGE = "aom-activity-result-status-change";

    public static final String TOPIC_S_O2O_USER_PROGRESS_TO_SP = "o2o_user_progress_to_sp";

    /**
     * 活动名称变更topic
     */
    public static final String TOPIC_AOM_ACTIVITY_NAMECHANGED = "aom-activity-namechanged";


    /**
     * 活动时间变化通知
     */
    public static final String TOPIC_AOM_ACTIVITY_TIME_CYCLE_CHANGED = "aom-activity-time-cycle-changed";


    /**
     * 活动状态变更（只给项目使用）
     */
    public static final String TOPIC_AOM_ACTIVITY_STATUS_CHANGED_FOR_PROJECT = "aom-activity-status-changed-project";

    public static final String TOPIC_AOM_ACTIVITY_STATUS_CHANGED_FOR_ACTV = "aom-activity-status-changed-actv";

    /**
     * 项目结束后自动归档（只给项目使用）
     */
    public static final String TOPIC_AOM_ACTIVITY_AUTO_ARCHIVE_PROJECT = "aom-activity-auto-archive-project";


    public static final String TOPIC_AOM_ACTIVITY_MEMBER_CHANGED_HANDLE_FOR_PROJECT = "aom-activity-member-changed-handle-project";

    /**
     * 人员列表导出topic
     */
    public static final String TOPIC_C_AOM_ACTIVITY_MEMBER_EXPORT = "aom-user-list-export";

    /**
     * 概览进度导出topic
     */
    public static final String TOPIC_C_AOM_RPT_SCHEDULE_EXPORT = "aom-rpt-schedule-export";

    /**
     * 项目发布时-批量推送学员消息
     */
    public static final String TOPIC_AOM_SEND_RELEASE_STUDENT_MSG = "aom-project-release-msg-student";

    /**
     * 项目删除时-批量推送学员消息
     */
    public static final String TOPIC_AOM_SEND_DELETE_STUDENT_MSG = "aom-project-delete-msg-student";

    /**
     * 项目撤回时-批量推送学员消息
     */
    public static final String TOPIC_AOM_SEND_WITHDRAW_STUDENT_MSG = "aom-project-withdraw-msg-student";

    /**
     * 审核回调
     */
    public static final String AUDIT_COMPLETED_TOPIC = "topic-audit-result";

    /**
     * The constant 自动结束项目管理员提醒处理mq
     */
    public static final String TOPIC_AOM_PROJECT_END_AUTO_REMINDER = "aom-project-end-auto-reminder";

    /**
     * 报名回调-开发阶段先随机写一个
     */
    public static final String TOPIC_ENROLL_EVENT_DEVELOP_V1 = "enrc-event-develop-v1";


    public static final String TOPIC_AOM_ENROLL_EVENT= "enrc-event";

    /**
     * 当用户组发生变更通知项目负责人关联的动态用户组
     */
    public static final String TOPIC_AOM_PRINCIPALS_GROUP_USER_RELATION_CHANGE = "aom-principals-group-user-relation-change";

    /**
     * demo复制活动结果topic
     */
    public static final String TOPIC_AOM_DEMOCOPY_ACTIVITY_RESULT = "aom-democopy-activity-result";
}
