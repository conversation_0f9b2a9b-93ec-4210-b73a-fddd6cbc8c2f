package com.yxt.aom.base.bean.common;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.yxt.common.annotation.timezone.DateFormatField;
import com.yxt.common.pojo.IdName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * Activity4Get
 */
@Data
public class Activity4Audit {
    /**
     * 主键id
     */
    @JsonIgnore
    private String id;

    /**
     * 活动/项目名称
     */
    @Schema(description = "活动/项目名称", example = "项目名称test")
    private String actvName;

    /**
     * 类型(1-活动, 2-项目)
     */
    @Schema(description = "类型(1-活动, 2-项目)", example = "2")
    private Integer actvType;

    /**
     * 状态(0-未保存, 1-未发布, 2-进行中, 3-已结束, 4-归档中, 41-已归档, 42-归档超过一年, 5-已删除, 6-已暂停, 7-已撤回)
     */
    @Schema(description = "状态(0-未保存, 1-未发布, 2-进行中, 3-已结束, 4-归档中, 41-已归档, 42-归档超过一年, 5-已删除, 6-已暂停, 7-已撤回)", example = "2")
    private Integer actvStatus;

    /**
     * 时间模式(0-固定, 1-相对)
     */
    @Schema(description = "时间模式(0-固定, 1-相对)", example = "0")
    private Integer timeModel;

    /**
     * 固定开始时间
     */
    @Schema(description = "固定开始时间", example = "2024-10-25 10:56:47")
    @DateFormatField(isDate = true)
    private Date startTime;

    /**
     * 固定截止时间
     */
    @Schema(description = "固定截止时间", example = "2024-11-25 10:56:47")
    @DateFormatField(isDate = true)
    private Date endTime;

    /**
     * 相对开始天数
     */
    @Schema(description = "相对开始天数", example = "0")
    private Integer startDayOffset;

    /**
     * 相对截止天数
     */
    @Schema(description = "相对截止天数", example = "7")
    private Integer endDayOffset;

    /**
     * 封面
     */
    @Schema(description = "封面", example = "https://stc.yxt.com/ufd/55a3e0/o2o/pc/other/project.png")
    private String imageUrl;

    /**
     * 简介
     */
    @Schema(description = "简介", example = "简介test")
    private String description;

    /**
     * 活动/项目的具体类型(UACD注册表中定义)
     */
    @Schema(description = "活动/项目的具体类型(UACD注册表中定义)", example = "proj_o2o")
    private String actvRegId;

    /**
     * UACD设计器id
     */
    @Schema(description = "UACD设计器id", example = "1849276367002220000")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long designerId;


    /**
     * 模型id
     */
    @Schema(description = "模型id", example = "ac0a267c-044e-4a28-b489-6be16e0ab781")
    private String modelId;

    /**
     * 场景id
     */
    @Schema(description = "场景id", example = "ac0a267c-044e-4a28-b489-6be16e0ab783")
    private String sceneId;

    /**
     * 分类id
     */
    @Schema(description = "分类id", example = "1849276367002220000")
    private String categoryId;

    @Schema(description = "分类名称")
    private String categoryName;

    @Schema(description = "正式学员人数")
    private Long memberCount;

    @Schema(description = "创建人姓名")
    private String createUserName;
    /**
     * 负责人id
     */

    /**
     * 是否自动结束(0-否, 1-是; 默认为0)
     */
    @Schema(description = "是否自动结束(0-否, 1-是; 默认为0)", example = "0")
    private Integer autoEnd;

    /**
     * 是否自动归档(0-否, 1-是; 默认为0)
     */
    @Schema(description = "是否自动归档(0-否, 1-是; 默认为0)", example = "0")
    private Integer autoArchive;

    /**
     * 是否开启审核(0-未开启, 1-开启; 默认为0)
     */
    @Schema(description = "是否开启审核(0-未开启, 1-开启; 默认为0)", example = "0")
    private Integer auditEnabled;

    /**
     * 审核状态(0-待审核, 1-审核中, 2-已通过, 3-未通过, 4-已撤回; 默认为0)
     */
    @Schema(description = "审核状态(0-待审核, 1-审核中, 2-已通过, 3-未通过, 4-已撤回; 默认为0)", example = "0")
    private Integer auditStatus;


    @Schema(description = "创建人id", example = "00f5760d-f921-424f-adaa-6a94b812baab")
    private String createUserId;


    @Schema(description = "创建时间", example = "2024-11-25 10:56:47")
    @DateFormatField(isDate = true)
    private Date createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间", example = "2024-11-25 10:56:47")
    @DateFormatField(isDate = true)
    private Date updateTime;


    /**
     * 参与ID
     */
    @Schema(description = "参与ID", example = "1849276367002220000")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long participationId;

    /**
     * 负责人列表
     */
    @Schema(description = "负责人列表")
    private List<IdName> mgrs;
    @Schema(description = "h5地址")
    private String h5Url;
    @Schema(description = "pc地址")
    private String pcUrl;
}
