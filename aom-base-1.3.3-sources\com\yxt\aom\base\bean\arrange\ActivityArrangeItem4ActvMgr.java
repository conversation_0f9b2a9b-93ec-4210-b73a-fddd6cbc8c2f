package com.yxt.aom.base.bean.arrange;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.yxt.aom.base.bean.common.ActivityRewardRuleInfoMgr;
import com.yxt.common.annotation.timezone.DateFormatField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * ActivityArrangeItem4ActvMgr
 */
@Data
@Schema(name = "活动管理-活动节点")
public class ActivityArrangeItem4ActvMgr {
    /**
     * 主键id
     */
    @Schema(description = "节点id", example = "1849276367003750001")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 机构id
     */
    @Schema(description = "机构id", example = "ac0a267c-044e-4a28-b489-6be16e0ab788")
    private String orgId;

    /**
     * 活动/项目id
     */
    @Schema(description = "活动/项目id", example = "1849276367003750000")
    private String actvId;

    /**
     * 活动/项目OrganizationID
     */
    @Schema(description = "活动/项目OrganizationID", example = "1849276367003220000")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long actvOrgId;

    /**
     * 上一级节点id
     */
    @Schema(description = "上一级节点id", example = "1849276367004880001")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long parentId;

    /**
     * 叶节点引用对象的具体类型(UACD注册表中定义)
     */
    @Schema(description = "叶节点引用对象的具体类型(UACD注册表中定义)", example = "actv_exam")
    private String refRegId;

    /**
     * 叶节点引用对象的id
     */
    @Schema(description = "叶节点引用对象的id", example = "1849276367003750001")
    private String refId;

    /**
     * 叶子节点引用对象的名称
     */
    @Schema(description = "叶子节点引用对象的名称", example = "考试test1")
    private String refName;

    /**
     * 业务自定义别名
     */
    @Schema(description = "业务自定义别名", example = "考试test1")
    private String itemName;

    /**
     * 节点类型(0-叶节点(任务), 1-目录节点-章节/阶段, 2-目录节点-任务组)
     */
    @Schema(description = "节点类型(0-叶节点(任务), 1-目录节点-章节/阶段, 2-目录节点-任务组)", example = "0")
    private Integer itemType;

    /**
     * 类型国际化key
     */
    @Schema(description = "类型国际化key", example = "pc_ulcd_default_catalog_name")
    private String typeNameKey;

    /**
     * 活动任务类型自定义别名
     */
    @Schema(description = "活动任务类型自定义别名", example = "外链课2")
    private String actvAlias;

    /**
     * 节点层级(从1开始)
     */
    @Schema(description = "节点层级(从1开始)", example = "1")
    private Integer nodeLevel;

    /**
     * 节点描述
     */
    @Schema(description = "节点描述", example = "节点描述test")
    private String description;

    /**
     * 是否必修(0-否, 1-是)
     */
    @Schema(description = "是否必修(0-否, 1-是)", example = "1")
    private Integer required;

    /**
     * 是否锁定：0-否，1-是
     */
    @Schema(description = "是否锁定：0-否，1-是", example = "0")
    private Integer locked;

    /**
     * 是否隐藏：0-否，1-是
     */
    @Schema(description = "是否隐藏：0-否，1-是", example = "0")
    private Integer hidden;

    /**
     * 排序
     */
    @Schema(description = "排序", example = "1")
    private Integer orderIndex;

    /**
     * 学习时长
     */
    @Schema(description = "学习时长", example = "1")
    private Integer studyHours;

    /**
     * 业务扩展json
     */
    @Schema(description = "业务扩展json")
    private String ext;

    /**
     * 时间模式(0-固定, 1-相对)
     */
    @Schema(description = "时间模式(0-固定, 1-相对)", example = "0")
    private Integer timeModel;

    /**
     * 固定开始时间
     */
    @Schema(description = "固定开始时间", example = "2024-10-25 10:56:47")
    @DateFormatField(isDate = true)
    private Date startTime;

    /**
     * 固定截止时间
     */
    @Schema(description = "固定截止时间", example = "2024-11-25 10:56:47")
    @DateFormatField(isDate = true)
    private Date endTime;

    /**
     * 相对开始天数
     */
    @Schema(description = "相对开始天数", example = "0")
    private Integer startDayOffset;

    /**
     * 相对截止天数
     */
    @Schema(description = "相对截止天数", example = "7")
    private Integer endDayOffset;

    /**
     * 数据状态(1-页面通过校验, 2-同步成功, 3-同步失败, 4-未同步; 默认为1)
     */
    @Schema(description = "数据状态(1-页面通过校验, 2-同步成功, 3-同步失败, 4-未同步; 默认为1)", example = "2")
    private Integer itemStatus;

    /**
     * 是否删除(0-未删除, 1-已删除)
     */
    @JsonIgnore
    private Integer deleted;

    /**
     * 创建人id
     */
    @Schema(description = "创建人id", example = "00f5760d-f921-424f-adaa-6a94b812baab")
    private String createUserId;

    /**
     * 更新人id
     */
    @Schema(description = "更新人id", example = "00f5760d-f921-424f-adaa-6a94b812baab")
    private String updateUserId;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2024-10-25 10:56:47")
    @DateFormatField(isDate = true)
    private Date createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间", example = "2024-10-25 10:56:47")
    @DateFormatField(isDate = true)
    private Date updateTime;

    /**
     * 逾期时间
     */
    @Schema(description = "逾期时间", example = "2024-11-25 10:56:47")
    @DateFormatField(isDate = true)
    private Date overdueTime;

    /**
     * 学员完成数
     */
    @Schema(description = "学员完成数", example = "33")
    private Integer completeCount;

    /**
     * 参与总学员数
     */
    @Schema(description = "参与总学员数", example = "50")
    private Integer totalCount;

    /**
     * 任务完成进度
     */
    @Schema(description = "任务完成进度", example = "90.00")
    private BigDecimal passRate;

    @Schema(description = "奖励规则信息", example = "90.00")
    private List<ActivityRewardRuleInfoMgr> rewardRuleInfo;

    /**
     * 子节点列表
     */
    @Schema(description = "子节点列表")
    @DateFormatField(isobj = true)
    private List<ActivityArrangeItem4ActvMgr> children = new ArrayList<>();
}
