package com.yxt.aom.base.bean.part;

import com.yxt.common.Constants;
import com.yxt.common.annotation.timezone.DateFormatField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/12/27 14:18
 */
@Getter
@Setter
public class PartMember4Delete {

    @Schema(description = "参与id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long participationId;

    @Schema(description = "userNames", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> userNames;

    @Schema(description = "userIds", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> userIds;

    @Schema(description = "是否全选",example = "true")
    private boolean checkAll = false;

    @Schema(description = "1正式学员 0旁听学员", example = "1")
    private Integer formal;

    @Schema(description = "关键字")
    private String keyword;

    @Schema(description = "账号状态账号状态 0-禁用 1-启用 2-删除 不传取所有", example = "1")
    private Integer status;

    @Schema(description = "部门id")
    private List<String> deptIds;

    @Schema(description = "直属经理id")
    private List<String> managerIds;

    @Schema(description = "岗位id")
    private List<String> positionIds;

    @Schema(description = "排除学员id")
    private List<String> excludeUserIds = Collections.emptyList();

    @Schema(description = "开始学习开始时间", example = "2019-10-08")
    @DateFormatField(format = Constants.SDF_YEAR2DAY)
    private String startTime;

    @Schema(description = "开始学习结束时间", example = "2019-10-08")
    @DateFormatField(format = Constants.SDF_YEAR2DAY)
    private String endTime;

    @Schema(description = "是否发送消息 false：不发 true 发")
    private boolean sendMsg = Boolean.FALSE;

    @Schema(description = "完成状态 0未开始、1学习中、2已完成", example = "1")
    private Integer studyStatus;

}

