package com.yxt.aom.base.bean.part.ext;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 用户扩展信息
 *
 * <AUTHOR>
 * @since 2025/5/29
 */
@Getter
@Setter
@NoArgsConstructor
public class MemberExtBean {

    /**
     * 学员id
     */
    private String userId;

    /**
     * 学员活动得分
     */
    private BigDecimal activityScore;

    /**
     * 学员活动评语
     */
    private String activityComment;

    /**
     * 打分时间
     */
    private Date activityScoreTime;

    /**
     * 是否通过(0-否 1-是 -1-未提交)
     */
    private Integer passed;

    /**
     * 是否毕业 默认0未毕业 1毕业 2取消毕业
     */
    private Integer graduated;

}
