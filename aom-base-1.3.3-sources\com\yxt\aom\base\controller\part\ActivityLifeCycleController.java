package com.yxt.aom.base.controller.part;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yxt.aom.base.bean.part.cycle.AomActivityArchiveReq;
import com.yxt.aom.base.bean.part.cycle.AomActivityDelReq;
import com.yxt.aom.base.bean.part.cycle.AomActivityEndReq;
import com.yxt.aom.base.bean.part.cycle.AomActivityReleaseReq;
import com.yxt.aom.base.bean.part.cycle.AomActivityWthdrawReq;
import com.yxt.aom.base.service.part.ActivityLifeCycleService;
import com.yxt.common.Constants;
import com.yxt.common.annotation.Auth;
import com.yxt.common.annotation.DbHintMaster;
import com.yxt.common.enums.AuthType;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.service.AuthService;
import com.yxt.common.util.ApiUtil;
import com.yxt.dbrouter.core.toolkit.DBRouteHolder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

import static com.yxt.aom.base.common.AomPropConstants.TOKEN;
import static com.yxt.aom.base.common.DefaultValueConstants.Numbers.INT_0;

/**
 * 活动生命周期
 */
@Tag(name = "活动生命周期")
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/activity/lifeCycle")
public class ActivityLifeCycleController {

    private final AuthService authService;
    private final ActivityLifeCycleService activityLifeCycleService;
    //发布

    @Operation(summary = "发布项目")
    @ResponseStatus(HttpStatus.OK)
    @PutMapping(value = "/release", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @Auth(action = Constants.LOG_TYPE_UPDATESINGLE, type = {AuthType.DETAILTOKEN})
    @DbHintMaster
    public void releaseActivity(@RequestBody @Valid AomActivityReleaseReq bean) {
        UserCacheBasic user = authService.getUserCacheBasic();
        DBRouteHolder.push(user.getOrgId());
        bean.setOrgId(user.getOrgId());
        bean.setOptUserId(user.getUserId());
        log.info("releaseActivity req:{}", JSON.toJSONString(bean));
        activityLifeCycleService.release(bean);

    }

    //撤回
    @Operation(summary = "撤回项目")
    @ResponseStatus(HttpStatus.OK)
    @PostMapping(value = "/withdraw")
    @Auth(action = Constants.LOG_TYPE_UPDATESINGLE, type = {AuthType.DETAILTOKEN})
    @DbHintMaster
    public void withdrawActivity(@RequestParam("actvId") String actvId, @RequestParam("regId") String regId) {
        UserCacheBasic user = authService.getUserCacheBasic();
        DBRouteHolder.push(user.getOrgId());
        AomActivityWthdrawReq activityWthdrawReq = new AomActivityWthdrawReq();
        activityWthdrawReq.setActvIds(Lists.newArrayList(actvId));
        activityWthdrawReq.setRegId(regId);
        activityWthdrawReq.setOrgId(user.getOrgId());
        activityWthdrawReq.setOptUserId(user.getUserId());
        log.info("withdrawActivity req:{}", JSON.toJSONString(activityWthdrawReq));
        activityLifeCycleService.withdraw(activityWthdrawReq);
    }

    //结束
    @Operation(summary = "结束项目")
    @ResponseStatus(HttpStatus.OK)
    @PostMapping(value = "/end")
    @Auth(action = Constants.LOG_TYPE_UPDATESINGLE, type = {AuthType.DETAILTOKEN})
    @DbHintMaster
    public void endActivity(HttpServletRequest request,
            @RequestParam("actvId") String actvId, @RequestParam("regId") String regId) {
        UserCacheDetail user = authService.getUserCacheDetail();
        DBRouteHolder.push(user.getOrgId());
        AomActivityEndReq activityEnd = new AomActivityEndReq();
        activityEnd.setActvIds(Lists.newArrayList(actvId));
        activityEnd.setRegId(regId);
        activityEnd.setOrgId(user.getOrgId());
        activityEnd.setOptUserId(user.getUserId());
        activityEnd.setAutoEnd(INT_0);
        activityEnd.setSource(user.getSource());
        Map<String, Object> params = new HashMap<>();
        String token = ApiUtil.getToken(request);
        params.put(TOKEN, token);
        activityEnd.setParams(params);
        log.info("endActivity req:{}", JSON.toJSONString(activityEnd));
        activityLifeCycleService.end(activityEnd);
    }

    // 归档
    @Operation(summary = "归档活动")
    @ResponseStatus(HttpStatus.OK)
    @PostMapping(value = "/archive")
    @Auth(action = Constants.LOG_TYPE_UPDATESINGLE, type = {AuthType.DETAILTOKEN})
    @DbHintMaster
    public void archiveActivity(HttpServletRequest request, @RequestParam("actvId") String actvId,
            @RequestParam("regId") String regId) {
        UserCacheDetail user = authService.getUserCacheDetail();
        DBRouteHolder.push(user.getOrgId());
        log.info("archiveActivity actvId={} userId={}", actvId, user.getUserId());
        if (StringUtils.isBlank(actvId)) {
            return;
        }
        AomActivityArchiveReq activityArchiveReq = new AomActivityArchiveReq();
        activityArchiveReq.setActvIds(Lists.newArrayList(actvId));
        activityArchiveReq.setRegId(regId);
        activityArchiveReq.setOrgId(user.getOrgId());
        activityArchiveReq.setOptUserId(user.getUserId());
        activityArchiveReq.setSource(user.getSource());
        log.info("archiveActivity req:{}", JSON.toJSONString(activityArchiveReq));
        activityLifeCycleService.archive(activityArchiveReq);
    }

    //删除
    @Operation(summary = "删除活动")
    @ResponseStatus(HttpStatus.OK)
    @PostMapping(value = "/del")
    @Auth(action = Constants.LOG_TYPE_UPDATESINGLE, type = {AuthType.DETAILTOKEN})
    @DbHintMaster
    public void delActivity(HttpServletRequest request, @RequestParam("actvId") String actvId,
            @RequestParam("regId") String regId) {
        UserCacheDetail user = authService.getUserCacheDetail();
        DBRouteHolder.push(user.getOrgId());
        log.info("delActivity actvId={} userId={}", actvId, user.getUserId());
        if (StringUtils.isBlank(actvId)) {
            return;
        }
        AomActivityDelReq activityDelReq = new AomActivityDelReq();
        activityDelReq.setActvIds(Lists.newArrayList(actvId));
        activityDelReq.setRegId(regId);
        activityDelReq.setOrgId(user.getOrgId());
        activityDelReq.setOptUserId(user.getUserId());
        activityDelReq.setSource(user.getSource());
        log.info("delActivity req:{}", JSON.toJSONString(activityDelReq));
        activityLifeCycleService.del(activityDelReq);
    }


}
