package com.yxt.aom.activity.service.sco;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import com.yxt.aom.activity.custom.SingleActivityCompo;
import com.yxt.aom.base.entity.common.ActivitySource;
import com.yxt.aom.base.manager.arrange.ArrangeManager;
import com.yxt.aom.base.mapper.common.ActivitySourceMapper;
import com.yxt.aom.common.config.AomDbProperties;
import com.yxt.aom.common.util.AomBeanNameUtils;
import com.yxt.aom.datamodel.common.TargetObject;
import com.yxt.aom.datamodel.event.ScoResultEvent;
import com.yxt.aom.datamodel.scoresult.BaseScoResult;
import com.yxt.common.enums.YesOrNo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Service("aomScoResultService")
@RequiredArgsConstructor
@Slf4j
public class ScoResultService {

    private final ActivitySourceMapper activitySourceMapper;
    private final AomDbProperties aomDbProperties;
    private final ArrangeManager arrangeManager;

    public void consumeScoResult(ScoResultEvent<BaseScoResult> event ,String messageBody,String currentRegId) {

        Map<String, String> dsMap = aomDbProperties.getDsmap();
        if (MapUtils.isEmpty(dsMap)) {
            log.info("ScoResultService consumeScoResult dsMap is empty");
            return;
        }
        TargetObject targetObject = event.getTargetObject();
        Set<String> dsSet = dsMap.keySet();
        Set<String> sourceRegIdsSet = Sets.newHashSet(targetObject.getSourceRegIds() == null ? Sets.newHashSet() : targetObject.getSourceRegIds());
        sourceRegIdsSet.add(targetObject.getSourceType());
        if(CollectionUtils.isEmpty(Sets.intersection(dsSet,sourceRegIdsSet))) {
            log.info("ScoResultService consumeScoResult dsSet : {} , sourceRegIdsSet : {} " , JSON.toJSONString(dsSet) ,JSON.toJSONString(sourceRegIdsSet));
            return;
        }

        //插槽逻辑
        SingleActivityCompo singleActivityCompo = AomBeanNameUtils.getCustomBean(SingleActivityCompo.class,
                currentRegId);
        if (singleActivityCompo != null) {
            singleActivityCompo.scoResultCallBack(event,messageBody);
            log.info("ScoResultService consumeScoResult scoResultCallBack call back success");
        }

    }


    private void syncKng(ScoResultEvent<BaseScoResult> event ,String messageBody,String currentRegId) {
        BaseScoResult baseScoResult = event.getGenerated();
        TargetObject targetObject = event.getTargetObject();
        //
        Set<String> actvIds = arrangeManager.listActvIdsByRefId(baseScoResult.getOrgId(),targetObject.getTargetId());
        //当前sourceId不需要处理
        actvIds.remove(targetObject.getSourceId());
        if(CollectionUtils.isEmpty(actvIds)) {
            return;
        }
        //todo
        //查询同步项目引用 并且调用kng sco 同步进度接口
        List<ActivitySource> activitySources = activitySourceMapper.listByActIds(baseScoResult.getOrgId(), actvIds);
        //同步进度的项目数据
        activitySources = activitySources.stream().filter(activitySource -> Objects.equals(activitySource.getSync(), YesOrNo.YES.getValue())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(activitySources)){
            return;
        }
        //调用kng sco 同步进度接口
        activitySources.forEach(activitySource -> {

            //todo 调用kng sco 同步进度接口
        });

    }

}
