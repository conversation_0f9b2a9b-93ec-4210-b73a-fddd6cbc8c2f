package com.yxt.aom.base.bean.control;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/10/30  18:49
 * @description 描述
 */
@Getter
@Setter
@Schema(name = "获取控制台链接")
public class ConsoleUrlReq {

    @Schema(description = "活动Id")
    private String actvId;

    @Schema(description = "itemId")
    private Long itemId;

    @Schema(description = "查询类型(1-活动, 2-项目)")
    private Integer actvType;

    @Schema(description = "其他参数", example = "{\"{{itemId}}\":\"123\"}")
    private Map<String, String> params;

}
