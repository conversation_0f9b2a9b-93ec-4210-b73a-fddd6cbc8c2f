package com.yxt.aom.base.bean.control;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class SchedulePageByUserRequest {

    @Schema(description = "必修/选修，0-必修、1-选修、null-不限")
    private Integer required;

    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "小组id")
    private Long groupId;

    @Schema(description = "部门id")
    private List<String> deptIds;

    @Schema(description = "关键字搜素")
    private String keyword;

    @Schema(description = "任务筛选")
    private List<ScheduleTaskFilter> taskFilters;

    @Schema(description = "人员状态：0:禁用，1:启用 2:删除，不传为全部")
    private Integer status;
}
