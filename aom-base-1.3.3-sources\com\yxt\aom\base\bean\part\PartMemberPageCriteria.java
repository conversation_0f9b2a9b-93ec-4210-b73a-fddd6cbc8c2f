package com.yxt.aom.base.bean.part;

import com.google.common.collect.Maps;
import com.yxt.aom.base.bean.order.OrderField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 *
 *
 * <AUTHOR> shan
 * @since 2021/9/15 15:35
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
public class PartMemberPageCriteria {

    private String orgId;
    /**
     * 参与id
     */
    private Long participationId;
    /**
     * 活动id
     */
    private String actvId;
    private Long groupId;
    private String keyword;
    private Integer searchType;
    private String userName;
    private String fullName;
    private Integer status;
    /**
     * '完成标准 默认0 完成所有必修 1完成所有任务 2完成项目内指定任务数量 3完成项目内指定阶段数量 4获得项目内指定学分
     */
    private Integer studyStandard;
    private List<String> weixUserIds;
    private List<String> deptIds;
    private List<String> positionIds;
    /**
     * 职级id列表
     */
    private List<String> gradeIds;
    /**
     * 1账号删除,0未删除
     */
    private Integer deleted;
    private List<String> managerIds;
    /**
     * 项目集下查询需要
     */
    private List<Long> projectIds;

    /**
     * 1正式学员 0旁听学员 -1 取全部
     */
    private Integer formal;

    /**
     * 开始学习开始时间
     */
    private String startTime;

    /**
     * 开始学习结束时间
     */
    private String endTime;

    /**
     * 分页
     */
    private Long from;

    private Long size;

    /**
     * true 走 lite_group表
     */
    private boolean isLiteGroup = false;

    /**
     * 类型 0项目 1项目模板 2:集团化项目
     */
    private Integer type;

    private List<String> userIds;

    private List<String> excludeUserIds;

    /**
     * 开始学习开始时间
     */
    private String joinStartTime;

    /**
     * 开始学习结束时间
     */
    private String joinEndTime;

     /**
     * 学习状态 0-未开始 1-进行中 2-已完成
     */
    private Integer studyStatus;

    @Schema(description = "完成状态集合 0-未开始 1-进行中 2-已完成")
    private List<Integer> studyStatusList;

    /**
     * 是否逾期 0-未逾期 1-已逾期
     */
    private Integer overdue;

    /**
     * 项目模式 起止时间模式 还是周期模式
     */
    private Integer timeType;

    /**
     * 项目结束时间
     */
    private Date projectEndTime;

    private Map<String, String> columnMap = Maps.newHashMap();

    /**
     * 账号状态 0-已禁用 1-已启用 2-已删除
     */
    private Integer accountStatus;

    /**
     * 排序字段列表
     */
    private List<OrderField> orderFields;

    private String actvName;

    private String operatorId;

    private Locale locale;

    /**
     * 查询时的时间
     */
    private String currentTime;

    /**
     * 毕业状态(0-未毕业 1-已毕业 2-取消毕业)，2也属于未毕业
     */
    private Integer graduated;

    /**
     * 活动注册id
     */
    private String regId;

    public PartMemberPageCriteria() {
        // NOSONAR
    }

    public static PartMemberPageCriteria of() {
        return new PartMemberPageCriteria();
    }

}
