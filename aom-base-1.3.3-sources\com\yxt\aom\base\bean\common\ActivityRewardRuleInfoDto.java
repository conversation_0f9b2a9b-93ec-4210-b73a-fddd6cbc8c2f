package com.yxt.aom.base.bean.common;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/5/27  20:04
 * @description 描述
 */
@Data
public class ActivityRewardRuleInfoDto {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 来源id
     */
    private String sourceId;

    /**
     * 来源id
     */
    private Integer sourceType;

    /**
     * 业务id
     */
    private String serviceId;

    /**
     * 业务类型
     */
    private Integer serviceType;

    /**
     * 动作类型
     */
    private Integer actionType;

    /**
     * 奖励规则tag
     */
    private String tag;

    /**
     * 奖励规则
     */
    private String ruleValue;

    /**
     * 奖励配置id
     */
    private Long rewardConfigId;

    /**
     * 积分
     */
    private Integer point;

    /**
     * 学分
     */
    private BigDecimal credit;

}
