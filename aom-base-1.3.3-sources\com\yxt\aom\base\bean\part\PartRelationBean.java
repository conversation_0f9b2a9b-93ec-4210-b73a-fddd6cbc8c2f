package com.yxt.aom.base.bean.part;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * 参与关系bean
 *
 * <AUTHOR>
 * @since 2024/10/25
 */
@Getter
@Setter
@NoArgsConstructor
@SuperBuilder
public class PartRelationBean {

    /**
     * 活动id
     */
    private String actvId;

    /**
     * 活动注册id
     */
    private String regId;
    /**
     * 参与id
     */
    private Long participationId;

    /**
     * 用户id列表
     */
    private List<String> userIds;

    /**
     * 操作人id
     */
    private String operatorId;

    /**
     * 辅导员所属小组id
     */
    private Long targetId;

    /**
     * 加入方式 1-手动加入 2-动态用户组加入
     */
    private int joinMethod;
}
