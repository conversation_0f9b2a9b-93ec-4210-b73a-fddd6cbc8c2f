package com.yxt.aom.base.bean.part.honor;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class Honor4Create {

    @Schema(description = "UACD注册表中定义Id")
    private String regId;

    @Schema(description = "荣誉名称", example = "name", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank()
    @Size(max = 50)
    private String honorName;

    @Schema(description = "荣誉图标", example = "http://...", requiredMode = Schema.RequiredMode.REQUIRED)
    @Size(max = 500)
    @NotBlank()
    private String imageUrl;
}
