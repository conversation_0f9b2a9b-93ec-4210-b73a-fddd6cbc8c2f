package com.yxt.aom.base.component.part;

import com.yxt.aom.base.common.BaseErrorConsts;
import com.yxt.aom.base.entity.common.Activity;
import com.yxt.aom.base.enums.AomActivityStatusEnum;
import com.yxt.aom.base.enums.AuditStatusEnum;
import com.yxt.aom.base.enums.AuditTempEnum;
import com.yxt.aom.base.manager.common.AomDesignerManager;
import com.yxt.aom.base.mapper.common.ActivityMapper;
import com.yxt.aom.base.wrapper.AuditWrapper;
import com.yxt.aom.common.config.AomDataSourceTypeHolder;
import com.yxt.auditfacade.bean.AuditTmplInfo4Base;
import com.yxt.common.enums.YesOrNo;
import com.yxt.common.exception.ApiException;
import com.yxt.uacd.facade.bean.DesignerConfigBean;
import jodd.util.StringPool;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Objects;

import static com.yxt.common.Constants.INT_0;

/**
 * 审核相关方法
 *
 * <AUTHOR> @yxt.com
 * @date 2024 /12/12 11:32
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class AomAuditComponent {

    private final AuditWrapper auditWrapper;
    private final ActivityMapper activityMapper;
    private final AomDesignerManager designerManager;

    /**
     * 项目提交审核
     *
     * @param orgId         机构id
     * @param currentUserId 发起人
     * @param actvId        actvId
     * @param regId         regId
     * @param optionalBeans 自选审核节点 --》用模板审核可以为空
     * @return boolean true:直接调发布 ,false:需要审核通过后发布
     */
    public boolean initiateAudit(String orgId, String currentUserId, String actvId, String regId,
            String optionalBeans) {
        boolean result = true;
        AomDataSourceTypeHolder.set(regId);
        Activity activity = activityMapper.findById(orgId, actvId);
        if (activity == null) {
            log.info("initiateAudit activity not found orgId - {} actvId - {}", orgId, actvId);
            return false;
        }
        if (activity.getAuditStatus() == AuditStatusEnum.ING.getType()) {
            throw new ApiException(BaseErrorConsts.AOM_AUDIT_EXEC_EXIST);
        }
        if (activity.getAuditEnabled() == 0) {
            log.info("initiateAudit activity auditEnabled is close orgId - {} actvId - {}", orgId, actvId);
            return true;
        }
        if (activity.getActvStatus() != AomActivityStatusEnum.DRAFT.getType()) {
            log.info("initiateAudit activity actvStatus is not DRAFT orgId - {} actvId - {}", orgId, actvId);
            return true;
        }

        if (activity.getAuditStatus() == AuditStatusEnum.PASS.getType()) {
            log.info("initiateAudit activity auditStatus is PASS orgId - {} actvId - {}", orgId, actvId);
            return true;
        }

        DesignerConfigBean designerConfig = designerManager.getDesignerConfig(regId);
        if (designerConfig != null && designerConfig.getManageConfigBean() != null && StringUtils.isNotBlank(
                designerConfig.getManageConfigBean().getProjectAuditTempCode())) {
            String projectAuditTempCode = designerConfig.getManageConfigBean().getProjectAuditTempCode();
            AuditTempEnum auditTempEnum = AuditTempEnum.getByTmplCode(projectAuditTempCode);
            //判断是否配置审核流程
            AuditTmplInfo4Base info;
            if (Objects.equals(auditTempEnum.getAuditType(), INT_0)) {
                info = auditWrapper.getAuditTmplBaseInfo(projectAuditTempCode, orgId);
            } else {
                info = auditWrapper.getAuditProjTmplBaseInfo(projectAuditTempCode, orgId, actvId);
            }
            if (info == null) {
                log.info("initiateAudit activity auditTmplInfo4Base is null orgId - {} actvId - {}", orgId, actvId);
                return false;
            }
            if (info.getTmplStatus() != null && info.getTmplStatus() == YesOrNo.NO.getValue()) {
                activity.setAuditStatus(AuditStatusEnum.PASS.getType());
            } else {
                activity.setAuditStatus(AuditStatusEnum.ING.getType());
                if (Objects.equals(auditTempEnum.getAuditType(), INT_0)) {
                    auditWrapper.initiateAudit(orgId, currentUserId, projectAuditTempCode, activity.getActvName(),
                            actvId + StringPool.SEMICOLON + regId, optionalBeans);
                } else {
                    auditWrapper.initiateProjAudit(orgId, currentUserId, projectAuditTempCode, activity.getActvName(),
                            actvId + StringPool.SEMICOLON + regId, optionalBeans, actvId);
                }

                result = false;
            }
            activityMapper.updateById(activity);
        }
        return result;
    }


    /**
     * 撤回审核
     *
     * @param orgId         机构id
     * @param currentUserId 撤回审核人
     * @param actvId        actvId
     * @param regId         regId
     */
    public void revokeAudit(String orgId, String currentUserId, String actvId, String regId) {
        AomDataSourceTypeHolder.set(regId);
        Activity activity = activityMapper.findById(orgId, actvId);
        if (activity == null) {
            log.info("busRevoke activity not found orgId - {} actvId - {}", orgId, actvId);
            return;
        }
        if (activity.getAuditStatus() != AuditStatusEnum.ING.getType()) {
            log.info("busRevoke activity auditStatus is not ing orgId - {} actvId - {}", orgId, actvId);
            return;
        }
        DesignerConfigBean designerConfig = designerManager.getDesignerConfig(regId);
        if (designerConfig != null && designerConfig.getManageConfigBean() != null && StringUtils.isNotBlank(
                designerConfig.getManageConfigBean().getProjectAuditTempCode())) {
            auditWrapper.busRevoke(orgId, currentUserId, actvId + StringPool.SEMICOLON + regId);
            activity.setAuditStatus(AuditStatusEnum.REVOCATION.getType());
            activityMapper.updateById(activity);
        }
    }


    /**
     * 是否开启审核
     *
     * @param orgId the org id
     * @param regId the reg id
     * @return the boolean
     */
    public boolean openAudit(String orgId, String regId) {
        DesignerConfigBean designerConfig = designerManager.getDesignerConfig(regId);
        if (designerConfig != null && designerConfig.getManageConfigBean() != null && StringUtils.isNotBlank(
                designerConfig.getManageConfigBean().getProjectAuditTempCode())) {
            String projectAuditTempCode = designerConfig.getManageConfigBean().getProjectAuditTempCode();
            //判断是否配置审核流程
            AuditTmplInfo4Base info = auditWrapper.getAuditTmplBaseInfo(projectAuditTempCode, orgId);
            return info != null && info.getTmplStatus() != YesOrNo.NO.getValue();
        }
        return false;
    }
}
