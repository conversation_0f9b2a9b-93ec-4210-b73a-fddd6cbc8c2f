package com.yxt.aom.base.component.part;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yxt.aom.base.bean.part.ActivityBatchDelayStudyReq;
import com.yxt.aom.base.bean.part.ActivityDelayStudyReq;
import com.yxt.aom.base.bean.part.ActivityMember4Add;
import com.yxt.aom.base.bean.part.ActivityMember4StartEndTime;
import com.yxt.aom.base.bean.part.ActivityPart4Create;
import com.yxt.aom.base.bean.part.ActivityPartEnrollSetBean;
import com.yxt.aom.base.bean.part.ActivityParticipationMember4Change;
import com.yxt.aom.base.bean.part.AomActPartDynamicGroup4Create;
import com.yxt.aom.base.bean.part.AomAddActivityMemberMsgBean;
import com.yxt.aom.base.bean.part.AomUserInfo;
import com.yxt.aom.base.bean.part.GroupLeader;
import com.yxt.aom.base.bean.part.MemberImportBean;
import com.yxt.aom.base.bean.part.PartMember4List;
import com.yxt.aom.base.bean.part.PartMemberErrorExportReq;
import com.yxt.aom.base.bean.part.PartMemberPageCriteria;
import com.yxt.aom.base.bean.part.PartMemberReq;
import com.yxt.aom.base.bean.part.PartUserChangeBean;
import com.yxt.aom.base.bean.part.file.MemberImportResponse;
import com.yxt.aom.base.cache.AomCacheKey;
import com.yxt.aom.base.cache.AomRedisCache;
import com.yxt.aom.base.common.AomPropConstants;
import com.yxt.aom.base.component.common.AomMqComponent;
import com.yxt.aom.base.custom.interceptor.DlcFileExportInterceptor;
import com.yxt.aom.base.entity.common.Activity;
import com.yxt.aom.base.entity.part.ActivityGroup;
import com.yxt.aom.base.entity.part.ActivityParticipation;
import com.yxt.aom.base.entity.part.ActivityParticipationMember;
import com.yxt.aom.base.enums.ActivityTypeEnum;
import com.yxt.aom.base.enums.AomActivityMemberChangeEnum;
import com.yxt.aom.base.enums.AomActivityStatusEnum;
import com.yxt.aom.base.enums.AomCacheKeyEnum;
import com.yxt.aom.base.enums.DesignerDataAuthorityEnum;
import com.yxt.aom.base.manager.common.AomDesignerManager;
import com.yxt.aom.base.manager.part.ParticipationMemberManager;
import com.yxt.aom.base.service.AomI18nService;
import com.yxt.aom.base.service.common.ActivityService;
import com.yxt.aom.base.service.export.PartMemberErrorExportService;
import com.yxt.aom.base.service.export.PartMemberExportService;
import com.yxt.aom.base.service.part.impl.ActivityDynamicGroupService;
import com.yxt.aom.base.service.part.impl.ActivityGroupService;
import com.yxt.aom.base.service.part.impl.ActivityParticipationMemberService;
import com.yxt.aom.base.service.part.impl.ActivityParticipationService;
import com.yxt.aom.base.wrapper.UdpWrapper;
import com.yxt.aom.common.config.AomDataSourceTypeHolder;
import com.yxt.common.Constants;
import com.yxt.common.exception.ApiException;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.DateUtil;
import com.yxt.common.util.StreamUtil;
import com.yxt.dbrouter.core.toolkit.DBRouteHolder;
import com.yxt.enrollcenter.facade.bean.msg.notice.ProjectFormalBean;
import com.yxt.leaf.service.SnowflakeKeyGenerator;
import com.yxt.uacd.facade.bean.DesignerManageConfigBean;
import com.yxt.uacd.facade.bean.DesignerMsgTmplConfigBean;
import com.yxt.udpfacade.bean.user.es.EsScopeBean;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.google.common.collect.Lists.newArrayList;
import static com.yxt.aom.base.common.AomExportConstants.GROUP_LEADER_NOT_CHANGE;
import static com.yxt.aom.base.common.AomExportConstants.GROUP_NOT_EXIST;
import static com.yxt.aom.base.common.AomExportConstants.GROUP_TOO_MANY;
import static com.yxt.aom.base.common.AomExportConstants.SUB_PROJECT_ERROR;
import static com.yxt.aom.base.common.AomExportConstants.TOO_MANY_USER;
import static com.yxt.aom.base.common.AomExportConstants.USER_DELETED;
import static com.yxt.aom.base.common.AomExportConstants.USER_DISABLE;
import static com.yxt.aom.base.common.AomExportConstants.USER_FORMAL_ERROR;
import static com.yxt.aom.base.common.AomExportConstants.USER_IS_NOT_ALLOW;
import static com.yxt.aom.base.common.AomExportConstants.USER_LEADER_ERROR;
import static com.yxt.aom.base.common.AomExportConstants.USER_LEADER_EXIST;
import static com.yxt.aom.base.common.AomExportConstants.USER_LEADER_OVERFLOW;
import static com.yxt.aom.base.common.AomExportConstants.USER_NAME_IS_NULL;
import static com.yxt.aom.base.common.AomMqConstants.TOPIC_ACTIVITY_MEMBER;
import static com.yxt.aom.base.common.BaseErrorConsts.ACTV_IS_END;
import static com.yxt.aom.base.common.BaseErrorConsts.ACTV_NOT_EXIST;
import static com.yxt.aom.base.common.BaseErrorConsts.AOM_PART_MEMBER_NOT_FOUND;
import static com.yxt.aom.base.common.BaseErrorConsts.MISSING_PARAMS;
import static com.yxt.aom.base.common.DefaultValueConstants.Numbers.INT_0;
import static com.yxt.aom.base.common.DefaultValueConstants.Numbers.INT_1;
import static com.yxt.aom.base.common.DefaultValueConstants.Numbers.INT_100;
import static com.yxt.aom.base.common.DefaultValueConstants.Numbers.INT_3;
import static com.yxt.common.Constants.INT_1000;
import static com.yxt.common.Constants.INT_500;
import static com.yxt.common.enums.YesOrNo.NO;
import static com.yxt.common.enums.YesOrNo.YES;

@Component
@Slf4j
@RequiredArgsConstructor
public class ParticipationComponent {

    private final ActivityParticipationService activityParticipationService;
    private final ActivityParticipationMemberService activityParticipationMemberService;
    private final AomMqComponent aomMqComponent;
    private final ParticipationMemberManager partMemberManager;
    private final UdpWrapper udpWrapper;
    private final ActivityGroupService activityGroupService;
    private final AomRedisCache aomRedisCache;
    private final PartMemberErrorExportService partMemberErrorExportService;
    private final PartMemberExportService partMemberExportService;
    private final ActivityService activityService;
    private final AomI18nService aomI18nService;
    private final ActivityDynamicGroupService activityDynamicGroupService;
    private final AomDesignerManager aomDesignerManager;
    private final SnowflakeKeyGenerator snowflakeKeyGenerator;
    private final Optional<DlcFileExportInterceptor> fileExportInterceptor;


    public static final List<String> KEYS = Collections.unmodifiableList(
            newArrayList(USER_NAME_IS_NULL, USER_IS_NOT_ALLOW, USER_DELETED, USER_DISABLE, GROUP_NOT_EXIST,
                    GROUP_TOO_MANY, TOO_MANY_USER, GROUP_LEADER_NOT_CHANGE, SUB_PROJECT_ERROR, USER_FORMAL_ERROR,
                    USER_LEADER_ERROR, USER_LEADER_EXIST, USER_LEADER_OVERFLOW));


    public Long createPart(ActivityPart4Create bean, UserCacheBasic userCacheBasic) {
        return activityParticipationService.createPart(bean, userCacheBasic);
    }

    public Long updatePart(ActivityPart4Create bean, UserCacheBasic userCacheBasic) {
        return activityParticipationService.createPart(bean, userCacheBasic);
    }

    public void dynamicGroupSaveOrUpdate(AomActPartDynamicGroup4Create bean, UserCacheBasic userCacheBasic) {
        boolean sync = activityDynamicGroupService.saveOrUpdate(bean, userCacheBasic, INT_0);
        String userGroupId = bean.getUserGroupId();
        if (sync && StringUtils.isNotEmpty(userGroupId)) {
            List<String> userIds = udpWrapper.getUserIdsByGroupId(userCacheBasic.getOrgId(), userGroupId, -1);
            if (CollectionUtils.isNotEmpty(userIds)) {
                ActivityParticipationMember4Change memberBean = new ActivityParticipationMember4Change();
                memberBean.setParticipationId(bean.getPartId());
                memberBean.setUserIds(userIds);
                memberBean.setFormal(INT_1);
                memberBean.setActvId(bean.getActvId());
                memberBean.setJoinMethod(INT_1);
                addMember(memberBean, userCacheBasic.getOrgId(), userCacheBasic.getUserId());
            }

        }
    }

    public ActivityMember4StartEndTime getMemberStartEndTime(String orgId, String actvId, Long partId, String userId) {
        Activity activity = activityService.findById(orgId, actvId);
        return activityParticipationMemberService.getMemberStartEndTime(orgId, activity, partId, userId);
    }

    public void delayStudy(ActivityDelayStudyReq bean, UserCacheBasic userCacheBasic) {
        String actvId = bean.getActvId();
        String orgId = userCacheBasic.getOrgId();
        Activity activity = activityService.findById(orgId, actvId);
        if (activity == null) {
            throw new ApiException(ACTV_NOT_EXIST);
        }
        if (AomActivityStatusEnum.isOver(activity.getActvStatus())) {
            throw new ApiException(ACTV_IS_END);
        }
        activityParticipationMemberService.delayStudy(bean, userCacheBasic);
    }

    public void batchDelayStudy(ActivityBatchDelayStudyReq bean, String orgId, String userId, String sourceCode) {
        String actvId = bean.getActvId();
        Activity activity = activityService.findById(orgId, actvId);
        if (activity == null) {
            throw new ApiException(ACTV_NOT_EXIST);
        }
        if (AomActivityStatusEnum.isOver(activity.getActvStatus())) {
            throw new ApiException(ACTV_IS_END);
        }
        activityParticipationMemberService.batchDelayStudy(bean, orgId, userId, sourceCode);
    }

    public void addMember(ActivityMember4Add beanReq, String orgId, String userId) {
        ActivityParticipationMember4Change bean = new ActivityParticipationMember4Change();
        BeanCopierUtil.copy(beanReq, bean, false);
        //去rangId
        Activity activity = activityService.findById(orgId, bean.getActvId());
        if (activity == null) {
            throw new ApiException(ACTV_NOT_EXIST);
        }
        if (CollectionUtils.isEmpty(bean.getUserIds())) {
            return;
        }
        List<String> userIds = bean.getUserIds().stream().distinct().collect(Collectors.toList());
        bean.setRegId(activity.getActvRegId());
        Date date = DateUtil.currentTime();
        bean.setOrgId(orgId);
        bean.setOptUserId(userId);
        bean.setEffectTime(date);
        bean.setEventType(AomActivityMemberChangeEnum.ADD);
        bean.setCurrentTime(date);
        bean.setRetryTopic(TOPIC_ACTIVITY_MEMBER);
        bean.setActvType(activity.getActvType());
        bean.setGroupId(beanReq.getGroupId());
        try {
            if (userIds.size() > INT_1000) {
                List<List<String>> partition = Lists.partition(userIds, INT_1000);
                partition.forEach(pt -> {
                    bean.setUserIds(pt);
                    List<ActivityParticipationMember> activityParticipationMembers = buildMemberEntity(bean);
                    AomAddActivityMemberMsgBean aomAddActivityMemberMsgBean = new AomAddActivityMemberMsgBean();
                    aomAddActivityMemberMsgBean.setMemberList(activityParticipationMembers);
                    aomAddActivityMemberMsgBean.setBean(bean);
                    aomMqComponent.sendRocketMessage(TOPIC_ACTIVITY_MEMBER, JSON.toJSONString(aomAddActivityMemberMsgBean));
                });
            } else {
                addMember(bean);
            }
        } catch (Exception e) {
            log.error("addMember err:{}", e.getMessage());
        }
    }

    public List<ActivityParticipationMember> addMember(ActivityParticipationMember4Change bean) {
        List<ActivityParticipationMember> activityParticipationMembers = buildMemberEntity(bean);
        if (CollectionUtils.isNotEmpty(activityParticipationMembers)) {
           return activityParticipationMemberService.addMemberV2(activityParticipationMembers, bean);
        }
        return Lists.newArrayList();
    }

    public void addMemberForHandle(AomAddActivityMemberMsgBean msgBean) {
        ActivityParticipationMember4Change bean = msgBean.getBean();
        List<ActivityParticipationMember> memberList = msgBean.getMemberList();
        if (CollectionUtils.isNotEmpty(memberList)) {
            activityParticipationMemberService.addMemberV2(memberList, bean);
        }
    }

    // region enroll

    /**
     * 报名添加人员
     *
     * @param projectFormalBean
     * @param orgId
     * @param optUserId
     */
    public void addMemberForEnroll(ProjectFormalBean projectFormalBean, String orgId, String optUserId) {
        List<String> userIds = projectFormalBean.getUserIds();
        String outProjectId = projectFormalBean.getOutProjectId();
        //        Long participationId = activityParticipationService.getParticipationId(orgId, outProjectId);
        ActivityParticipation activityPart = activityParticipationService.getActivityPart(orgId, Long.parseLong(outProjectId));
        if (activityPart == null) {
            return;
        }

        ActivityParticipationMember4Change memberBean = new ActivityParticipationMember4Change();
        memberBean.setParticipationId(activityPart.getId());
        memberBean.setUserIds(userIds);
        memberBean.setFormal(INT_1);
        memberBean.setActvId(activityPart.getActvId());
        memberBean.setJoinMethod(INT_3);
        addMember(memberBean, orgId, optUserId);
    }

    public void delPartMember4Enroll(String orgId, String operatorId, String userId, String partId) {
        log.info("delPartMember4Enroll req partId:{}, userId:{}", partId, userId);

        ActivityParticipation activityPart = activityParticipationService.getActivityPart(orgId, Long.parseLong(partId));
        if (activityPart == null) {
            return;
        }
        Activity activity = activityService.requireAvailableActivity(orgId, activityPart.getActvId());
        if (activity == null) {
            return;
        }
        //        Long participationId = activityParticipationService.getParticipationId(orgId, actvId);
        //查询user的报名加入记录
        ActivityParticipationMember member = activityParticipationMemberService.getByUserIdAndJoinMethod(orgId,
                activityPart.getActvId(), activityPart.getId(), userId, INT_3);
        if (member == null) {
            log.info("delPartMember4Enroll member is null");
            return;
        }

        Date date = DateUtil.currentTime();
        // 过滤群主、删除代办、删除课程订单、维护redis缓存信息、删除带教...
        PartUserChangeBean changeBean = PartUserChangeBean.builder().orgId(orgId)
                .actvId(activityPart.getActvId()).actvType(ActivityTypeEnum.PROJ.getType())
                .regId(activity.getActvRegId())
                .participationId(activityPart.getId()).userIds(Lists.newArrayList(userId))
                .optUserId(operatorId).currentTime(date).build();
        activityParticipationMemberService.doDelPartMember(changeBean);

    }


    //endregion enroll

    private List<ActivityParticipationMember> buildMemberEntity(ActivityParticipationMember4Change bean) {
        List<ActivityParticipationMember> entities = new ArrayList<>();
        List<String> userIds = bean.getUserIds();
        if (CollectionUtils.isEmpty(userIds)) {
            return entities;
        }
        userIds.forEach(userId -> {
            ActivityParticipationMember member = new ActivityParticipationMember();
            member.setOrgId(bean.getOrgId());
            member.setUserId(userId);
            member.setActvId(bean.getActvId());
            member.setParticipationId(bean.getParticipationId());
            member.setFormal(bean.getFormal());
            member.setJoinTime(bean.getCurrentTime());
            member.setJoinMethod(bean.getJoinMethod());
            member.setDelayFlag(INT_0);
            Long groupId = bean.getGroupId();
            if (groupId == null) {
                groupId = 0L;
            }
            member.setGroupId(groupId);
            member.setEffectTime(bean.getEffectTime());
            member.setDeleted(INT_0);
            member.setUpdateUserId(bean.getOptUserId());
            member.setUpdateTime(bean.getCurrentTime());
            member.setCreateUserId(bean.getOptUserId());
            member.setCreateTime(bean.getCurrentTime());
            if (bean.getStartTimeForCycle() != null) {
                member.setStartTime(bean.getStartTimeForCycle());
            }
            member.setActivityScore(BigDecimal.valueOf(-1));
            member.setActivityComment(StringUtils.EMPTY);
            member.setPassed(-1);
            member.setGraduated(INT_0);

            entities.add(member);
        });
        return entities;
    }


    /**
     * 活动参与学员分页列表信息
     *
     * @param page  分页信息
     * @param orgId 机构id
     * @param req   查询参数
     * @return 学员信息
     */
    public PagingList<PartMember4List> listPagePartMember(Page<PartMember4List> page, String orgId, PartMemberReq req,
            String sourceCode) {
        String actvId = req.getActvId();
        if (StringUtils.isBlank(actvId)) {
            Long participationId = req.getParticipationId();
            ActivityParticipation participation = activityParticipationService.getActivityPart(orgId, participationId);
            actvId = participation.getActvId();
        }
        IPage<PartMember4List> result = activityParticipationMemberService.listPagePartMember(page, orgId, actvId, req,
                sourceCode);
        return BeanCopierUtil.toPagingList(result);
    }

    /**
     * 参与人员类型变更 正式转旁听  旁听转正式
     *
     * @param orgId      机构id
     * @param req        入参信息
     * @param operatorId 操作人id
     */
    public void changeUserFormal(String orgId, PartMemberReq req, String operatorId, String fullName, String sourceCode) {
        log.info("修改参与人员类型 orgId={} operatorId={} req={}", orgId, operatorId, JSON.toJSONString(req));
        Long participationId = req.getParticipationId();
        ActivityParticipation participation = activityParticipationService.getActivityPart(orgId, participationId);
        List<String> allUserIds = Lists.newArrayList();
        if (req.isCheckAll()) {
            // 全选
            Integer formal = req.getFormal();
            req.setFormal(formal == INT_1 ? INT_0 : INT_1);
            List<String> userIds = activityParticipationMemberService.listPartMember(orgId, participation.getActvId(),
                    req, sourceCode);
            if (CollectionUtils.isNotEmpty(userIds)) {
                allUserIds.addAll(userIds);
            }
            if (CollectionUtils.isNotEmpty(req.getExcludeUserIds())) {
                allUserIds.removeAll(req.getExcludeUserIds());
            }
        } else {
            if (CollectionUtils.isEmpty(req.getUserIds())) {
                log.info("类型变更未找到用户id req={}", JSON.toJSONString(req));
                throw new ApiException(MISSING_PARAMS);
            }
            allUserIds.addAll(req.getUserIds());
        }
        if (CollectionUtils.isEmpty(allUserIds)) {
            log.info("类型变更未找到符合条件的用户信息 req={}", JSON.toJSONString(req));
            return;
        }

        Date currentTime = DateUtil.currentTime();
        Activity activity = activityService.requireAvailableActivity(orgId, participation.getActvId());
        if (CollectionUtils.size(allUserIds) > INT_500) {
            AomAddActivityMemberMsgBean bean = new AomAddActivityMemberMsgBean();
            ActivityParticipationMember4Change change = ActivityParticipationMember4Change.builder()
                    .regId(activity.getActvRegId()).orgId(orgId).actvId(participation.getActvId()).actvType(activity.getActvType()).participationId(participationId).formal(req.getFormal())
                    .optUserId(operatorId).fullName(fullName).effectTime(currentTime).currentTime(currentTime)
                    .eventType(AomActivityMemberChangeEnum.FORMAL)
                    .sendMsg(Objects.equals(YES.getValue(), req.getSendMsg())).build();
            bean.setBean(change);
            Lists.partition(allUserIds, INT_1000).forEach(uIds -> {
                change.setUserIds(uIds);
                aomMqComponent.sendRocketMessage(TOPIC_ACTIVITY_MEMBER, bean);
            });
            return;
        }
        // 执行用户类型变更操作
        PartUserChangeBean changeBean = PartUserChangeBean.builder().orgId(orgId).actvId(participation.getActvId()).actvType(ActivityTypeEnum.PROJ.getType()).regId(activity.getActvRegId())
                .participationId(participationId).userIds(allUserIds).formal(req.getFormal()).optUserId(operatorId).fullName(fullName).sendMsg(Objects.equals(YES.getValue(), req.getSendMsg())).currentTime(currentTime)
                .build();
        activityParticipationMemberService.doChangeUserFormal(changeBean);
    }


    /**
     * 删除活动参与学员信息
     *
     * @param orgId      机构id
     * @param bean       入参信息
     * @param operatorId 操作人id
     * @param sourceCode 机构渠道来源code
     */
    public void delPartMember(String orgId, PartMemberReq bean, String operatorId, String sourceCode, String actvId) {
        log.info("delPartMemberReq orgId={} bean={}", orgId, JSON.toJSONString(bean));
        DBRouteHolder.push(orgId);
        AomDataSourceTypeHolder.set(bean.getRegId());
        Long participationId = bean.getParticipationId();
        List<String> realUserIs;
        if (bean.isCheckAll()) {
            // 全选
            List<String> userIds = activityParticipationMemberService.listPartMember(orgId, actvId, bean, sourceCode);
            if (CollectionUtils.isEmpty(userIds)) {
                log.info("删除用户未找到用户id req={}", JSON.toJSONString(bean));
                throw new ApiException(MISSING_PARAMS);
            }
            realUserIs = userIds;
        } else {
            realUserIs = partMemberManager.listPartUserIdByUserName(orgId, actvId, participationId,
                    bean.getUserNames());
            if (CollectionUtils.isNotEmpty(bean.getUserIds())) {
                realUserIs.addAll(bean.getUserIds());
            }
        }
        if (CollectionUtils.isEmpty(realUserIs)) {
            log.info("删除用户未找到符合条件的用户信息 req={}", JSON.toJSONString(bean));
            return;
        }
        Date currentTime = DateUtil.currentTime();
        Activity activity = activityService.requireAvailableActivity(orgId, actvId);
        if (CollectionUtils.size(realUserIs) > INT_500) {
            AomAddActivityMemberMsgBean msgBean = new AomAddActivityMemberMsgBean();
            ActivityParticipationMember4Change change = ActivityParticipationMember4Change.builder()
                    .regId(activity.getActvRegId()).orgId(orgId).actvId(actvId).actvType(activity.getActvType()).participationId(participationId).optUserId(operatorId).effectTime(currentTime)
                    .sourceCode(sourceCode).sendMsg(Objects.equals(YES.getValue(), bean.getSendMsg()))
                    .eventType(AomActivityMemberChangeEnum.REMOVE).build();
            msgBean.setBean(change);
            Lists.partition(realUserIs, INT_1000).forEach(uIds -> {
                change.setUserIds(uIds);
                aomMqComponent.sendRocketMessage(TOPIC_ACTIVITY_MEMBER, msgBean);
            });
            return;
        }
        // 过滤群主、删除代办、删除课程订单、维护redis缓存信息、删除带教...
        PartUserChangeBean changeBean = PartUserChangeBean.builder().orgId(orgId).actvId(actvId)
                .actvType(ActivityTypeEnum.PROJ.getType()).regId(activity.getActvRegId())
                .participationId(participationId).userIds(realUserIs).optUserId(operatorId).currentTime(currentTime)
                .sourceCode(sourceCode)
                .sendMsg(Objects.equals(YES.getValue(), bean.getSendMsg())).build();
        activityParticipationMemberService.doDelPartMember(changeBean);
    }

    /**
     * 根据主键id删除参与学员信息
     *
     * @param orgId      机构id
     * @param ids        主键id列表
     * @param operatorId 操作人id
     */
    public void delPartMemberById(String orgId, List<String> ids, String operatorId) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        if (CollectionUtils.size(ids) < INT_100) {
            log.info("删除参与学员 orgId={} ids={}", orgId, JSON.toJSONString(ids));
        }
        DBRouteHolder.push(orgId);
        Date currentTime = DateUtil.currentTime();
        ActivityParticipationMember member = partMemberManager.getActivityPartMemberById(orgId, Long.valueOf(ids.get(0)));
        Activity activity = activityService.requireAvailableActivity(orgId, member.getActvId());
        if (CollectionUtils.size(ids) > INT_1000) {
            AomAddActivityMemberMsgBean msgBean = new AomAddActivityMemberMsgBean();
            ActivityParticipationMember4Change change = ActivityParticipationMember4Change.builder()
                    .regId(activity.getActvRegId()).orgId(orgId).actvId(member.getActvId()).actvType(activity.getActvType()).participationId(member.getParticipationId()).optUserId(operatorId)
                    .effectTime(currentTime).eventType(AomActivityMemberChangeEnum.REMOVE).build();
            msgBean.setBean(change);

            Lists.partition(ids, INT_1000).forEach(memberIds -> {
                List<String> uIds = partMemberManager.listActvPartUserIdById(orgId,
                        memberIds.stream().map(Long::valueOf).collect(Collectors.toList()));
                if (CollectionUtils.isNotEmpty(uIds)) {
                    change.setUserIds(uIds);
                    aomMqComponent.sendRocketMessage(TOPIC_ACTIVITY_MEMBER, msgBean);
                }
            });
            return;
        }
        List<String> uIds = partMemberManager.listActvPartUserIdById(orgId,
                ids.stream().map(Long::valueOf).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(uIds)) {
            log.info("删除参与人员异常 未找到指定参与人员信息");
            throw new ApiException(AOM_PART_MEMBER_NOT_FOUND);
        }
        PartUserChangeBean changeBean = PartUserChangeBean.builder().orgId(orgId)
                .actvId(member.getActvId()).actvType(ActivityTypeEnum.PROJ.getType())
                .regId(activity.getActvRegId())
                .participationId(member.getParticipationId()).userIds(uIds).optUserId(operatorId)
                .currentTime(currentTime).build();
        activityParticipationMemberService.doDelPartMember(changeBean);
    }

    /**
     * 活动人员导入
     *
     * @param orgId           机构id
     * @param userCacheDetail 操作人信息
     * @param beans           导入记录
     * @param partId          参与id
     * @return 导入结果信息
     */
    public MemberImportResponse importStudents(String orgId, UserCacheDetail userCacheDetail, String actvId, List<MemberImportBean> beans, Long partId, LocalDateTime startTime, Locale locale) {
        List<ActivityGroup> groups = activityGroupService.listPartGroup(orgId, actvId, partId);
        Map<String, Long> groupNameMap = groups.stream()
                .collect(Collectors.toMap(ActivityGroup::getGroupName, ActivityGroup::getId, (v1, v2) -> v1));
        // 判断小组名称是否再库里面存在多个
        Map<String, Long> nameCountMap = groups.stream()
                .collect(Collectors.groupingBy(ActivityGroup::getGroupName, Collectors.counting()));
        // 获取群主id
        Map<String, Long> ownerUserMap = groups.stream().filter(t -> StringUtils.isNotBlank(t.getOwnerUserId()))
                .collect(Collectors.toMap(ActivityGroup::getOwnerUserId, ActivityGroup::getId));
        // 根据账号查询
        List<AomUserInfo> excelUsers = partMemberManager.listUserInfoByUserName(orgId,
                beans.stream().map(MemberImportBean::getUserName).filter(StringUtils::isNotEmpty).collect(Collectors.toList()));
        excelUsers = excelUsers.stream().filter(user -> user.getDeleted() == NO.getValue()).collect(Collectors.toList());
        Map<String, AomUserInfo> excelUserNameMap = StreamUtil.list2map(excelUsers, AomUserInfo::getUserNameLowerCase);
        // 账号不填的情况,根据工号查询
        List<AomUserInfo> userByUserNos = udpWrapper.listUserInfoByUserNo(orgId, beans.stream()
                .filter(bean -> StringUtils.isEmpty(bean.getUserName()) && StringUtils.isNotEmpty(bean.getUserNo()))
                .map(bean -> bean.getUserNo().toLowerCase()).collect(Collectors.toList()));
        Map<String, AomUserInfo> excelUserNoMap = StreamUtil.list2map(userByUserNos, AomUserInfo::getUserNoLowerCase);

        excelUsers.addAll(userByUserNos);

        Activity activity = activityService.requireAvailableActivity(orgId, actvId);
        EsScopeBean scopeBean = aomDesignerManager.getUserDataPermission(userCacheDetail, activity.getActvRegId(),
                DesignerDataAuthorityEnum.TRAINEE_ADD_EXTENT, StreamUtil.mapList(excelUsers, AomUserInfo::getUserId));

        List<String> userNames;
        if (!scopeBean.isAllPermission()) {
            userNames = excelUsers.stream().filter(t -> scopeBean.getDatas().contains(t.getUserId()))
                    .map(AomUserInfo::getUsername).distinct().collect(Collectors.toList());
        } else {
            userNames = Lists.newArrayList();
        }
        // 获取db小组组长
        List<GroupLeader> dbGroupLeader = activityGroupService.listPartGroupLeader(orgId, actvId, partId);

        List<ActivityParticipationMember> insertMembers = Lists.newArrayList();
        final List<MemberImportBean> errorBeans = Lists.newArrayList();
        Map<String, String> userIdGroupNameMap = Maps.newHashMap();
        Map<String, Long> importGroupLeader = beans.stream()
                .filter(t -> StringUtils.isNotBlank(t.getGroupName()) && Objects.nonNull(t.getLeader())
                        && t.getLeader() == INT_1)
                .collect(Collectors.groupingBy(MemberImportBean::getGroupName, Collectors.counting()));
        Map<String, String> i18nMap = aomI18nService.getI18nMap(KEYS);
        Map<String, Long> addGroups = Maps.newHashMap();
        Date now = DateUtil.currentTime();
        for (MemberImportBean memberImportBean : beans) {
            // 处理errorBeans
            if (!buildErrorBeans(dbGroupLeader, importGroupLeader, memberImportBean, errorBeans, userNames,
                    nameCountMap, excelUserNameMap, excelUserNoMap, ownerUserMap, scopeBean, activity.getActvName(),
                    i18nMap)) {
                log.info("project id is {}, fail member is {}", partId, JSON.toJSONString(memberImportBean));
                continue;
            }
            AomUserInfo udpUser = excelUserNameMap.get(memberImportBean.getUserName().toLowerCase());
            if (udpUser == null && StringUtils.isNotEmpty(memberImportBean.getUserNo())) {
                udpUser = excelUserNoMap.get(memberImportBean.getUserNo().toLowerCase());
            }
            if (udpUser == null) {
                continue;
            }
            // 可以导入到不存在的小组，不存在的小组会自动创建该小组
            if (StringUtils.isNotBlank(memberImportBean.getGroupName()) && !groupNameMap.containsKey(memberImportBean.getGroupName())
                    && addGroups.get(memberImportBean.getGroupName()) == null) {
                // 第一次初始化
                Long gId = snowflakeKeyGenerator.generateKey();
                addGroups.put(memberImportBean.getGroupName(), gId);
                groupNameMap.put(memberImportBean.getGroupName(), gId);
            }
            if (StringUtils.isBlank(memberImportBean.getGroupName())) {
                //如果不传小组，999是不存在的值，所以不会更新更新队长信息，维持原来的队长信息
                memberImportBean.setLeader(999);
            }
            String uId = udpUser.getUserId();
            if (!userIdGroupNameMap.containsKey(uId)) {
                ActivityParticipationMember groupMember = partMemberManager.generatePartMember(orgId, actvId, partId,
                        uId, memberImportBean.getFormal(), INT_1, userCacheDetail.getUserId(),
                        groupNameMap.get(memberImportBean.getGroupName()), now, startTime);
                userIdGroupNameMap.put(groupMember.getUserId(), memberImportBean.getGroupName());
                insertMembers.add(groupMember);
            }
        }
        // 调用保存加人公共接口
        ActivityParticipationMember4Change bean = ActivityParticipationMember4Change.builder().orgId(orgId)
                .actvId(actvId).regId(activity.getActvRegId()).participationId(partId).actvType(activity.getActvType())
                .optUserId(userCacheDetail.getUserId()).retryTopic(TOPIC_ACTIVITY_MEMBER).build();
        activityParticipationMemberService.addMemberV2(insertMembers, bean);
        // 错误数据进行导出
        Long downloadId = this.exportStudents(orgId, StringUtils.EMPTY, userCacheDetail.getUserId(),
                userCacheDetail.getFullname(), locale, errorBeans);
        // 封装response
        MemberImportResponse response = MemberImportResponse.builder().failCount(errorBeans.size())
                .successCount(insertMembers.size()).build();
        if (Objects.nonNull(downloadId)) {
            response.setErrorFileId(String.valueOf(downloadId));
        }
        return response;
    }

    private boolean buildErrorBeans(List<GroupLeader> dbLeaders, Map<String, Long> importGroupLeader, MemberImportBean memberImportBean, List<MemberImportBean> errorBeans,
            List<String> userNames, Map<String, Long> nameCountMap, Map<String, AomUserInfo> excelUserNameMap, Map<String, AomUserInfo> excelUserNoMap,
            Map<String, Long> ownerUserMap, EsScopeBean scopeBean, String activityName, Map<String, String> i18nMap) {
        String userName = memberImportBean.getUserName();
        String fullName = memberImportBean.getUserFullName();
        String userNo = memberImportBean.getUserNo();
        String groupName = memberImportBean.getGroupName();
        Integer formal = memberImportBean.getFormal();
        Integer leader = memberImportBean.getLeader();
        if (!checkImportNormal(dbLeaders, importGroupLeader, memberImportBean, errorBeans, nameCountMap,
                excelUserNameMap, excelUserNoMap, ownerUserMap, i18nMap, activityName)) {
            return false;
        }
        //.不可导入不在自己数据权限范围内的账号
        if (!scopeBean.isAllPermission() && !userNames.contains(memberImportBean.getUserName())) {
            errorBeans.add(new MemberImportBean(userName, fullName, userNo, groupName, formal, leader,
                    i18nMap.get(USER_IS_NOT_ALLOW), activityName));
            return false;
        }
        return true;
    }

    private boolean checkImportNormal(List<GroupLeader> dbLeaders, Map<String, Long> importGroupLeader, MemberImportBean memberImportBean, List<MemberImportBean> errorBeans,
            Map<String, Long> nameCountMap, Map<String, AomUserInfo> excelUserNameMap, Map<String, AomUserInfo> excelUserNoMap,
            Map<String, Long> ownerUserMap, Map<String, String> i18nMap, String activityName) {
        String userName = memberImportBean.getUserName();
        String fullName = memberImportBean.getUserFullName();
        String userNo = memberImportBean.getUserNo();
        String groupName = memberImportBean.getGroupName();
        Integer formal = memberImportBean.getFormal();
        Integer leader = memberImportBean.getLeader();
        // 帐号、工号必填其一
        if (StringUtils.isBlank(userName) && StringUtils.isBlank(userNo)) {
            errorBeans.add(new MemberImportBean(userName, fullName, userNo, groupName, formal, leader,
                    i18nMap.get(USER_NAME_IS_NULL), activityName));
            return false;
        }
        // XXX小组存在多个
        if (nameCountMap.get(groupName) != null && nameCountMap.get(groupName) > INT_1) {
            errorBeans.add(new MemberImportBean(userName, fullName, userNo, groupName, formal, leader,
                    groupName + i18nMap.get(GROUP_TOO_MANY), activityName));
            return false;
        }
        // 判断formal是否是0，1的值
        if (formal != null && formal != INT_0 && formal != INT_1) {
            errorBeans.add(new MemberImportBean(userName, fullName, userNo, groupName, formal, leader,
                    i18nMap.get(USER_FORMAL_ERROR), activityName));
            return false;
        }
        // 判断leader是否是0，1的值
        if (StringUtils.isNotEmpty(groupName) && leader != null && leader != INT_0 && leader != INT_1) {
            errorBeans.add(new MemberImportBean(userName, fullName, userNo, groupName, formal, leader,
                    i18nMap.get(USER_LEADER_ERROR), activityName));
            return false;
        }
        // 判断leader是否设置多个
        if (StringUtils.isNotEmpty(groupName) && leader != null && leader == INT_1
                && importGroupLeader.get(groupName) != null && importGroupLeader.get(groupName) > INT_1) {
            errorBeans.add(new MemberImportBean(userName, fullName, userNo, groupName, formal, leader,
                    i18nMap.get(USER_LEADER_OVERFLOW), activityName));
            return false;
        }
        // 判断leader是否在库中已存在
        if (leader != null && leader == INT_1) {
            for (GroupLeader gleader : dbLeaders) {
                if (StringUtils.equals(gleader.getGroupName(), groupName) && (
                        !StringUtils.equals(gleader.getLeaderUserName(), userName) && !StringUtils.equals(gleader.getLeaderUserNo(), userNo))) {
                    errorBeans.add(new MemberImportBean(userName, fullName, userNo, groupName, formal, leader,
                            i18nMap.get(USER_LEADER_EXIST), activityName));
                    return false;
                }
            }
        }
        // 是否存在学员并且是否被删除
        AomUserInfo udpUser = excelUserNameMap.get(StringUtils.lowerCase(userName));
        udpUser = setUserName(memberImportBean, excelUserNoMap, userNo, udpUser);
        if (udpUser == null || udpUser.getDeleted() == YES.getValue()) {
            errorBeans.add(new MemberImportBean(userName, fullName, userNo, groupName, formal, leader,
                    i18nMap.get(USER_DELETED), activityName));
            return false;
        }
        if (udpUser.getStatus() == INT_0) {
            errorBeans.add(new MemberImportBean(userName, fullName, userNo, groupName, formal, leader,
                    i18nMap.get(USER_DISABLE), activityName));
            return false;
        }
        // 小组群聊群主无法更换小组，请先更换群主。
        if (Objects.nonNull(ownerUserMap.get(udpUser.getUserId())) && !ownerUserMap.get(udpUser.getUserId()).equals(nameCountMap.get(groupName))) {
            errorBeans.add(new MemberImportBean(userName, fullName, userNo, groupName, formal, leader,
                    i18nMap.get(GROUP_LEADER_NOT_CHANGE), activityName));
            return false;
        }
        return true;
    }

    private AomUserInfo setUserName(MemberImportBean memberImportBean, Map<String, AomUserInfo> excelUserNoMap, String userNo, AomUserInfo udpUser) {
        if (udpUser == null && StringUtils.isNotEmpty(userNo)) {
            udpUser = excelUserNoMap.get(userNo.toLowerCase());

            // 后面会用username做数据权限校验
            if (udpUser != null) {
                memberImportBean.setUserName(udpUser.getUsername());
            }
        }
        return udpUser;
    }

    private void saveErrorBeansRedis(String orgId, String uuId, List<MemberImportBean> errorBeans) {
        if (CollectionUtils.isEmpty(errorBeans)) {
            return;
        }
        AomCacheKey key = AomCacheKeyEnum.IMPORT_GROUP_MEMBER_ERROR.typedKey((mapBuilder -> mapBuilder.of(AomPropConstants.ORG_ID, orgId, AomPropConstants.UUID, uuId)));
        String josn = JSON.toJSONString(errorBeans);
        log.info("saveErrorBeansRedis {}", josn);
        aomRedisCache.put(key.ofString(), josn, key.expire(), key.timeUnit());
    }

    /**
     * 导入学员异常数据进行导出
     *
     * @param orgId      机构id
     * @param uuId       缓存key
     * @param operatorId 操作人id
     * @param fullname   操作人姓名
     * @param locale     多语言
     */
    public Long exportStudents(String orgId, String uuId, String operatorId, String fullname, Locale locale, List<MemberImportBean> errorBeans) {
        if (CollectionUtils.isEmpty(errorBeans)) {
            return null;
        }
        PartMemberErrorExportReq req = new PartMemberErrorExportReq();
        req.setOrgId(orgId);
        req.setUuId(uuId);
        req.setOperatorId(operatorId);
        req.setFullname(fullname);
        req.setLocale(locale);
        if (fileExportInterceptor.isPresent()) {
            Long downloadId = fileExportInterceptor.get().export(req, errorBeans);
            if (Objects.nonNull(downloadId)) {
                return downloadId;
            }
        }
        return partMemberErrorExportService.export(req, errorBeans);
    }


    /**
     * 导出学员列表信息
     *
     * @param orgId      机构id
     * @param req        请求参数
     * @param operatorId 操作人id
     * @param fullname   操作人姓名
     * @param locale     语种
     */
    public void exportPartMember(String orgId, PartMemberReq req, String operatorId, String fullname, Locale locale,
            String sourceCode) {
        Long participationId = req.getParticipationId();
        log.info("exportPartMember orgId={} participationId={}", orgId, participationId);
        ActivityParticipation participation = activityParticipationService.getActivityPart(orgId, participationId);
        Activity activity = activityService.requireAvailableActivity(orgId, participation.getActvId());

        PartMemberPageCriteria criteria = partMemberManager.getCriteria(orgId, participation.getActvId(), req,
                sourceCode, DateUtil.currentTime());
        criteria.setOperatorId(operatorId);
        criteria.setFullName(fullname);
        criteria.setLocale(locale);

        criteria.setActvName(activity.getActvName());

        partMemberExportService.export(criteria);
    }


    public void setActivityEnroll(String orgId, String optUserId, ActivityPartEnrollSetBean partEnrollSetBean) {
        Long enrollProjectId = partEnrollSetBean.getEnrollProjectId();
        Long partId = partEnrollSetBean.getPartId();
        String actvId = partEnrollSetBean.getActvId();
        Activity activity = activityService.findById(orgId, actvId);
        if (activity == null) {
            throw new ApiException(ACTV_NOT_EXIST);
        }
        activityParticipationService.updatePartEnroll(orgId, partId, enrollProjectId, optUserId, activity);
    }

    /**
     * 获取原始消息通道是否开启
     *
     * @param regId 注册id
     * @param msgCode 原始消息code
     * @return 是否开启 0-未开启 1-已开启
     */
    public int getMsgCodeChannel(String regId, String msgCode) {
        DesignerManageConfigBean manageConfig = aomDesignerManager.getDesignerManageConfig(regId);
        if (Objects.isNull(manageConfig) || CollectionUtils.isEmpty(manageConfig.getMsgConfigs())) {
            return Constants.INT_0;
        }

        Map<String, Integer> msgConfigMap = StreamUtil.list2map(manageConfig.getMsgConfigs(),
                DesignerMsgTmplConfigBean::getOriginalMsgTmplCode, DesignerMsgTmplConfigBean::getEnabled);

        return msgConfigMap.getOrDefault(msgCode, Constants.INT_0);
    }

}
