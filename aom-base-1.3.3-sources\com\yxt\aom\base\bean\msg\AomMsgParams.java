package com.yxt.aom.base.bean.msg;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@RequiredArgsConstructor
public class AomMsgParams {

    private String actvId;

    private String orgId;

    private String createFullName;

    private String createUserId;

    /**
     * 消息模板code
     */
    private String tempCode;

    @Schema(description = "是否使用自定义消息模版; 0: 否; 1: 是; 默认: 0;")
    private Integer useCustomTmpl = 1;

    @Schema(description = "自定义消息targetId", example = "xx")
    private String targetId;
}
