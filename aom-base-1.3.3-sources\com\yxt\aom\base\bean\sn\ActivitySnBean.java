package com.yxt.aom.base.bean.sn;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @description: 解锁对象
 * @author: dingjh
 * @date: 2025/6/11 17:23
 */
@Data
public class ActivitySnBean {

    @Schema(description = "机构id", requiredMode = Schema.RequiredMode.REQUIRED)
    private String orgId;
    @Schema(description = "活动项目id", requiredMode = Schema.RequiredMode.REQUIRED)
    private String actvId;
    @Schema(description = "学员id", requiredMode = Schema.RequiredMode.REQUIRED)
    private String userId;
    @Schema(description = "项目发布时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long releaseTime;
    @Schema(description = "学员开始学习时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long userStartTime;
    @Schema(description = "解锁配置", requiredMode = Schema.RequiredMode.REQUIRED)
    private ActivitySnConfig activitySnConfig;
    @Schema(description = "大纲树", requiredMode = Schema.RequiredMode.REQUIRED)
    private ActivityItemTree activityItemTree;
}
