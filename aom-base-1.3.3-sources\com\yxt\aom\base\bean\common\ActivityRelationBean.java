package com.yxt.aom.base.bean.common;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * 活动负责人信息
 *
 * <AUTHOR>
 * @since 2024/12/18
 */
@Getter
@Setter
@NoArgsConstructor
public class ActivityRelationBean {

    /**
     * 机构id
     */
    private String orgId;

    /**
     * 活动id
     */
    private String actvId;

    /**
     * 活动注册id
     */
    private String regId;

    /**
     * 活动负责人id列表
     */
    private List<String> principalUserIds;

    /**
     * 项目下所有活动id map 按照regId分组
     */
    private Map<String, List<String>> refIdMap;

    /**
     * 具体活动ids
     */
    private List<String> refIds;

    /**
     * 类型(1-活动, 2-项目)
     */
    private Integer actvType;

}
