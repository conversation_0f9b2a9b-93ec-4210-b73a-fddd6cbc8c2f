package com.yxt.aom.base.bean.part.group;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * @description:
 * @author: dingjh
 * @date: 2024/12/17 17:16
 */
@Getter
@Setter
@NoArgsConstructor
@Schema(description = "result -1,已重复调用，0正常，1存在异常；failGroupIds：异常小组id[数组]")
public class Result4OpenIm {

    @Schema(description = "是否成功： 0-是，1-存在错误")
    private Integer result = 0;
    @Schema(description = "失败的小组id")
    @JsonSerialize(using = ToStringSerializer.class)
    private List<Long> failGroupIds;
}
