package com.yxt.aom.base.bean.part;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ActivityPartEnrollSetBean {

    @Schema(description = "活动/项目id")
    private String actvId;

    @Schema(description = "参与id")
    private Long partId;

    @Schema(description = "报名id")
    private Long enrollProjectId;

    @Schema(description = "UACD注册表中定义Id example=proj_o2o")
    private String regId;
}
