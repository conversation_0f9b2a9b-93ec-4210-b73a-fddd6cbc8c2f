package com.yxt.aom.base.bean.setup;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@Schema(name = "项目自动催促配置属性")
public class AomActivityAutoUrgeCreate {

    private static final long serialVersionUID = 3175978995515048663L;

    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "自动配置催促项id(配置更新时需要传)")
    private Long urgeId;

    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "项目/活动id")
    private String actvId;

    @NotNull
    @Schema(description = "催促类型 默认0项目催促学员、1阶段催促学员、2任务催促学员、3任务催促批阅人、4催促学员的直属经理、部门经理")
    private Integer urgeType;


    @Schema(description = "日催促时间点，默认8点")
    private Integer urgeTime = 8;


    @Schema(description = "结束前几天设置默认1")
    private Integer urgeBeforeDays = 1;


    @Schema(description = "配置自动督学(是否勾选)是否生效 0不生效 1生效", example = "0")
    private Integer effective = 0;


    @Schema(description = "设置固定时间,自动催促未完成项目的学员 固定时间设置标志(是否勾选) 0未勾选 1勾选", example = "0")
    private Integer fixTimeFlag = 0;

    @Schema(description = "设置固定时间,自动催促未完成项目的学员")
    private AomActivityFixTimeExt fixTime;
    @Schema(description = "是否催促旁听学员0 否 1是")
    private Integer urgeAudit;

    @Schema(description = "urgeType =4 时有效， 多选：1-直属经理、2-部门经理、21-上一级部门经理、22-上二级部门经理、23-上三级部门经理、3-带教导师、4-团队负责人, 示例 [1,22,3,4]")
    private List<Integer> urgeSubType = new ArrayList<>();

    @Schema(description = " urgeType =4 时有效, 1-天, 2-周 , 3-月")
    private Integer urgeTimeType = 1;

    @Schema(description = "urgeType =4 且 urgeTimeType = 2、3时有效,  多选，周[1,3,5,7] 月[1,10,28,30,31]")
    private  List<Integer> timeRange = new ArrayList<>();

    @Schema(description = "urgeType=4时使用,0:全部 1:未完成")
    private Integer urgeRangeWithManager;

    @Schema(description = "0:按固定某一天催促, 1:周期提醒, 2:按自定义周期")
    private Integer urgeTimeCategory;

    @Schema(description = "urge_time_category=1时使用,0:天, 1:周 2:月")
    private Integer urgeCycleType;

    @Schema(description = "urge_time_category=1时使用,多个配置逗号隔开 周[1,3,5,7] 月[1,10,28,30,31]")
    private List<Integer> urgeCycleTimeRange;

    @Schema(description = "自动催促自定义周期配置")
    private List<AomActivityUrgeCustomCycleExtObj> customCycleExtList;
}
