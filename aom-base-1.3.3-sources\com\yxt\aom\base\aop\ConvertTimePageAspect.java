package com.yxt.aom.base.aop;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yxt.common.component.timezone.TimezoneComponent;
import com.yxt.common.pojo.api.CommonList;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.util.timezone.ConvertDateUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import static com.yxt.common.aop.AopConsts.TIMEZONE_AOP_ORDER;

/**
 * ConvertTimePageAspect
 */
@Slf4j
@Aspect
@Component("aomConvertTimePageAspect")
@Order(TIMEZONE_AOP_ORDER)
@RequiredArgsConstructor
public class ConvertTimePageAspect {
    @Value("${base.zone.date.serializer:false}")
    private boolean enabledDateSerializer;
    private final TimezoneComponent timezoneComponent;

    /**
     * Do after returning.
     *
     * @param object the object
     */
    @AfterReturning(returning = "object", pointcut = "@annotation(com.yxt.aom.base.annotation.ConvertTimePage) || @within(com.yxt.aom.base.annotation.ConvertTimePage)")
    public void doAfterReturning(Object object) {
        try {
            if (enabledDateSerializer) {
                if (object instanceof IPage<?> iPage && CollectionUtils.isNotEmpty(iPage.getRecords())) {
                    ConvertDateUtil.convertUTC8ToOther(iPage.getRecords(), timezoneComponent.getZoneId());
                } else if (object instanceof PagingList<?> pagingList && CollectionUtils.isNotEmpty(
                        pagingList.getDatas())) {
                    ConvertDateUtil.convertUTC8ToOther(pagingList.getDatas(), timezoneComponent.getZoneId());
                } else if (object instanceof CommonList<?> commonList && CollectionUtils.isNotEmpty(
                        commonList.getDatas())) {
                    ConvertDateUtil.convertUTC8ToOther(commonList.getDatas(), timezoneComponent.getZoneId());
                }
            }
        } catch (Exception e) {
            log.error("===ConvertTimePageAspect执行失败{}====", e.getMessage());
        }
    }
}
