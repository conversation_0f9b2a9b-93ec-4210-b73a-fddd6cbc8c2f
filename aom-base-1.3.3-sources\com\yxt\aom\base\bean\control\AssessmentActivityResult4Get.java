package com.yxt.aom.base.bean.control;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class AssessmentActivityResult4Get implements Serializable {
    @Serial
    private static final long serialVersionUID = 1607956374344406390L;

    /**
     * 机构id
     */
    @Schema(description = "机构id")
    private String orgId;

    /**
     * 活动/项目id
     */

    @Schema(description = "活动/项目id")
    private String actvId;

    /**
     * 学员id
     */
    @Schema(description = "学员id")
    private String userId;

    @Schema(description = "学员账号")
    private String userName;

    @Schema(description = "学员姓名")
    private String fullName;

    /**
     * 基础记录id
     */
    @Schema(description = "基础记录id")
    private Long baseActvResultId;

    /**
     * 叶节点id
     */
    @Schema(description = "叶节点id")
    private Long itemId;

    /**
     * 叶节点引用对象的具体类型(UACD注册表中定义)
     */
    @Schema(description = "叶节点引用对象的具体类型(UACD注册表中定义)")
    private String refRegId;

    /**
     * 是否必修 0否 1是
     */
    @Schema(description = "是否必修 0否 1是")
    private Integer required;
    /**
     * 0未开始，1进行中，2已完成
     */
    @Schema(description = "0未开始，1进行中，2已完成")
    private Integer resultStatus;

    /**
     * 开始学习时间
     */
    @Schema(description = "开始学习时间")
    private Date startTime;

    /**
     * 完成时间
     */
    @Schema(description = "完成时间")
    private Date completedTime;

    /**
     * 最近学习时间
     */
    @Schema(description = "最近学习时间")
    private Date lastStudyTime;

    /**
     * 是否手动标记完成0 否 1是
     */
    @Schema(description = "是否手动标记完成0 否 1是")
    private Integer handCompleted;


    /**
     * 任务重复类型 0：默认 1：多班次任务 2：指派重复学习
     */
    @Schema(description = "任务重复类型 0：默认 1：多班次任务 2：指派重复学习")
    private Integer resultRepeatFlag;

    /**
     * 指派重新学习次数
     */
    @Schema(description = "指派重新学习次数")
    private Integer repeatCount;

    /**
     * 生效的子任务结果Id
     */
    @Schema(description = "生效的子任务结果Id")
    private Long subTaskResultId;

    /**
     * 活动是否通过 0否 1是
     */
    @Schema(description = "活动是否通过 0否 1是")
    private Integer passed;

    @Schema(description = "活动批阅状态 0：未开始；1：考试中；2：已提交；3：批阅中；4：已完成")
    private Integer targetStatus;

    /**
     * 活动得分
     */
    @Schema(description = "活动得分")
    private BigDecimal score;

    /**
     * 活动总分
     */
    @Schema(description = "活动总分")
    private BigDecimal totalScore;

    /**
     * 是否删除 0未删 1已删
     */
    @Schema(description = "是否删除 0未删 1已删")
    private Integer deleted;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 创建人id
     */
    @Schema(description = "创建人id")
    private String createUserId;

    /**
     * 更细时间
     */
    @Schema(description = "更细时间")
    private Date updateTime;

    /**
     * 更新人id
     */
    @Schema(description = "更新人id")
    private String updateUserId;
}
