package com.yxt.aom.base.bean.part.group;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * @description:
 * @author: dingjh
 * @date: 2024/12/17 16:46
 */
@Getter
@Setter
@NoArgsConstructor
public class GroupIm4Open {
    @Schema(description = "活动id")
    @NotNull
    private String actvId;

    @Schema(description = "UACD注册表中定义Id")
    @NotNull
    private String regId;
}
