package com.yxt.aom.base.bean.control;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ScheduleTaskFilter {
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "任务id")
    private Long id;

    @Schema(description = "叶节点引用对象的具体类型(UACD注册表中定义)", example = "actv_exam")
    private String refRegId;

    @Schema(description = "结果状态：不传-全部，0未开始，1进行中，2已完成")
    private Integer resultStatus;
}
