package com.yxt.aom.base.bean.arrange;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import static com.yxt.aom.base.common.DefaultValueConstants.Numbers.INT_0;

/**
 * ActivityArrangeItemCount
 */
@Data
@Schema(name = "活动大纲节点数")
public class ActivityArrangeItemCount {
    /**
     * 所有任务数量
     */
    private Integer allTaskCount = INT_0;
    /**
     * 必修任务数
     */
    private Integer requiredTaskCount = INT_0;
    /**
     * 带教任务数
     */
    private Integer ojtTaskCount = INT_0;
    /**
     * 带教必修任务数
     */
    private Integer ojtRequiredTaskCount = INT_0;

}
