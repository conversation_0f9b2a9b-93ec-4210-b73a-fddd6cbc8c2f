package com.yxt.aom.base.bean.common;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.yxt.aom.base.common.BaseErrorConsts;
import com.yxt.common.Constants;
import com.yxt.common.annotation.timezone.DateFormatField;
import com.yxt.common.validation.constraints.IntegerScope;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * ActivitySearchReq
 */
@Data
public class ActivitySearchReq {
    @JsonIgnore
    private String orgId;

    @JsonIgnore
    private String userId;

    @JsonIgnore
    private String locale;

    @JsonIgnore
    private String admin;

    @JsonIgnore
    private boolean allPermission;

    @JsonIgnore
    private List<String> scopeUserIds;

    @Schema(description = "活动/项目的具体类型(UACD注册表中定义)", example = "proj_o2o", requiredMode = Schema.RequiredMode.REQUIRED)
    private String actvRegId;

    @Schema(description = "搜索关键字", example = "培训项目")
    private String searchKey;

    @Schema(description = "搜索关键字的字段（searchKey有值时生效，不传默认为名字）", example = "actvName")
    private List<String> searchKeyFields;

    @Schema(description = "要搜索的活动/项目名称", example = "项目名称test")
    @Size(max = 200, message = BaseErrorConsts.ACTV_NAME_SIZE)
    private String actvName;

    @Schema(description = "类型(1-活动, 2-项目; 传null或不传则查全部)", example = "2")
    @IntegerScope(scope = {1, 2}, message = BaseErrorConsts.ACTV_TYPE_SCOPE)
    private Integer actvType;

    @Schema(description = "状态列表(0-未保存, 1-未发布, 2-进行中, 3-已结束, 4-归档中, 41-已归档, 42-归档超过一年, 5-已删除, 6-已暂停, 7-已撤回); 传null或不传则查全部", example = "[1, 2]")
    private Set<Integer> actvStatusSet;

    @Schema(description = "时间模式(0-固定, 1-相对); 传null或不传则查全部", example = "0")
    @IntegerScope(scope = {0, 1}, message = BaseErrorConsts.ACTV_TIMEMODEL_SCOPE)
    private Integer timeModel;

    @Schema(description = "搜索的开始时间", example = "2019-10-08 12:00")
    @DateFormatField(format = Constants.SDF_YEAR2MINUTE)
    private String searchStartDate;

    @Schema(description = "搜索的结束时间", example = "2019-10-08 12:00")
    @DateFormatField(format = Constants.SDF_YEAR2MINUTE)
    private String searchEndDate;

    /**
     * 业务上，列表使用负责人（mgrUserIds）和创建人（creatorUserIds）去筛选
     */
    @Schema(description = "项目/活动查看范围(1-我负责的[项目负责人有我], 2-我创建的[项目创建人是我], 3-我管辖的[所辖员工负责的])", example = "2", requiredMode = Schema.RequiredMode.REQUIRED)
    @IntegerScope(scope = {1, 2, 3}, message = BaseErrorConsts.ACTV_RANGE_SCOPE)
    private int range = 1;

    @Schema(description = "负责人用户id列表")
    @Size(max = 50, message = "apis.aom.activity.mgrUserIds.Size")
    private List<String> mgrUserIds;

    @Schema(description = "创建人id列表")
    @Size(max = 50, message = "apis.aom.activity.creatorUserIds.Size")
    private List<String> creatorUserIds;

    @Schema(description = "创建人部门id列表")
    @Size(max = 50, message = "apis.aom.activity.creatorDeptIds.Size")
    private List<String> creatorDeptIds;

    @Schema(description = "创建时间开始时间", example = "2021-01-01 12:00")
    @DateFormatField(format = Constants.SDF_YEAR2MINUTE)
    private String createStartDate;

    @Schema(description = "创建时间段截止时间", example = "2021-01-10 12:00")
    @DateFormatField(format = Constants.SDF_YEAR2MINUTE)
    private String createEndDate;

    @Schema(description = "项目/活动开始时间筛选开始", example = "2021-01-01 12:00")
    @DateFormatField(format = Constants.SDF_YEAR2MINUTE)
    private String startTimeStartDate;

    @Schema(description = "项目/活动开始时间筛选结束", example = "2021-01-10 12:00")
    @DateFormatField(format = Constants.SDF_YEAR2MINUTE)
    private String startTimeEndDate;

    @Schema(description = "审核状态(0-待审核, 1-审核中, 2-已通过, 3-未通过, 4-已撤回); 传null或不传则查全部")
    private Integer auditStatus;

    @Schema(description = "分类id列表", example = "[1849276367002220000, 1849276367002220001]")
    private Set<String> categoryIds;

    @Schema(description = "用途类别(0-普通项目/活动, 1-项目/活动模板, 2-集团化项目/活动, 3-移动端项目/活动; 默认为0; 3目前已停用)", example = "0")
    @IntegerScope(scope = {0, 1, 2}, message = BaseErrorConsts.ACTV_USAGETYPE_SCOPE)
    private Integer usageType = 0;

    @Schema(description = "是否重要(0-不是，1-是)", example = "0")
    private Integer veryImportant;

    @Schema(description = "来源类型(0独立安排,10岗位地图安排,11层级地图安排,12人才盘点安排,13人才池安排,14个人发展计划)", example = "0")
    private Integer sourceType;

}

