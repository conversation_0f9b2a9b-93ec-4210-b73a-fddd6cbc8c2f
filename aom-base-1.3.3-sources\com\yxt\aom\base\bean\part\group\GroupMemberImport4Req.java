package com.yxt.aom.base.bean.part.group;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @createDate 2024/12/13 13:37:13
 */
@Data
public class GroupMemberImport4Req {
    @Schema(description = "UACD注册表中定义Id")
    private String regId;

    @Schema(description = "项目id")
    private String actvId;

    @Schema(description = "参与id")
    private Long participationId;

    @Schema(description = "小组id，不分组就传0")
    @NotNull(message = "apis.aom.part.groupId.invalid")
    private Long groupId;

    @Schema(description = "学员类型 0-旁听学员 1-正式学员")
    private Integer formal = 1;

    @Schema(description = "文件id")
    private String fileId;
}
