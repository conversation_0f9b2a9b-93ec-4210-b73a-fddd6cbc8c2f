package com.yxt.aom.activity.service.arrange;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yxt.aom.activity.common.AomActvApiError;
import com.yxt.aom.activity.custom.CustomActivityArrangeCompo;
import com.yxt.aom.activity.entity.arrange.ActivityDraft;
import com.yxt.aom.activity.enums.DraftStatusEnum;
import com.yxt.aom.activity.facade.bean.arrange.Activity4BatchSave;
import com.yxt.aom.activity.facade.bean.arrange.ActivityDemoCopyReq;
import com.yxt.aom.activity.facade.bean.arrange.ActivityDemoCopyRsp;
import com.yxt.aom.activity.facade.bean.arrange.ActivityDraft4BatchCheck;
import com.yxt.aom.activity.facade.bean.arrange.ActivityDraft4BatchCopy;
import com.yxt.aom.activity.facade.bean.arrange.ActivityDraft4BatchSave;
import com.yxt.aom.activity.facade.bean.arrange.ActivityDraft4Check;
import com.yxt.aom.activity.facade.bean.arrange.ActivityDraft4Copy;
import com.yxt.aom.activity.facade.bean.arrange.ActivityDraft4Save;
import com.yxt.aom.activity.facade.bean.arrange.ActivityItem4Save;
import com.yxt.aom.activity.facade.bean.arrange.DraftName4Update;
import com.yxt.aom.activity.facade.bean.arrange.RefIdCheckReq;
import com.yxt.aom.activity.facade.bean.arrange.RefIdMap;
import com.yxt.aom.activity.facade.bean.arrange.Response4BatchCheck;
import com.yxt.aom.activity.facade.common.MqConstants;
import com.yxt.aom.activity.facade.mq.ActvDurationMq;
import com.yxt.aom.activity.mapper.arrange.ActivityDraftMapper;
import com.yxt.aom.base.bean.part.cycle.AomActivityDelReq;
import com.yxt.aom.base.common.AomConstants;
import com.yxt.aom.base.common.BaseErrorConsts;
import com.yxt.aom.base.component.common.AomMqComponent;
import com.yxt.aom.base.entity.common.Activity;
import com.yxt.aom.base.entity.common.ActivitySource;
import com.yxt.aom.base.enums.ActivityTypeEnum;
import com.yxt.aom.base.enums.AomActivityStatusEnum;
import com.yxt.aom.base.manager.common.AomRegistryManager;
import com.yxt.aom.base.manager.control.ControlManager;
import com.yxt.aom.base.mapper.arrange.ActivityArrangeItemMapper;
import com.yxt.aom.base.mapper.common.ActivityMapper;
import com.yxt.aom.base.mapper.common.ActivitySourceMapper;
import com.yxt.aom.base.service.part.ActivityLifeCycleService;
import com.yxt.aom.common.config.AomDataSourceTypeHolder;
import com.yxt.aom.common.util.AomBeanNameUtils;
import com.yxt.aom.migr.mq.MigrActivityItemDataMq;
import com.yxt.aom.migr.mq.MigrActivityItemEndedMq;
import com.yxt.aom.migr.mq.common.Activity4Migr;
import com.yxt.common.Constants;
import com.yxt.common.enums.YesOrNo;
import com.yxt.common.exception.ApiException;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.DateUtil;
import com.yxt.common.util.StreamUtil;
import com.yxt.common.util.StringUtil;
import com.yxt.dbrouter.core.toolkit.DBRouteHolder;
import com.yxt.leaf.service.SnowflakeKeyGenerator;
import com.yxt.uacd.facade.bean.RegistryConfigBean;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.yxt.aom.base.common.DefaultValueConstants.Numbers.INT_200;

/**
 * ActivityArrangeService
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ActivityArrangeService {
    private static final String NAME = "name";
    private final ActivityDraftMapper draftMapper;
    private final ActivityMapper activityMapper;
    private final ActivitySourceMapper sourceMapper;
    private final AomMqComponent mqComponent;
    private final ActivityArrangeItemMapper itemMapper;
    private final SnowflakeKeyGenerator snowflakeKeyGenerator;
    private final ActivityLifeCycleService activityLifeCycleService;
    private final AomRegistryManager registryManager;
    private final ControlManager controlManager;

    /**
     * 活动时长变更时调用
     *
     * @param orgId    机构id
     * @param id       活动对象id
     * @param regId    活动对象类型（即UACD注册id）
     * @param duration 活动时长
     */
    public void notifyDurationChanged(String orgId, String id, String regId, Integer duration) {
        AomDataSourceTypeHolder.set(regId);
        LambdaQueryWrapper<ActivityDraft> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ActivityDraft::getOrgId, orgId).eq(ActivityDraft::getRefId, id)
                .eq(ActivityDraft::getRefRegId, regId).eq(ActivityDraft::getDeleted, YesOrNo.NO.getValue());
        List<ActivityDraft> drafts = draftMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(drafts)) {
            return;
        }
        ActvDurationMq mq = new ActvDurationMq();
        mq.setOrgId(orgId);
        mq.setRefRegId(regId);
        mq.setRefId(id);
        mq.setDuration(duration);
        mq.setItemIds(drafts.stream().map(ActivityDraft::getItemId).filter(itemId -> itemId != null && itemId > 0L)
                .collect(Collectors.toSet()));
        mqComponent.sendRocketMessage(MqConstants.TOPIC_AOM_ACTIVITY_DURATIONCHANGED, JSON.toJSONString(mq));
        itemMapper.updateStudyHours(orgId, mq.getItemIds(), duration);
    }

    /**
     * 获取活动表单信息
     *
     * @param orgId    the org id
     * @param actvId   the actv id
     * @param itemId   the item id
     * @param refRegId the ref reg id
     * @param refId    the ref id
     * @return the activity draft
     */
    public ActivityDraft getActivityDraft(String orgId, String actvId, Long itemId, String refRegId, String refId) {
        AomDataSourceTypeHolder.set(refRegId);
        ActivityDraft draft = null;
        if (StringUtils.isNumeric(refId)) {
            //refId可能是草稿id
            draft = draftMapper.selectById(StringUtil.str2Long(refId, 0L));
        }
        if (draft == null) {
            //refId为活动id
            draft = findDraft(orgId, actvId, itemId, refId);
            if (draft == null) {
                throw new ApiException(AomActvApiError.DRAFT_ITEM_NOT_FOUND);
            }
        }
        return draft;
    }

    /**
     * List used ref id set.
     *
     * @param bean the bean
     * @return the set
     */
    public Set<String> listUsedRefId(RefIdCheckReq bean) {
        List<ActivityDraft> drafts = listDraft(bean.getOrgId(), bean.getActvId(), bean.getRefRegId());
        if (CollectionUtils.isEmpty(drafts)) {
            return Collections.emptySet();
        }
        CustomActivityArrangeCompo compo = AomBeanNameUtils.getCustomBean(CustomActivityArrangeCompo.class,
                bean.getRefRegId());
        if (compo == null) {
            return Collections.emptySet();
        }
        return compo.listUsedRefId(bean.getOrgId(), bean.getActvId(), bean.getRefIds(), drafts);
    }

    /**
     * 批量预检活动草稿
     *
     * @param bean the bean
     * @return the response 4 batch check
     */
    public Response4BatchCheck batchCheckDraft(ActivityDraft4BatchCheck bean) {
        List<ActivityDraft4Check> datas = bean.getDatas();
        if (CollectionUtils.isEmpty(datas)) {
            Response4BatchCheck result = new Response4BatchCheck();
            result.setSucceed(true);
            return result;
        }

        CustomActivityArrangeCompo compo = AomBeanNameUtils.getCustomBean(CustomActivityArrangeCompo.class,
                bean.getRefRegId());
        if (compo == null) {
            Response4BatchCheck result = new Response4BatchCheck();
            result.setSucceed(true);
            return result;
        }

        AomDataSourceTypeHolder.set(bean.getRefRegId());
        Set<Long> itemIds = datas.stream().map(ActivityDraft4Check::getItemId).collect(Collectors.toSet());
        List<ActivityDraft> existedDrafts = listDraft(bean.getOrgId(), bean.getActvId(), itemIds);
        Map<Long, List<ActivityDraft>> groupedMap = existedDrafts.stream()
                .collect(Collectors.groupingBy(ActivityDraft::getItemId));
        datas.forEach(data -> updateRefIdAndFormData(data, groupedMap.get(data.getItemId())));
        bean.setDatas(datas.stream().filter(data -> StringUtils.isNotBlank(data.getFormData())).toList());
        return compo.batchCheckDraft(bean);
    }

    /**
     * 批量保存活动草稿
     *
     * @param bean the bean
     * @return the list
     */
    public Map<Long, String> batchSaveDraft(ActivityDraft4BatchSave bean) {
        if (bean == null) {
            return Collections.emptyMap();
        }

        String orgId = bean.getOrgId();
        String actvId = bean.getActvId();
        String operatorId = bean.getOperatorId();
        List<ActivityDraft4Save> datas = bean.getDatas();
        if (StringUtils.isBlank(orgId) || StringUtils.isBlank(actvId) || CollectionUtils.isEmpty(datas)) {
            return Collections.emptyMap();
        }

        datas = datas.stream().filter(data -> data.getItemId() != null && data.getItemId() > 0).toList();
        return batchSaveDraft(orgId, actvId, operatorId, datas, bean.getRefRegId());
    }

    /**
     * 批量复制活动草稿
     *
     * @param bean the bean
     * @return List
     */
    public List<RefIdMap> batchCopyDraft(ActivityDraft4BatchCopy bean) {
        if (bean == null || StringUtils.isBlank(bean.getFromOrgId()) || StringUtils.isBlank(bean.getToOrgId())
                || StringUtils.isBlank(bean.getFromActvId()) || StringUtils.isBlank(bean.getToActvId())
                || CollectionUtils.isEmpty(bean.getDatas())) {
            return Collections.emptyList();
        }
        Map<Long, ActivityDraft4Copy> fromToItemMap = StreamUtil.list2map(bean.getDatas(),
                ActivityDraft4Copy::getFromItemId);
        AomDataSourceTypeHolder.set(bean.getRefRegId());
        DBRouteHolder.push(bean.getFromOrgId());
        List<ActivityDraft> fromDrafts = listDraft(bean.getFromOrgId(), bean.getFromActvId(), fromToItemMap.keySet());
        DBRouteHolder.poll();
        if (CollectionUtils.isEmpty(fromDrafts)) {
            return Collections.emptyList();
        }
        CustomActivityArrangeCompo compo = AomBeanNameUtils.getCustomBean(CustomActivityArrangeCompo.class,
                bean.getRefRegId());
        if (compo != null && !StringUtils.equals(bean.getFromOrgId(), bean.getToOrgId())) {
            compo.updateDraftIdFields4Copy(bean, fromDrafts);
        }
        Map<Long, String> refIdMap = Maps.newHashMap();
        List<ActivityDraft> toDrafts = BeanCopierUtil.convertList(fromDrafts,
                fromDraft -> copyDraft(bean, fromDraft, fromToItemMap.get(fromDraft.getItemId()), refIdMap));
        if (CollectionUtils.isNotEmpty(toDrafts)) {
            DBRouteHolder.push(bean.getToOrgId());
            draftMapper.batchInsert(toDrafts);
            DBRouteHolder.poll();
        }

        Map<Long, Long> toFromItemMap = StreamUtil.list2map(bean.getDatas(), ActivityDraft4Copy::getToItemId,
                ActivityDraft4Copy::getFromItemId);
        return toDrafts.stream().map(toDraft -> toRefIdMap(toDraft, toFromItemMap, refIdMap)).toList();
    }

    /**
     * 批量保存活动（草稿转正式）
     *
     * @param bean the bean
     * @return the map
     */
    public Map<Long, String> batchSaveActivity(Activity4BatchSave bean) {
        if (bean == null) {
            return Collections.emptyMap();
        }

        String orgId = bean.getOrgId();
        String actvId = bean.getActvId();
        String operatorId = bean.getOperatorId();
        List<ActivityItem4Save> items = bean.getItems();
        if (StringUtils.isBlank(orgId) || StringUtils.isBlank(actvId)) {
            return Collections.emptyMap();
        }

        if (items == null) {
            items = Collections.emptyList();
        }
        String refRegId = bean.getRefRegId();
        AomDataSourceTypeHolder.set(refRegId);
        Map<Long, ActivityItem4Save> itemMap = StreamUtil.list2map(items, ActivityItem4Save::getItemId);
        Set<Long> itemIds = itemMap.keySet();
        List<ActivityDraft> existedDrafts = listDraft(orgId, actvId, itemIds);

        List<ActivityDraft> activities2Create = existedDrafts.stream()
                .filter(draft -> StringUtils.isBlank(draft.getRefId())).toList();
        List<ActivityDraft> activities2Update = existedDrafts.stream()
                .filter(draft -> StringUtils.isNotBlank(draft.getRefId()) && Objects.equals(draft.getDraftStatus(),
                        DraftStatusEnum.DRAFT.getType())).toList();
        List<ActivityDraft> drafts4Remove = draftMapper.listDraft4Remove(orgId, actvId, refRegId, itemIds);
        Set<String> activityIds2Remove = drafts4Remove.stream().map(ActivityDraft::getRefId)
                .filter(StringUtils::isNotBlank).collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(activities2Create) && CollectionUtils.isEmpty(activities2Update)
                && CollectionUtils.isEmpty(drafts4Remove)) {
            return Collections.emptyMap();
        }

        CustomActivityArrangeCompo compo = AomBeanNameUtils.getCustomBean(CustomActivityArrangeCompo.class,
                bean.getRefRegId());
        if (compo == null) {
            throw new ApiException(BaseErrorConsts.MISSING_IMPLEMENT);
        }
        Map<Long, String> idMap = compo.batchSaveActivity(orgId, bean.getActvRegId(), actvId, operatorId,
                activities2Create, activities2Update, activityIds2Remove);
        log.debug("idMap : {}", idMap);
        batchSaveActivity(bean, activities2Create, activities2Update, drafts4Remove, itemMap, idMap);
        batchUpdateFormData(orgId, actvId, operatorId, refRegId,bean.getActvRegId());
        return idMap;
    }

    /**
     * 更新活动草稿名称
     *
     * @param bean the bean
     */
    public void updateDraftName(DraftName4Update bean) {
        ActivityDraft draft = getActivityDraft(bean.getOrgId(), bean.getActvId(), bean.getItemId(), bean.getRefRegId(),
                bean.getRefId());
        draft.setFormData(updateName(draft.getFormData(), bean.getNewName()));
        draft.setDraftStatus(DraftStatusEnum.DRAFT.getType());
        draft.setUpdateUserId(bean.getOperatorId());
        draft.setUpdateTime(DateUtil.currentTime());
        draftMapper.updateById(draft);
    }

    /**
     * demo复制活动
     *
     * @param bean the bean
     * @return the activity demo copy rsp
     */
    public ActivityDemoCopyRsp demoCopyActivity(ActivityDemoCopyReq bean) {
        Map<String, String> userMap = bean.getUserMap();
        if (MapUtils.isEmpty(userMap)) {
            return null;
        }

        AomDataSourceTypeHolder.set(bean.getActvRegId());
        DBRouteHolder.push(bean.getSrcOrgId());
        Activity srcActivity = activityMapper.findById(bean.getSrcOrgId(), bean.getSrcActvId());
        List<ActivitySource> srcActivitySourceList = sourceMapper.listByActIds(bean.getSrcOrgId(),
                Collections.singleton(bean.getSrcActvId()));
        ActivitySource srcActivitySource = CollectionUtils.isNotEmpty(srcActivitySourceList) ?
                srcActivitySourceList.get(0) :
                null;
        ActivityDraft srcDraft = findDraft(bean.getSrcOrgId(), bean.getSrcProjectId(), bean.getSrcItemId(), null);
        DBRouteHolder.poll();

        if (srcActivity == null || srcDraft == null || StringUtils.isBlank(srcDraft.getRefId())) {
            return null;
        }
        bean.setSrcFormData(srcDraft.getFormData());
        CustomActivityArrangeCompo compo = AomBeanNameUtils.getCustomBean(CustomActivityArrangeCompo.class,
                bean.getActvRegId());
        if (compo == null) {
            return null;
        }

        ActivityDemoCopyRsp rsp = compo.demoCopyActivity(bean);
        if (rsp == null) {
            return null;
        }

        DBRouteHolder.push(bean.getTgtOrgId());
        draftMapper.insert(copyActivityDraft(bean, rsp, srcDraft));
        activityMapper.insert(copyActivity(bean, rsp, srcActivity));
        if (srcActivitySource != null) {
            sourceMapper.insert(copyActivitySource(bean, rsp, srcActivitySource));
        }
        DBRouteHolder.poll();

        return rsp;
    }

    /**
     * Migr activity.
     *
     * @param mq the mq
     */
    public void migrActivity(MigrActivityItemDataMq mq) {
        Activity4Migr activity4Migr = mq.getActivity();
        if (activity4Migr == null) {
            log.warn("migrActivity activity4Migr is null, mq : {}", mq);
            return;
        }

        delete4MigrActv(mq.getOrgId(), mq.getActivity().getId(), mq.getRefId());

        Activity activity = toActivity(mq);
        activityMapper.insert(activity);

        ActivityDraft draft = toActivityDraft(mq, activity4Migr);
        draftMapper.insert(draft);

        ActivitySource source = toActivitySource(activity, mq.getItemId());
        sourceMapper.insert(source);

        sendEndedMq(mq);
    }

    /**
     * 获取项目下指定类型的任务草稿列表
     *
     * @param orgId    the org id
     * @param actvId   the actv id
     * @param refRegId the ref reg id
     * @return the list
     */
    public List<ActivityDraft> listDraft(String orgId, String actvId, String refRegId) {
        if (StringUtils.isBlank(orgId) || StringUtils.isBlank(actvId) || StringUtils.isBlank(refRegId)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<ActivityDraft> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ActivityDraft::getOrgId, orgId).eq(ActivityDraft::getActvId, actvId)
                .eq(ActivityDraft::getRefRegId, refRegId);
        return draftMapper.selectList(queryWrapper);
    }

    private void batchUpdateFormData(String orgId, String actvId, String operatorId, String refRegId,String actvRegId) {
        CustomActivityArrangeCompo compo = AomBeanNameUtils.getCustomBean(CustomActivityArrangeCompo.class, refRegId);
        if (compo == null) {
            throw new ApiException(BaseErrorConsts.MISSING_IMPLEMENT);
        }
        List<ActivityDraft> drafts = draftMapper.listDraft4ActvId(orgId, actvId,refRegId);
        Map<String, String> mapFormData = StreamUtil.list2map(drafts, ActivityDraft::getRefId, ActivityDraft::getFormData);
        Map<String, String> map = compo.batchUpdateFormData(orgId, actvId, operatorId, mapFormData,actvRegId);
        if (MapUtils.isEmpty(map)) {
            return;
        }
        map.forEach((refId, formData) -> draftMapper.updateFormData(orgId, actvId, refId, formData));
    }

    private void delete4MigrActv(String orgId, String actvId, String refId) {
        activityMapper.deleteById(refId);

        LambdaQueryWrapper<ActivityDraft> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ActivityDraft::getOrgId, orgId).eq(ActivityDraft::getActvId, actvId)
                .eq(ActivityDraft::getRefId, refId);
        draftMapper.delete(queryWrapper);

        LambdaQueryWrapper<ActivitySource> queryWrapper2 = new LambdaQueryWrapper<>();
        queryWrapper2.eq(ActivitySource::getOrgId, orgId).eq(ActivitySource::getActvId, refId)
                .eq(ActivitySource::getSourceId, actvId);
        sourceMapper.delete(queryWrapper2);

        controlManager.delete4MigrActv(orgId, Sets.newHashSet(refId));
    }

    private void sendEndedMq(MigrActivityItemDataMq mq) {
        MigrActivityItemEndedMq endedMq = new MigrActivityItemEndedMq();
        endedMq.setOrgId(mq.getOrgId());
        endedMq.setActvId(mq.getActivity().getId());
        endedMq.setActvRegId(mq.getActivity().getActvRegId());
        endedMq.setOldItemId(mq.getOldItemId());
        endedMq.setItemId(mq.getItemId());
        endedMq.setRefId(mq.getRefId());
        endedMq.setRefRegId(mq.getRefRegId());
        endedMq.setExt(mq.getExt());
        mqComponent.sendRocketMessage(com.yxt.aom.migr.common.MqConstants.TOPIC_AOM_MIGR_ACTIVITY_ITEM_ENDED,
                JSON.toJSONString(endedMq));
    }

    private void batchSaveActivity(Activity4BatchSave bean, List<ActivityDraft> activities2Create,
                                   List<ActivityDraft> activities2Update, List<ActivityDraft> drafts4Remove,
                                   Map<Long, ActivityItem4Save> itemMap, Map<Long, String> idMap) {
        String orgId = bean.getOrgId();
        String operatorId = bean.getOperatorId();
        List<Activity> activityList = Lists.newArrayList();
        Map<String, Long> taskIdMap = Maps.newHashMap();
        if (MapUtils.isNotEmpty(idMap)) {
            activities2Create.forEach(activityDraft -> {
                String refId = idMap.get(activityDraft.getItemId());
                if (StringUtils.isBlank(refId)) {
                    //since 6.1, 一个itemId下可能会有多个活动, map key改为草稿id
                    refId = idMap.get(activityDraft.getId());
                }
                if (StringUtils.isBlank(refId)) {
                    return;
                }
                activityDraft.setRefId(refId);
                updateDraftStatus(operatorId, activityDraft);
                ActivityItem4Save item = itemMap.get(activityDraft.getItemId());
                if (item != null) {
                    activityList.add(toActivity(bean, refId, item));
                    taskIdMap.put(refId, item.getItemId());
                }
            });
        }
        List<ActivityDraft> updateList = activities2Create.stream().filter(t -> StringUtils.isNotBlank(t.getRefId()))
                .collect(Collectors.toList());
        activities2Update.forEach(activityDraft -> updateDraftStatus(operatorId, activityDraft));
        updateList.addAll(activities2Update);
        if (CollectionUtils.isNotEmpty(updateList)) {
            draftMapper.batchUpdate(updateList);
        }
        if (CollectionUtils.isNotEmpty(drafts4Remove)) {
            draftMapper.deleteByIds(orgId, operatorId,
                    drafts4Remove.stream().map(ActivityDraft::getId).collect(Collectors.toSet()));
            Set<String> actvIds = drafts4Remove.stream().map(ActivityDraft::getRefId).filter(StringUtils::isNotBlank)
                    .collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(actvIds)) {
                sourceMapper.deleteByActvIdsAndSourceId(orgId, actvIds, bean.getActvId(), operatorId);
                deleteActvsConditional(bean, actvIds);
            }
        }
        batchSave(activityList, taskIdMap);
    }

    private void deleteActvsConditional(Activity4BatchSave bean, Set<String> actvIds) {
        RegistryConfigBean regConfig = registryManager.getRegistryConfig(bean.getRefRegId());
        if (regConfig != null && !Objects.equals(regConfig.getUsageType(), Constants.INT_1)) {
            // 除了引用型活动，其它类型活动都要删除
            AomActivityDelReq activityDelReq = new AomActivityDelReq();
            activityDelReq.setActvIds(Lists.newArrayList(actvIds));
            activityDelReq.setRegId(bean.getRefRegId());
            activityDelReq.setOrgId(bean.getOrgId());
            activityDelReq.setOptUserId(bean.getOperatorId());
            activityLifeCycleService.del(activityDelReq);
        }
    }

    private void batchSave(List<Activity> activityList, Map<String, Long> taskIdMap) {
        if (CollectionUtils.isEmpty(activityList)) {
            return;
        }

        List<Activity> existedList = activityMapper.selectBatchIds(activityList.stream().map(Activity::getId).toList());
        if (CollectionUtils.isEmpty(existedList)) {
            activityMapper.batchInsert(activityList);
            sourceMapper.batchInsert(
                    activityList.stream().map(activity -> toActivitySource(activity, taskIdMap.get(activity.getId())))
                            .toList());
            return;
        }

        Map<String, Activity> existedMap = StreamUtil.list2map(existedList, Activity::getId);
        List<Activity> addList = Lists.newArrayList();
        List<Activity> updateList = Lists.newArrayList();
        activityList.forEach(activity -> {
            Activity existedOne = existedMap.get(activity.getId());
            if (existedOne == null) {
                addList.add(activity);
            } else {
                copyFields4Update(existedOne, activity);
                updateList.add(activity);
            }
        });
        if (CollectionUtils.isNotEmpty(addList)) {
            activityMapper.batchInsert(addList);
            sourceMapper.batchInsert(
                    addList.stream().map(activity -> toActivitySource(activity, taskIdMap.get(activity.getId())))
                            .toList());
        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            activityMapper.batchUpdate(updateList);
        }
    }

    private void copyFields4Update(Activity existedOne, Activity updateOne) {
        updateOne.setCreateUserId(existedOne.getCreateUserId());
        updateOne.setCreateTime(existedOne.getCreateTime());
    }

    private ActivitySource toActivitySource(Activity activity, Long itemId) {
        ActivitySource source = new ActivitySource();
        source.setId(snowflakeKeyGenerator.generateKey());
        source.setOrgId(activity.getOrgId());
        source.setActvId(activity.getId());
        source.setSourceId(activity.getSourceId());
        source.setSourceName(activity.getSourceName());
        source.setSourceRegId(activity.getSourceRegId());
        source.setItemId(ObjectUtils.defaultIfNull(itemId, 0L));
        source.setCreateUserId(activity.getCreateUserId());
        source.setUpdateUserId(activity.getUpdateUserId());
        source.setCreateTime(DateUtil.currentTime());
        source.setUpdateTime(source.getCreateTime());
        source.setDeleted(YesOrNo.NO.getValue());
        source.setSync(YesOrNo.YES.getValue());
        return source;
    }

    private Activity toActivity(Activity4BatchSave bean, String refId, ActivityItem4Save item) {
        Activity activity = new Activity();
        activity.setId(refId);
        activity.setOrgId(bean.getOrgId());
        activity.setActvRegId(bean.getRefRegId());
        activity.setActvName(StringUtils.truncate(item.getRefName(), INT_200));
        activity.setActvType(ActivityTypeEnum.ACTV.getType());
        activity.setActvStatus(AomActivityStatusEnum.DRAFT.getType());
        activity.setTimeModel(item.getTimeModel());
        activity.setStartTime(item.getStartTime());
        activity.setEndTime(item.getEndTime());
        activity.setStartDayOffset(item.getStartDayOffset());
        activity.setEndDayOffset(item.getEndDayOffset());
        activity.setImageUrl(StringUtils.EMPTY);
        activity.setDescription(StringUtils.EMPTY);
        activity.setDesignerId(bean.getDesignerId());
        activity.setSourceId(ObjectUtils.defaultIfNull(bean.getActvId(), StringUtils.EMPTY));
        activity.setSourceName(ObjectUtils.defaultIfNull(bean.getActvName(), StringUtils.EMPTY));
        activity.setSourceRegId(ObjectUtils.defaultIfNull(bean.getActvRegId(), StringUtils.EMPTY));
        activity.setModelId(StringUtils.EMPTY);
        activity.setSceneId(StringUtils.EMPTY);
        activity.setCategoryId(StringUtils.EMPTY);
        activity.setAutoEnd(YesOrNo.NO.getValue());
        activity.setAuditEnabled(YesOrNo.NO.getValue());
        activity.setAuditStatus(2);
        activity.setCreateUserId(bean.getOperatorId());
        activity.setUpdateUserId(bean.getOperatorId());
        activity.setCreateTime(DateUtil.currentTime());
        activity.setUpdateTime(activity.getCreateTime());
        activity.setDeleted(YesOrNo.NO.getValue());
        //先设置默认值 db不报错
        activity.setVeryImportant(YesOrNo.NO.getValue());
        activity.setAutoArchive(YesOrNo.NO.getValue());
        activity.setUsageType(YesOrNo.NO.getValue());
        activity.setSourceType(YesOrNo.NO.getValue());
        activity.setPlanId(StringUtils.EMPTY);
        activity.setPublicActv(YesOrNo.NO.getValue());
        activity.setProgressSync(YesOrNo.NO.getValue());
        return activity;
    }

    private Activity toActivity(MigrActivityItemDataMq mq) {
        Activity4Migr activity4Migr = mq.getActivity();
        Activity activity = new Activity();
        activity.setId(mq.getRefId());
        activity.setOrgId(mq.getOrgId());
        activity.setActvRegId(mq.getRefRegId());
        activity.setActvName(StringUtils.truncate(mq.getRefName(), INT_200));
        activity.setActvType(ActivityTypeEnum.ACTV.getType());
        activity.setActvStatus(activity4Migr.getActvStatus());
        activity.setTimeModel(mq.getTimeModel());
        activity.setStartTime(mq.getStartTime());
        activity.setEndTime(mq.getEndTime());
        activity.setStartDayOffset(mq.getStartDayOffset());
        activity.setEndDayOffset(mq.getEndDayOffset());
        activity.setImageUrl(StringUtils.EMPTY);
        activity.setDescription(StringUtils.EMPTY);
        activity.setDesignerId(activity4Migr.getDesignerId());
        activity.setSourceId(ObjectUtils.defaultIfNull(activity4Migr.getId(), StringUtils.EMPTY));
        activity.setSourceName(ObjectUtils.defaultIfNull(activity4Migr.getActvName(), StringUtils.EMPTY));
        activity.setSourceRegId(ObjectUtils.defaultIfNull(activity4Migr.getActvRegId(), StringUtils.EMPTY));
        activity.setModelId(StringUtils.EMPTY);
        activity.setSceneId(StringUtils.EMPTY);
        activity.setCategoryId(StringUtils.EMPTY);
        activity.setAutoEnd(YesOrNo.NO.getValue());
        activity.setAuditEnabled(YesOrNo.NO.getValue());
        activity.setAuditStatus(2);
        activity.setCreateUserId(ObjectUtils.defaultIfNull(mq.getCreateUserId(), StringUtils.EMPTY));
        activity.setUpdateUserId(ObjectUtils.defaultIfNull(mq.getUpdateUserId(), StringUtils.EMPTY));
        activity.setCreateTime(ObjectUtils.defaultIfNull(mq.getCreateTime(), DateUtil.currentTime()));
        activity.setUpdateTime(ObjectUtils.defaultIfNull(mq.getUpdateTime(), DateUtil.currentTime()));
        activity.setDeleted(YesOrNo.NO.getValue());
        activity.setVeryImportant(YesOrNo.NO.getValue());
        activity.setAutoArchive(YesOrNo.NO.getValue());
        activity.setUsageType(YesOrNo.NO.getValue());
        activity.setSourceType(YesOrNo.NO.getValue());
        activity.setPlanId(StringUtils.EMPTY);
        activity.setPublicActv(YesOrNo.NO.getValue());
        activity.setProgressSync(YesOrNo.NO.getValue());
        return activity;
    }

    private ActivityDraft copyDraft(ActivityDraft4BatchCopy bean, ActivityDraft fromDraft, ActivityDraft4Copy data,
                                    Map<Long, String> refIdMap) {
        ActivityDraft toDraft = new ActivityDraft();
        long id = snowflakeKeyGenerator.generateKey();
        toDraft.setId(id);
        toDraft.setOrgId(bean.getToOrgId());
        toDraft.setActvId(bean.getToActvId());
        toDraft.setRefRegId(bean.getRefRegId());
        toDraft.setRefId(StringUtils.EMPTY);
        toDraft.setFormData(updateName(fromDraft.getFormData(), data.getNewName()));
        toDraft.setItemId(data.getToItemId());
        toDraft.setDraftStatus(DraftStatusEnum.DRAFT.getType());
        toDraft.setCreateUserId(bean.getOperatorId());
        toDraft.setUpdateUserId(bean.getOperatorId());
        toDraft.setCreateTime(DateUtil.currentTime());
        toDraft.setUpdateTime(toDraft.getCreateTime());
        toDraft.setDeleted(YesOrNo.NO.getValue());
        refIdMap.put(id,
                StringUtils.isBlank(fromDraft.getRefId()) ? String.valueOf(fromDraft.getId()) : fromDraft.getRefId());
        return toDraft;
    }

    private String updateName(String formData, String newName) {
        if (StringUtils.isBlank(formData) || StringUtils.isBlank(newName)) {
            return formData;
        }
        JSONObject jsonObject;
        try {
            jsonObject = JSON.parseObject(formData);
        } catch (Exception e) {
            jsonObject = null;
            log.error("JSON.parseObject {} error", formData, e);
        }
        if (jsonObject == null || !jsonObject.containsKey(NAME)) {
            return formData;
        }
        jsonObject.put(NAME, newName);
        return jsonObject.toJSONString();
    }

    private void updateDraftStatus(String operatorId, ActivityDraft activityDraft) {
        activityDraft.setDraftStatus(DraftStatusEnum.FORMAL.getType());
        activityDraft.setUpdateUserId(operatorId);
        activityDraft.setUpdateTime(DateUtil.currentTime());
    }

    private Map<Long, String> batchSaveDraft(String orgId, String actvId, String operatorId,
                                             List<ActivityDraft4Save> datas, String refRegId) {
        AomDataSourceTypeHolder.set(refRegId);
        Set<Long> itemIds = datas.stream().map(ActivityDraft4Save::getItemId).collect(Collectors.toSet());
        List<ActivityDraft> existedDrafts = listDraft(orgId, actvId, itemIds);
        final List<ActivityDraft> addList = Lists.newArrayList();
        final List<ActivityDraft> updateList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(existedDrafts)) {
            addList.addAll(
                    datas.stream().map(data -> toActivityDraft(orgId, actvId, operatorId, data, refRegId)).toList());
        } else {
            Map<Long, List<ActivityDraft>> groupedMap = existedDrafts.stream()
                    .collect(Collectors.groupingBy(ActivityDraft::getItemId));
            datas.forEach(data -> {
                ActivityDraft existedOne = findDraftFromList(data.getRefId(), groupedMap.get(data.getItemId()));
                if (existedOne != null) {
                    existedOne.setDraftStatus(StringUtils.equals(data.getFormData(), existedOne.getFormData()) ?
                            DraftStatusEnum.FORMAL.getType() :
                            DraftStatusEnum.DRAFT.getType());
                    existedOne.setFormData(data.getFormData());
                    existedOne.setUpdateUserId(operatorId);
                    existedOne.setUpdateTime(DateUtil.currentTime());
                    updateList.add(existedOne);
                } else {
                    addList.add(toActivityDraft(orgId, actvId, operatorId, data, refRegId));
                }
            });
        }
        if (CollectionUtils.isNotEmpty(addList)) {
            draftMapper.batchInsert(addList);
        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            draftMapper.batchUpdate(updateList);
        }
        //由调用方确保每个itemId一批次顶多创建一个草稿
        return Stream.of(addList, updateList).flatMap(List::stream).collect(Collectors.toMap(ActivityDraft::getItemId,
                t -> StringUtils.isNotBlank(t.getRefId()) ? t.getRefId() : String.valueOf(t.getId()), (v1, v2) -> v1));
    }

    private ActivityDraft findDraftFromList(String refId, List<ActivityDraft> list) {
        if (StringUtils.isBlank(refId) || CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.stream()
                .filter(t -> StringUtils.equals(String.valueOf(t.getId()), refId) || StringUtils.equals(t.getRefId(),
                        refId)).findFirst().orElse(null);
    }

    private ActivityDraft findDraft(String orgId, String actvId, Long itemId, String refId) {
        LambdaQueryWrapper<ActivityDraft> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ActivityDraft::getOrgId, orgId).eq(ActivityDraft::getActvId, actvId)
                .eq(ActivityDraft::getItemId, itemId).eq(ActivityDraft::getDeleted, YesOrNo.NO.getValue());
        if (StringUtils.isNotBlank(refId)) {
            queryWrapper.eq(ActivityDraft::getRefId, refId);
        }
        queryWrapper.last(AomConstants.LIMIT_1);
        return draftMapper.selectOne(queryWrapper);
    }

    private List<ActivityDraft> listDraft(String orgId, String actvId, Set<Long> itemIds) {
        if (CollectionUtils.isEmpty(itemIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<ActivityDraft> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ActivityDraft::getOrgId, orgId).eq(ActivityDraft::getActvId, actvId)
                .in(ActivityDraft::getItemId, itemIds).eq(ActivityDraft::getDeleted, YesOrNo.NO.getValue());
        return draftMapper.selectList(queryWrapper);
    }

    private ActivityDraft toActivityDraft(String orgId, String actvId, String operatorId, ActivityDraft4Save bean,
                                          String refRegId) {
        ActivityDraft draft = new ActivityDraft();
        draft.setId(snowflakeKeyGenerator.generateKey());
        draft.setOrgId(orgId);
        draft.setActvId(actvId);
        draft.setRefRegId(refRegId);
        draft.setRefId(StringUtils.EMPTY);
        draft.setFormData(bean.getFormData());
        draft.setItemId(bean.getItemId());
        draft.setDraftStatus(DraftStatusEnum.DRAFT.getType());
        draft.setCreateUserId(operatorId);
        draft.setUpdateUserId(operatorId);
        draft.setCreateTime(DateUtil.currentTime());
        draft.setUpdateTime(draft.getCreateTime());
        draft.setDeleted(YesOrNo.NO.getValue());
        return draft;
    }

    private ActivityDraft toActivityDraft(MigrActivityItemDataMq mq, Activity4Migr activity4Migr) {
        ActivityDraft draft = new ActivityDraft();
        draft.setId(snowflakeKeyGenerator.generateKey());
        draft.setOrgId(mq.getOrgId());
        draft.setActvId(activity4Migr.getId());
        draft.setRefRegId(mq.getRefRegId());
        draft.setRefId(mq.getRefId());
        draft.setFormData(mq.getFormData());
        draft.setItemId(mq.getItemId());
        draft.setDraftStatus(DraftStatusEnum.FORMAL.getType());
        draft.setCreateUserId(ObjectUtils.defaultIfNull(mq.getCreateUserId(), StringUtils.EMPTY));
        draft.setUpdateUserId(ObjectUtils.defaultIfNull(mq.getUpdateUserId(), StringUtils.EMPTY));
        draft.setCreateTime(ObjectUtils.defaultIfNull(mq.getCreateTime(), DateUtil.currentTime()));
        draft.setUpdateTime(ObjectUtils.defaultIfNull(mq.getUpdateTime(), DateUtil.currentTime()));
        draft.setDeleted(YesOrNo.NO.getValue());
        return draft;
    }

    private void updateRefIdAndFormData(ActivityDraft4Check bean, List<ActivityDraft> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        if (StringUtils.isBlank(bean.getRefId())) {
            ActivityDraft entity = list.get(0);
            bean.setRefId(entity.getRefId());
            bean.setFormData(entity.getFormData());
            return;
        }
        for (ActivityDraft entity : list) {
            if (StringUtils.equals(bean.getRefId(), entity.getRefId())) {
                bean.setFormData(entity.getFormData());
                break;
            }
        }
    }

    private RefIdMap toRefIdMap(ActivityDraft toDraft, Map<Long, Long> toFromItemMap, Map<Long, String> refIdMap) {
        RefIdMap result = new RefIdMap();
        result.setToItemId(toDraft.getItemId());
        result.setFromItemId(toFromItemMap.get(toDraft.getItemId()));
        result.setToRefId(String.valueOf(toDraft.getId()));
        result.setFromRefId(refIdMap.get(toDraft.getId()));
        return result;
    }

    private ActivityDraft copyActivityDraft(ActivityDemoCopyReq bean, ActivityDemoCopyRsp rsp, ActivityDraft srcDraft) {
        Map<String, String> userMap = bean.getUserMap();
        ActivityDraft tgtDraft = new ActivityDraft();
        BeanCopierUtil.copy(srcDraft, tgtDraft, false);
        tgtDraft.setId(snowflakeKeyGenerator.generateKey());
        tgtDraft.setOrgId(bean.getTgtOrgId());
        tgtDraft.setActvId(bean.getTgtProjectId());
        tgtDraft.setItemId(bean.getTgtItemId());
        tgtDraft.setRefId(rsp.getTgtActvId());
        tgtDraft.setFormData(rsp.getTgtFormData());
        tgtDraft.setCreateUserId(userMap.getOrDefault(srcDraft.getCreateUserId(), srcDraft.getCreateUserId()));
        tgtDraft.setUpdateUserId(userMap.getOrDefault(srcDraft.getUpdateUserId(), srcDraft.getUpdateUserId()));
        return tgtDraft;
    }

    private Activity copyActivity(ActivityDemoCopyReq bean, ActivityDemoCopyRsp rsp, Activity srcActivity) {
        Map<String, String> userMap = bean.getUserMap();
        Activity tgtActivity = new Activity();
        BeanCopierUtil.copy(srcActivity, tgtActivity, false);
        tgtActivity.setId(rsp.getTgtActvId());
        tgtActivity.setOrgId(bean.getTgtOrgId());
        tgtActivity.setSourceId(bean.getTgtProjectId());
        tgtActivity.setCreateUserId(userMap.getOrDefault(srcActivity.getCreateUserId(), srcActivity.getCreateUserId()));
        tgtActivity.setUpdateUserId(userMap.getOrDefault(srcActivity.getUpdateUserId(), srcActivity.getUpdateUserId()));
        return tgtActivity;
    }

    private ActivitySource copyActivitySource(ActivityDemoCopyReq bean, ActivityDemoCopyRsp rsp,
                                              ActivitySource srcActivitySource) {
        Map<String, String> userMap = bean.getUserMap();
        ActivitySource tgtActivitySource = new ActivitySource();
        BeanCopierUtil.copy(srcActivitySource, tgtActivitySource, false);
        tgtActivitySource.setId(snowflakeKeyGenerator.generateKey());
        tgtActivitySource.setActvId(rsp.getTgtActvId());
        tgtActivitySource.setSourceId(bean.getTgtProjectId());
        tgtActivitySource.setOrgId(bean.getTgtOrgId());
        tgtActivitySource.setCreateUserId(
                userMap.getOrDefault(srcActivitySource.getCreateUserId(), srcActivitySource.getCreateUserId()));
        tgtActivitySource.setUpdateUserId(
                userMap.getOrDefault(srcActivitySource.getUpdateUserId(), srcActivitySource.getUpdateUserId()));
        return tgtActivitySource;
    }
}
