package com.yxt.aom.base.bean.part.group;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @createDate 2025/5/27 10:33:04
 */
@Data
public class GroupScoreWithdrawReq {
    @NotNull
    @Schema(description = "UACD注册表中定义Id")
    private String regId;
    @NotNull
    @Schema(description = "小组id")
    private String groupId;
    @NotNull
    @Schema(description = "小组积分记录id")
    private String groupScoreId;
}
