package com.yxt.aom.base.bean.control;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class OverViewUserScheduleCriteria {
    private List<String> mainOrgIds;
    private String orgId;
    private String actvId;
    private Integer required;
    private Long groupId;
    private String keyword;
    private List<String> deptIds;
    private String taskFilters;
    private List<String> studentIds;
    private List<String> excludeStudentIds;
    private int contactFlag = 0 ;
    private List<String> userIds  ;
    private Integer formal;
    private Integer status;

    /**
     * /完成标准 默认0 完成所有必修 1完成所有任务 2完成项目内指定任务数量 3完成项目内指定阶段数量 4获得项目内指定学分
     */
    private Integer studyStandard;

    private String projectEndTime;
}
