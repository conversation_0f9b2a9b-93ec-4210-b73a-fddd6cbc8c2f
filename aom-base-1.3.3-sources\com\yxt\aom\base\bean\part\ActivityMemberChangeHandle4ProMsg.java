package com.yxt.aom.base.bean.part;

import com.yxt.aom.base.enums.AomActivityMemberChangeEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Getter
@Setter
public class ActivityMemberChangeHandle4ProMsg {
    private String orgId;
    private String actvId;
    private String regId;
    private String optUserId;
    private Date currentTime;
    private Integer actvType;
    private List<String> addUserIds;
    private List<String> recoveryUserIds;
    /**
     * 旁听正式转换时，真实变更的人
     */
    private List<String> changeUserIds;
    private Integer actvStatus;
    private AomAddActivityMemberMsgBean notifyMemberMsg;
    private PartUserChangeBean bean;
    private AomActivityMemberChangeEnum eventType;
}
