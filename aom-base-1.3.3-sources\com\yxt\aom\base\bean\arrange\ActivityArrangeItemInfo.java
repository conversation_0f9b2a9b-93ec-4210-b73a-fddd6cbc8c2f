package com.yxt.aom.base.bean.arrange;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/11/13  11:10
 * @description item信息
 */
@Data
public class ActivityArrangeItemInfo {

    /**
     * itemId
     */
    private Long id;

    /**
     * 节点名称
     */
    private String itemName;

    /**
     * 节点类型
     */
    private Integer itemType;

    /**
     * 父级id
     */
    private Long parentId;

    /**
     * 父节点类型
     */
    private Integer parentItemType;

    /**
     * 父亲的父节点id
     */
    private Long grandParentId;

}
