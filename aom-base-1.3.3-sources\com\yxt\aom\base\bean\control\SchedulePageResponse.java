package com.yxt.aom.base.bean.control;

import com.yxt.bifrost.udp.localization.L10NContent;
import com.yxt.bifrost.udp.localization.annotation.L10NProperty;
import com.yxt.bifrost.udp.localization.enums.ShapeEnum;
import com.yxt.udpfacade.bean.enums.ResourceTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.Collections;
import java.util.List;

import static com.google.common.collect.Lists.newArrayList;

@Getter
@Setter
public class SchedulePageResponse extends UserCompletedSettingBase implements L10NContent {

    public static final List<String> EXPORT_SCHEDULE_MEMBER_HEADERS = Collections.unmodifiableList(
            newArrayList("userFullName", "userName", "status", "deptName", "positionName",
                    "completeProcessRate", "completeProcessRateStr", "completedAndNeedTotal", "requiredProgress", "completedAndRequiredTask",
                    //"ojtRate", "ojtTaskCompleteNum",
                    "duration"));

    public static final List<String> EXPORT_SCHEDULE_DEPT_HEADERS = Collections.unmodifiableList(
            newArrayList("name", "completeProcessRate", "completedAndNeedTotal", "requiredProgress", "completedAndRequiredTask",
                    //"ojtRate", "ojtTaskCompleteNum",
                    "duration"));

    @Schema(description = "用户id、小组id、部门id")
    @L10NProperty(resourceType = ResourceTypeEnum.USER, shape = ShapeEnum.KEY)
    private String id;

    @Schema(description = "用户（中文）、小组、部门名称")
    @L10NProperty(resourceType = ResourceTypeEnum.USER, shape = ShapeEnum.VALUE)
    private String name;

    @Schema(description = "部门全路径")
    private String totalName;

    @Schema(description = "进度")
    private String rate;

    @Schema(description = "时长（min）")
    private String duration;

    @Schema(description = "明细")
    private List<ScheduleDetailDto> details;

    @Schema(description = "用户账号")
    private String userName;

    @Schema(description = "账号状态 0:禁用 1启用 2 删除")
    private Integer status;
    @L10NProperty(resourceType = ResourceTypeEnum.DEPT, shape = ShapeEnum.KEY)
    private String deptId;
    @Schema(description = "部门名称")
    @L10NProperty(resourceType = ResourceTypeEnum.DEPT, shape = ShapeEnum.VALUE)
    private String deptName;

    @L10NProperty(resourceType = ResourceTypeEnum.POSITION, shape = ShapeEnum.KEY)
    private String positionId;
    @Schema(description = "岗位名称")
    @L10NProperty(resourceType = ResourceTypeEnum.POSITION, shape = ShapeEnum.VALUE)
    private String positionName;

    @Schema(description = "带教进度")
    private String ojtRate;

    @Schema(description = "带教任务总数 ", example = "10")
    private int ojtTaskCount;

    @Schema(description = "带教任务完成数量 ", example = "5")
    private int ojtCompleteCount;

}
