package com.yxt.aom.activity.controller.control;

import com.yxt.aom.activity.facade.bean.control.ResultCopyReq;
import com.yxt.aom.activity.facade.client.ActivityControlClient;
import com.yxt.common.annotation.Auth;
import com.yxt.common.enums.AuthType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

/**
 * 活动结果
 */
@Tag(name = "活动结果")
@RestController
@RequiredArgsConstructor
public class ActivityResultController implements ActivityControlClient {
    @Override
    @Operation(summary = "复制活动结果")
    @PostMapping(value = "/aom/external/activityresults/copy", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @Auth(type = AuthType.AKSK)
    public void copyActivityResult(ResultCopyReq bean) {

    }
}
