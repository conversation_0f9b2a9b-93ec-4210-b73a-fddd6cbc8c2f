package com.yxt.aom.base.bean.common;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.yxt.aom.base.bean.config.DynamicUserGroupConfig;
import com.yxt.aom.base.bean.config.ImConfig;
import com.yxt.aom.base.common.BaseErrorConsts;
import com.yxt.common.annotation.timezone.DateFormatField;
import com.yxt.common.validation.constraints.IntegerScope;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.Date;
import java.util.Set;

/**
 * Activity4Create
 */
@Data
public class Activity4Create {
    /**
     * 主键id
     */
    @JsonIgnore
    private String id;

    /**
     * 机构id
     */
    @JsonIgnore
    private String orgId;

    /**
     * 活动/项目名称
     */
    @Schema(description = "活动/项目名称", example = "项目名称test", requiredMode = Schema.RequiredMode.REQUIRED)
    @Size(max = 200, message = BaseErrorConsts.ACTV_NAME_SIZE)
    private String actvName;

    /**
     * 类型(1-活动, 2-项目)
     */
    @Schema(description = "类型(1-活动, 2-项目)", example = "2", requiredMode = Schema.RequiredMode.REQUIRED)
    @IntegerScope(scope = {1, 2}, message = BaseErrorConsts.ACTV_TYPE_SCOPE)
    private Integer actvType;

    /**
     * 状态(0-未保存, 1-未发布, 2-进行中, 3-已结束, 4-归档中, 41-已归档, 42-归档超过一年, 5-已删除, 6-已暂停, 7-已撤回)
     */
    @JsonIgnore
    private Integer actvStatus;

    /**
     * 时间模式(0-固定, 1-相对)
     */
    @Schema(description = "时间模式(0-固定, 1-相对)", example = "0", requiredMode = Schema.RequiredMode.REQUIRED)
    @IntegerScope(scope = {0, 1}, message = BaseErrorConsts.ACTV_TIMEMODEL_SCOPE)
    private Integer timeModel;

    /**
     * 固定开始时间
     */
    @Schema(description = "固定开始时间 格式:yyyy-MM-dd HH:mm:ss", example = "2024-10-25 10:56:47")
    @DateFormatField(isDate = true)
    private Date startTime;

    /**
     * 固定截止时间
     */
    @Schema(description = "固定截止时间 格式:yyyy-MM-dd HH:mm:ss", example = "2024-11-25 10:56:47")
    @DateFormatField(isDate = true)
    private Date endTime;

    /**
     * 相对开始天数
     */
    @Schema(description = "相对开始天数", example = "0")
    private Integer startDayOffset;

    /**
     * 相对截止天数
     */
    @Schema(description = "相对截止天数", example = "7")
    private Integer endDayOffset;

    /**
     * 封面
     */
    @Schema(description = "封面", example = "https://stc.yxt.com/ufd/55a3e0/o2o/pc/other/project.png")
    @Size(max = 500, message = BaseErrorConsts.ACTV_IMAGEURL_SIZE)
    private String imageUrl;

    /**
     * 简介
     */
    @Schema(description = "简介", example = "简介test")
    @Size(max = 65535, message = BaseErrorConsts.ACTV_DESC_SIZE)
    private String description;

    /**
     * 活动/项目的具体类型(UACD注册表中定义)
     */
    @Schema(description = "活动/项目的具体类型(UACD注册表中定义)", example = "proj_o2o", requiredMode = Schema.RequiredMode.REQUIRED)
    private String actvRegId;

    /**
     * UACD设计器id
     */
    @JsonIgnore
    private Long designerId;

    /**
     * 来源id
     */
    @JsonIgnore
    private String sourceId;

    /**
     * 来源名称
     */
    @JsonIgnore
    private String sourceName;

    /**
     * 来源的具体类型
     */
    @JsonIgnore
    private String sourceRegId;

    /**
     * 模型id
     */
    @Schema(description = "模型id", example = "ac0a267c-044e-4a28-b489-6be16e0ab781")
    private String modelId;

    /**
     * 场景id
     */
    @Schema(description = "场景id", example = "ac0a267c-044e-4a28-b489-6be16e0ab783")
    private String sceneId;

    /**
     * 分类id
     */
    @Schema(description = "分类id", example = "1849276367002220000")
    private String categoryId;

    /**
     * 是否自动结束(0-否, 1-是; 默认为0)
     */
    @Schema(description = "是否自动结束(0-否, 1-是; 默认为0)", example = "0")
    @IntegerScope(scope = {0, 1}, message = BaseErrorConsts.ACTV_AUTOEND_SCOPE)
    private Integer autoEnd;

    /**
     * 是否自动归档(0-否, 1-是; 默认为0)
     */
    @Schema(description = "是否自动归档(0-否, 1-是; 默认为0)", example = "0")
    @IntegerScope(scope = {0, 1}, message = BaseErrorConsts.ACTV_AUTOARCHIVE_SCOPE)
    private Integer autoArchive;

    /**
     * 是否开启审核(0-未开启, 1-开启; 默认为0)
     */
    @Schema(description = "是否开启审核(0-未开启, 1-开启; 默认为0)", example = "0")
    @IntegerScope(scope = {0, 1}, message = BaseErrorConsts.ACTV_AUDITENABLED_SCOPE)
    private Integer auditEnabled;

    /**
     * 审核状态(0-待审核, 1-审核中, 2-已通过, 3-未通过, 4-已撤回; 默认为0)
     */
    @JsonIgnore
    private Integer auditStatus;

    /**
     * 审核通过后是否自动发布(true-自动发布, false-不自动发布; 默认为false)
     */
    @Schema(description = "审核通过后是否自动发布(true-自动发布, false-不自动发布; 默认为false)", example = "false")
    private Boolean autoRelease;

    /**
     * 是否重要(0-不是，1-是)
     */
    @Schema(description = "是否重要(0-不是，1-是; 默认为0)", example = "0")
    @IntegerScope(scope = {0, 1}, message = BaseErrorConsts.ACTV_VERYIMPORTANT_SCOPE)
    private Integer veryImportant;

    /**
     * 用途类别(0-普通项目/活动, 1-项目/活动模板, 2-集团化项目/活动, 3-移动端项目/活动; 默认为0; 3目前已停用)
     */
    @Schema(description = "用途类别(0-普通项目/活动, 1-项目/活动模板, 2-集团化项目/活动, 3-移动端项目/活动; 默认为0; 3目前已停用)", example = "0")
    @IntegerScope(scope = {0, 1, 2}, message = BaseErrorConsts.ACTV_USAGETYPE_SCOPE)
    private Integer usageType = 0;

    @Schema(description = "来源类型(0独立安排,10岗位地图安排,11层级地图安排,12人才盘点安排,13人才池安排,14个人发展计划)", example = "0")
    @IntegerScope(scope = {0, 10, 11, 12, 13, 14}, message = BaseErrorConsts.ACTV_SOURCETYPE_SCOPE)
    private Integer sourceType;

    /**
     * 创建人id
     */
    @JsonIgnore
    private String createUserId;

    /**
     * 更新人id
     */
    @JsonIgnore
    private String updateUserId;

    /**
     * 创建时间
     */
    @JsonIgnore
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonIgnore
    private Date updateTime;

    /**
     * 是否删除(0-未删除, 1-已删除)
     */
    @JsonIgnore
    private Integer deleted;

    /**
     * 负责人id列表
     */
    @Schema(description = "负责人id列表", example = "[\"ac0a267c-044e-4a28-b489-6be16e0ab700\",\"ac0a267c-044e-4a28-b489-6be16e0ab711\"]")
    @Size(max = 30, message = "apis.aom.activity.mgrUserIds.Size2")
    private Set<String> mgrUserIds;

    /**
     * 负责人用户组id
     */
    @Schema(description = "负责人动态用户组id", example = "ac0a267c-044e-4a28-b489-6be16e0ab700")
    private String ownerDynamicUserGroupId;

    /**
     * 群聊设置
     */
    @Schema(description = "群聊设置")
    private ImConfig imConfig = new ImConfig();

    /**
     * 动态用户组配置
     */
    @Schema(description = "动态用户组配置")
    private DynamicUserGroupConfig dynamicUserGroupConfig;

    /**
     * 组织部门id列表
     */
    @Schema(description = "组织部门id列表，最多支持50个部门")
    private Set<String> orgDeptIds;

    /**
     * 开启多班次任务(0-关闭, 1-开启)
     */
    @Schema(description = "开启多班次任务(0-关闭, 1-开启)", example = "0")
    private Integer enableMultiShiftTasks;

    /**
     * 皮肤id
     */
    @Schema(description = "皮肤id", example = "ac0a267c-044e-4a28-b489-6be16e0ab653")
    private String skinId;

    /**
     * 计划id
     */
    @Schema(description = "计划id", example = "1849276367002225555")
    private String planId;

    /**
     * 项目/活动创建时使用的模板id
     */
    @Schema(description = "项目/活动创建时使用的模板id", example = "1849276367002220000")
    private String templateId;

    /**
     * 项目/活动编号
     */
    @Schema(description = "项目/活动编号", example = "proj001")
    @Size(max = 255, message = BaseErrorConsts.ACTV_CODE_SIZE)
    private String actvCode;

    /**
     * 是否公开活动(0-不公开, 1-公开)
     */
    @Schema(description = "是否公开活动(0-不公开, 1-公开)", example = "0")
    @IntegerScope(scope = {0, 1}, message = BaseErrorConsts.ACTV_PUBLICACTV_SCOPE)
    private Integer publicActv;

    /**
     * 是否同步在线课堂中的课程学习进度(0-不同步, 1-同步)
     */
    @Schema(description = "是否同步在线课堂中的课程学习进度(0-不同步, 1-同步)", example = "1")
    @IntegerScope(scope = {0, 1}, message = BaseErrorConsts.ACTV_PROGRESSSYNC_SCOPE)
    private Integer progressSync;

    /**
     * 自定义json
     */
    @Schema(description = "自定义json")
    private JSONObject custom;
}
