package com.yxt.aom.base.bean.part;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yxt.common.Constants;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
public class ActivityMember4Add {


    @Schema(description = "活动/项目id")
    @NotNull(message = "apis.aom.part.member.actvId.isNull")
    private String actvId;

    @Schema(description = "userIds")
    @Size(min = 1, message = "apis.aom.part.member.userIds.isNull")
    private List<String> userIds;

    @Schema(description = "学员类型 0-旁听学员 1-正式学员")
    @NotNull(message = "apis.aom.part.member.formal.isNull")
    private Integer formal;

    @Schema(description = "加入方式（1-手动加入 2-自动加入 3-通过报名加入）")
    @NotNull(message = "apis.aom.part.member.joinMethod.isNull")
    private Integer joinMethod;

    @Schema(description = "参与id")
    @NotNull(message = "apis.aom.part.member.partId.isNull")
    private Long participationId;

    @Schema(description = "周期模式设置学员开始时间", example = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = Constants.SDF_YEAR2SECOND, timezone = Constants.STR_GMT8)
    private Date startTimeForCycle;

    @Schema(description = "UACD注册表中定义Id")
    @NotNull(message = "apis.aom.part.member.regId.isNull")
    private String regId;

    @Schema(description = "小组id，项目添加学员不传，小组添加学员传对应小组id")
    private Long groupId = 0L;
}
