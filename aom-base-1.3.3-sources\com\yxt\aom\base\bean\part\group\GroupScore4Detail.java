package com.yxt.aom.base.bean.part.group;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @createDate 2025/5/28 10:12:48
 */
@Data
public class GroupScore4Detail {
    @Schema(description = "主键", example = "id")
    @JsonSerialize(using = ToStringSerializer.class)
    private long id;
    @Schema(description = "小组排名", example = "100")
    private Integer groupRank;
    @Schema(description = "小组积分", example = "100")
    private Integer groupPoint;
}
