package com.yxt.aom.activity.mq;

import com.alibaba.fastjson.JSON;
import com.yxt.aom.activity.service.arrange.ActivityArrangeService;
import com.yxt.aom.base.common.AomConstants;
import com.yxt.aom.base.component.common.AomDataMigrComponent;
import com.yxt.aom.common.config.AomDataSourceTypeHolder;
import com.yxt.aom.common.config.AomDbProperties;
import com.yxt.aom.migr.common.MigrStepEnum;
import com.yxt.aom.migr.mq.MigrActivityItemDataMq;
import com.yxt.aom.migr.mq.common.Activity4Migr;
import com.yxt.dbrouter.core.toolkit.DBRouteHolder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import static com.yxt.aom.migr.common.MqConstants.TOPIC_AOM_MIGR_ACTIVITY_ITEM_DATA;

/**
 * MigrActivityListener
 */
@Slf4j
@RequiredArgsConstructor
@Component
@ConditionalOnProperty(name = "aom.rocketmq.consumer-enabled", havingValue = "true", matchIfMissing = true)
@RocketMQMessageListener(consumerGroup = AomConstants.MQ_GROUP_PREFIX
        + TOPIC_AOM_MIGR_ACTIVITY_ITEM_DATA, topic = TOPIC_AOM_MIGR_ACTIVITY_ITEM_DATA, consumeMode = ConsumeMode.CONCURRENTLY, consumeThreadNumber = 3)
public class MigrActivityListener implements RocketMQListener<String> {
    private final AomDbProperties dbProperties;
    private final ActivityArrangeService arrangeService;
    private final AomDataMigrComponent migrComponent;

    @Override
    public void onMessage(String message) {
        log.info("MigrActivityListener, message: {}", message);
        if (StringUtils.isEmpty(message)) {
            return;
        }
        MigrActivityItemDataMq mq = null;
        try {
            mq = JSON.parseObject(message, MigrActivityItemDataMq.class);
            if (mq != null && dbProperties.getDsmap().containsKey(mq.getRefRegId())) {
                long startTime = System.currentTimeMillis();
                AomDataSourceTypeHolder.set(mq.getRefRegId());
                DBRouteHolder.push(mq.getOrgId());
                arrangeService.migrActivity(mq);
                log.info("MigrActivityListener migrActivity cost : {}", System.currentTimeMillis() - startTime);
            }
        } catch (Exception ex) {
            log.error("MigrActivityListener failed. message: {}, err: ", message, ex);
            if (mq != null && mq.getActivity() != null) {
                Activity4Migr activity = mq.getActivity();
                migrComponent.sendMigrFailedMq(mq.getOrgId(), activity.getId(), activity.getActvRegId(),
                        MigrStepEnum.ACTIVITY, ExceptionUtils.getStackTrace(ex));
            }
        }
    }
}
