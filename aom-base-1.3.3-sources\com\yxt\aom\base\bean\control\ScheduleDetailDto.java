package com.yxt.aom.base.bean.control;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.yxt.aom.base.util.AomNumberUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ScheduleDetailDto {
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "阶段、任务id")
    private Long id;
    @Schema(description = "叶节点引用对象的具体类型(UACD注册表中定义)", example = "actv_exam")
    private String refRegId;
    @Schema(description = "类型国际化key")
    private String typeNameKey;
    @Schema(description = "活动任务类型自定义别名")
    private String actvAlias;
    @Schema(description = "结果状态：0未开始，1进行中，2已完成")
    private Integer resultStatus;
    @Schema(description = "进度")
    private String rate;
    @Schema(description = "是否是阶段")
    private boolean isPeriod;
    @JsonIgnore
    private Integer studentNum;
    @JsonIgnore
    private Integer finishNum;

    @Schema(description = "考试得分")
    private Double score;

    @JsonIgnore
    public void calRate() {
        if (null == this.getStudentNum() || 0 == this.getStudentNum()) {
            this.setRate("0");
            return;
        }
        if (null == this.getFinishNum() || 0 == this.getFinishNum()) {
            this.setRate("0");
            return;
        }
        this.setRate(AomNumberUtils.dividePercent(this.getFinishNum(), this.getStudentNum()).toString());
    }

}
