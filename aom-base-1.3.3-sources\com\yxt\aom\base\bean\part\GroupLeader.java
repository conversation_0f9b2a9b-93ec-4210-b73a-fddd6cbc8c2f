package com.yxt.aom.base.bean.part;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

/**
 * 小组组长
 */
@Getter
@Setter
@NoArgsConstructor
public class GroupLeader {

    @Schema(description = "小组id", example = "id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long groupId;

    @Schema(description = "小组名称", example = "name")
    @JsonSerialize(using = ToStringSerializer.class)
    private String groupName;

    @Schema(description = "组长id", example = "1")
    private String leaderId;

    @Schema(description = "组长账号", example = "1")
    private String leaderUserName;

    @Schema(description = "组长工号", example = "1")
    private String leaderUserNo;

}
