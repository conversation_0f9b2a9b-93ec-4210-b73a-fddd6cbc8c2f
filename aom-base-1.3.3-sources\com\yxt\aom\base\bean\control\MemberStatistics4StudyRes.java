package com.yxt.aom.base.bean.control;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
public class MemberStatistics4StudyRes {

    private String userId;

    @Schema(description = "总任务数")
    private Integer taskCount;

    @Schema(description = "完成任务数")
    private Integer taskCompletedCount;

    @Schema(description = "完成率")
    private BigDecimal taskCompletedRate;
}
