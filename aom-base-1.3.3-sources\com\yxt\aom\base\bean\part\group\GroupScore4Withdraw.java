package com.yxt.aom.base.bean.part.group;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @createDate 2025/5/30 16:27:30
 */
@Data
public class GroupScore4Withdraw {
    @NotNull
    @Schema(description = "UACD注册表中定义Id")
    private String regId;

    @NotNull
    @Schema(description = "小组分配积分id")
    private Long groupScoreId;
}
