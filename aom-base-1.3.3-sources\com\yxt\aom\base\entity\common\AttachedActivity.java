package com.yxt.aom.base.entity.common;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * AttachedActivity
 */
@Data
@TableName("aom_attached_activity")
public class AttachedActivity implements Serializable {
    @Serial
    private static final long serialVersionUID = 3411378171858844332L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 机构id
     */
    private String orgId;

    /**
     * 活动/项目id
     */
    private String actvId;

    /**
     * 活动/项目的具体类型(UACD注册表中定义)
     */
    private String actvRegId;

    /**
     * 活动/项目下叶节点id
     */
    private Long itemId;

    /**
     * 附属活动id
     */
    private String attachedActvId;

    /**
     * 附属活动的具体类型(UACD注册表中定义)
     */
    private String attachedActvRegId;

    /**
     * 附属活动名称
     */
    private String attachedActvName;

    /**
     * 排序
     */
    private Integer orderIndex;

    /**
     * 用途(1-随课练习)
     */
    private Integer usageType;

    /**
     * 创建人Id
     */
    private String createUserId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人Id
     */
    private String updateUserId;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除
     */
    private Integer deleted;
}
