package com.yxt.aom.base.bean.common;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * DesignerModule4Get
 */
@Data
@Schema(name = "设计器模块信息")
public class DesignerModule4Get {
    @Schema(description = "注册ID", example = "actv_exam")
    private String registryId;

    @Schema(description = "微服务地址", example = "https://api-phx-di-hw.yunxuetang.com.cn/ote")
    private String serviceUrl;

    @Schema(description = "活动名称", example = "考试")
    private String moduleName;

    @Schema(description = "活动名称国际化key", example = "pc_o2o_lbl_kngouterlink")
    private String moduleNameI18n;

    @Schema(description = "排序", example = "1")
    private Integer orderIndex;

    @Schema(description = "图标URL", example = "https://stc.yxt.com/ufd/b0174a/ulcd/pc/svg/ulcd-type-108.svg")
    private String iconUrl;

    @Schema(description = "沉浸式学习组件key")
    private String studyComponent;

    @Schema(description = "H5跳转地址")
    private String h5Url;

    @Schema(description = "PC跳转地址")
    private String pcUrl;
}
