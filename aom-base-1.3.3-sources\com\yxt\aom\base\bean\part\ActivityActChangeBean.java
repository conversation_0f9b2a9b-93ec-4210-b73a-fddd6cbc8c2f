package com.yxt.aom.base.bean.part;

import com.yxt.aom.base.entity.part.ActivityAct;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class ActivityActChangeBean {

    private String orgId;

    private String actvId;

    /**
     * 发布时间
     */
    private Date releaseTime;

    /**
     * 首次发布时间
     */
    private Date firstReleaseTime;

    /**
     * 发布人 ID
     */
    private String releaseUserId;

    /**
     * 实际结束时间
     */
    private Date realEndTime;

    /**
     * 结束的人 ID
     */
    private String endUserId;

    /**
     * 归档日期
     */
    private Date archiveDate;

    /**
     * 归档人 ID
     */
    private String archiveUserId;

    private ActivityAct activityAct;

}
