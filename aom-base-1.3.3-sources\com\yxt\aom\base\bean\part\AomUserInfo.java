package com.yxt.aom.base.bean.part;

import lombok.Getter;
import lombok.Setter;

/**
 * 用户信息
 *
 * <AUTHOR>
 * @since 2024/11/5
 */
@Getter
@Setter
public class AomUserInfo {

    /**
     * 用户id
     */
    private String userId;

    /**
     * 用户账号
     */
    private String username;

    /**
     * 用户名称
     */
    private String fullname;

    /**
     * 员工编号(相当于警号、员工工号)
     */
    private String userNo;

    /**
     * 用户状态：用于标识当前用户的状态(0-禁用 1-启用)
     */
    private Integer status;

    /**
     * 是否删除
     */
    private Integer deleted;

    public String getUserNameLowerCase() {
        if(username != null) {
            return username.toLowerCase();
        }
        return null;
    }

    public String getUserNoLowerCase() {
        return userNo.toLowerCase();
    }

}
