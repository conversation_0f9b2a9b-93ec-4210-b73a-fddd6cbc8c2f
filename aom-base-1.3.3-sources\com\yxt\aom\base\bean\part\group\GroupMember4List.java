package com.yxt.aom.base.bean.part.group;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.yxt.bifrost.udp.localization.L10NContent;
import com.yxt.bifrost.udp.localization.annotation.L10NProperty;
import com.yxt.bifrost.udp.localization.enums.ShapeEnum;
import com.yxt.udpfacade.bean.enums.ResourceTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @createDate 2024/12/12 10:45:08
 */
@Data
public class GroupMember4List implements L10NContent {

    @Schema(description = "用户id", example = "123")
    @L10NProperty(resourceType = ResourceTypeEnum.USER, shape = ShapeEnum.KEY)
    private String userId;
    @Schema(description = "账号", example = "123")
    private String username;
    @Schema(description = "姓名", example = "123")
    @L10NProperty(resourceType = ResourceTypeEnum.USER, shape = ShapeEnum.VALUE)
    private String fullname;

    @L10NProperty(resourceType = ResourceTypeEnum.DEPT, shape = ShapeEnum.KEY)
    private String deptId;
    @Schema(description = "部门", example = "123")
    @L10NProperty(resourceType = ResourceTypeEnum.DEPT, shape = ShapeEnum.VALUE)
    private String deptName;

    @L10NProperty(resourceType = ResourceTypeEnum.POSITION, shape = ShapeEnum.KEY)
    private String positionId;
    @Schema(description = "岗位", example = "123")
    @L10NProperty(resourceType = ResourceTypeEnum.POSITION, shape = ShapeEnum.VALUE)
    private String positionList;

    @Schema(description = "选修任务+必修任务完成数量", example = "10")
    private Integer completeAllTaskCount = 0;
    @Schema(description = "选修任务+必修任务总数量", example = "30")
    private Integer allTaskCount = 0;

    @Schema(description = "必修任务完成数量", example = "10")
    private Integer completeRequiredCount = 0;
    @Schema(description = "必修任务总数量", example = "30")
    private Integer requiredTaskCount = 0;

    @Schema(description = "证书数")
    private int certificateNum;

    @Schema(description = "1正式学员 0旁听学员", example = "1")
    private Integer formal;

    /**
     * 导出时使用
     */
    @JsonIgnore
    private String formalName;
    @JsonIgnore
    private String actCompletedRateStr;
    @JsonIgnore
    private String requiredTaskCompletedRateStr;
}
