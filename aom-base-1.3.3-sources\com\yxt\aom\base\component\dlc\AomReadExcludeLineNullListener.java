package com.yxt.aom.base.component.dlc;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.yxt.common.util.ReflectionUtil;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * The type Read exclude line null listener.
 *
 * @param <T> the type parameter
 */
@Slf4j
public class AomReadExcludeLineNullListener<T> implements ReadListener<T> {

    private final List<T> list = new ArrayList<>();

    @Override
    public void invoke(T data, AnalysisContext analysisContext) {
        // 如果一行Excel数据均为空值，则不装载该行数据
        if (isLineNotNullValue(data)) {
            list.add(data);
        }
    }

    /**
     * 判断整行单元格数据是否均为空
     */
    private boolean isLineNotNullValue(T data) {
        try {
            List<Field> fields = Arrays.stream(data.getClass().getDeclaredFields())
                .filter(f -> f.isAnnotationPresent(ExcelProperty.class))
                .collect(Collectors.toList());
            for (Field field : fields) {
                ReflectionUtil.makeAccessible(field);
                Object value = field.get(data);
                if (Objects.nonNull(value)) {
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            log.error("读取数据行[{}]解析失败: {}", data, e.getMessage());
        }
        return true;
    }

    /**
     * Gets list.
     *
     * @return the list
     */
    public List<T> getList() {
        return this.list;
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        log.info("doAfterAllAnalysed");
    }
}
