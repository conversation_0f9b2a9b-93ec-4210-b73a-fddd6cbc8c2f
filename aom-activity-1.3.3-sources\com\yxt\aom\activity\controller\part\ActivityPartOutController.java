package com.yxt.aom.activity.controller.part;

import com.alibaba.fastjson.JSON;
import com.yxt.aom.base.bean.part.cycle.AomActivityArchiveReq;
import com.yxt.aom.base.bean.part.cycle.AomActivityDelReq;
import com.yxt.aom.base.bean.part.cycle.AomActivityEndReq;
import com.yxt.aom.base.bean.part.cycle.AomActivityReleaseReq;
import com.yxt.aom.base.bean.part.cycle.AomActivityWthdrawReq;
import com.yxt.aom.base.service.part.ActivityLifeCycleService;
import com.yxt.aom.common.config.AomDataSourceTypeHolder;
import com.yxt.common.Constants;
import com.yxt.common.annotation.Auth;
import com.yxt.common.annotation.DbHintMaster;
import com.yxt.common.enums.AuthType;
import com.yxt.dbrouter.core.toolkit.DBRouteHolder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

import static com.yxt.aom.base.common.AomPropConstants.AUTO_END;
import static com.yxt.common.Constants.INT_0;

@Tag(name = "活动生命周期-out")
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/aom/external/activity/lifeCycle")
public class ActivityPartOutController {

    private final ActivityLifeCycleService activityLifeCycleService;

    @Operation(summary = "发布")
    @ResponseStatus(HttpStatus.OK)
    @PutMapping(value = "/release", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @Auth(action = Constants.LOG_TYPE_UPDATESINGLE, type = {AuthType.AKSK})
    @DbHintMaster
    public void releaseActivity(@RequestBody @Valid AomActivityReleaseReq bean) {
        DBRouteHolder.push(bean.getOrgId());
        AomDataSourceTypeHolder.set(bean.getRegId());
        log.info("releaseActivity req:{}", JSON.toJSONString(bean));
        activityLifeCycleService.release(bean);
    }

    @Operation(summary = "撤回")
    @ResponseStatus(HttpStatus.OK)
    @PostMapping(value = "/withdraw", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @Auth(action = Constants.LOG_TYPE_UPDATESINGLE, type = {AuthType.AKSK})
    @DbHintMaster
    public void withdrawActivity(@RequestBody @Valid AomActivityWthdrawReq bean) {
        DBRouteHolder.push(bean.getOrgId());
        AomDataSourceTypeHolder.set(bean.getRegId());
        log.info("withdrawActivity req:{}", JSON.toJSONString(bean));
        activityLifeCycleService.withdraw(bean);
    }

    @Operation(summary = "结束")
    @ResponseStatus(HttpStatus.OK)
    @PostMapping(value = "/end", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @Auth(action = Constants.LOG_TYPE_UPDATESINGLE, type = {AuthType.AKSK})
    @DbHintMaster
    public void endActivity(@RequestBody @Valid AomActivityEndReq bean) {
        DBRouteHolder.push(bean.getOrgId());
        AomDataSourceTypeHolder.set(bean.getRegId());
        int autoEnd = getAutoEnd(bean.getParams());
        bean.setAutoEnd(autoEnd);
        log.info("endActivity req:{}", JSON.toJSONString(bean));
        activityLifeCycleService.end(bean);
    }

    private int getAutoEnd(Map<String, Object> params) {
        int res = INT_0;
        if (params != null) {
            Object o = params.get(AUTO_END);
            if (o != null) {
                String s = String.valueOf(o);
                if (StringUtils.isNumeric(s)) {
                    res = Integer.parseInt(s);
                }
            }
        }
        return res;
    }

    @Operation(summary = "归档")
    @ResponseStatus(HttpStatus.OK)
    @PostMapping(value = "/archive", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @Auth(action = Constants.LOG_TYPE_UPDATESINGLE, type = {AuthType.AKSK})
    @DbHintMaster
    public void archiveActivity(@RequestBody @Valid AomActivityArchiveReq bean) {
        DBRouteHolder.push(bean.getOrgId());
        AomDataSourceTypeHolder.set(bean.getRegId());
        log.info("archiveActivity req:{}", JSON.toJSONString(bean));
        activityLifeCycleService.archive(bean);
    }



    @Operation(summary = "删除")
    @ResponseStatus(HttpStatus.OK)
    @PostMapping(value = "/del", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @Auth(action = Constants.LOG_TYPE_UPDATESINGLE, type = {AuthType.AKSK})
    @DbHintMaster
    public void delActivity(@RequestBody @Valid AomActivityDelReq bean) {
        DBRouteHolder.push(bean.getOrgId());
        AomDataSourceTypeHolder.set(bean.getRegId());
        log.info("delActivity req:{}", JSON.toJSONString(bean));
        activityLifeCycleService.del(bean);
    }

}
