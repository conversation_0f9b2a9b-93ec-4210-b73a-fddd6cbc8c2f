package com.yxt.aom.base.bean.config;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;

/**
 * 动态用户组配置
 */
@Data
@Schema(description = "动态用户组配置")
public class DynamicUserGroupConfig {
    @Schema(description = "动态用户组删减人员，自动同步到项目中 0:不同步，1:同步,默认0")
    @Range(min = 0, max = 1)
    @NotNull
    private Integer deleteUserSyncProject = 0;
}
