package com.yxt.aom.base.bean.part;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.util.Date;
import java.util.List;

/**
 * 参与人员类型变更bean
 *
 * <AUTHOR>
 * @since 2024/11/1
 */
@Getter
@Setter
@NoArgsConstructor
@SuperBuilder
public class PartUserChangeBean {

    private String regId;

    /**
     * 机构id
     */
    private String orgId;

    /**
     * 活动id
     */
    private String actvId;

    /**
     * 类型(1-活动, 2-项目)
     */
    private Integer actvType;

    /**
     * 参与id
     */
    private Long participationId;

    /**
     * 学员id列表
     */
    private List<String> userIds;

    /**
     * 学员类型 0-旁听 1-正式
     */
    private Integer formal;

    /**
     * 操作人id
     */
    private String optUserId;

    /**
     * 操作人名称
     */
    private String fullName;

    /**
     * 是否发送消息？
     */
    private Boolean sendMsg = Boolean.FALSE;

    /**
     * 操作时间
     */
    private Date currentTime;

    /**
     * 设置的开始学习时间
     */
    private Date setStartTime;

    /**
     * 设置的开始学习时间
     */
    private Date setEndTime;

    /**
     * 具体活动ids
     */
    private List<String> refIds;

    /**
     * 学员更换小组id
     */
    private Long groupId;

    private String sourceCode;

}
