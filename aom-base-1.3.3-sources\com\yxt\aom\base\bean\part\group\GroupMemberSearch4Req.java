package com.yxt.aom.base.bean.part.group;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @createDate 2024/12/12 10:45:08
 */
@Data
public class GroupMemberSearch4Req {

    @Schema(description = "UACD注册表中定义Id")
    private String regId;

    @Schema(description = "小组id")
    @NotNull(message = "apis.aom.part.groupId.invalid")
    private Long groupId;

    @Schema(description = "姓名/账号")
    private String keyword;

    @Schema(description = "1正式学员 0旁听学员")
    private Integer formal;
}
