package com.yxt.aom.base.bean.sn;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @description:
 * @author: dingjh
 * @date: 2025/6/11 18:14
 */
@Data
public class ActivitySnResponse {
    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED)
    private String id;
    @Schema(description = "阶段/任务组")
    private String parentId;
    @Schema(description = "名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String refName;
    @Schema(description = "节点类型(0-叶节点, 1-目录节点，2-任务组)", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer itemType;
    @Schema(name = "解锁状态（1解锁 0未解锁）", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer lockStatus;
    @Schema(description = "任务解锁时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long unlockTime;
    @Schema(description = "逾期时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long overdueTime;
    @Schema(description = "阶段周期", example = "0")
    private Integer cycleTime;
    @Schema(description = "阶段周期开始天数")
    private Integer cycleStartTime;
    @Schema(description = "阶段周期结束天数")
    private Integer cycleEndTime;
    @Schema(description = "阶段周期模式 0:周期天数模式，1：设置周期起止模式")
    private Integer cycleModel;
}
