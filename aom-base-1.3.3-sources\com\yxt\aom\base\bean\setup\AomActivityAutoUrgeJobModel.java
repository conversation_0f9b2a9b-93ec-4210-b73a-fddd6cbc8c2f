package com.yxt.aom.base.bean.setup;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class AomActivityAutoUrgeJobModel {


    /**
     * 项目模式(0起止时间  1周期)
     */
    private Integer timeType;

    /**
     * 项目结束时间
     */
    private Date endTime;

    /**
     * 项目开始时间
     */
    private Date startTime;

    /**
     * 机构id
     */
    private String orgId;

    /**
     * 活动id
     */
    private String actvId;

    /**
     * 活动name
     */
    private String actvName;

    /**
     * 催促类型 默认0项目 1阶段 2任务
     */
    private Integer urgeType;

    /**
     * 日催促时间点，默认8点
     */
    private Integer urgeTime;

    /**
     * 结束前几天设置
     */
    private Integer urgeBeforeDays;

    /**
     * 配置(非固定时间)是否生效 0不生效 1生效
     */
    private Integer effective;

    /**
     * 固定时间设置标志 0不配置 1配置
     */
    private Integer fixTimeFlag;

    /**
     * 固定时间设置内容
     */
    private String fixTime;

    private String ext;

    private Integer actvType;

    private Integer urgeAudit;

    /** urgeType =4 时有效， 多选：1-直属经理、2-部门经理、21-上一级部门经理、22-上二级部门经理、23-上三级部门经理、3-带教导师、4-团队负责人, 示例 [1,22,3,4] **/
    private String urgeSubType;
    /** urgeType =4 时有效, 1-天, 2-周 , 3-月 **/
    private Integer urgeTimeType;
    /** urgeType =4 且 urgeTimeType = 2、3时有效,  多选，周[1,3,5,7] 月[1,10,28,30,31] **/
    private  String timeRange;

    //4.5新加
    /**
     * urge_time_category=1时使用,多个配置逗号隔开
     */
    private String urgeCycleTimeRange;

    /**
     * 0:按固定某一天催促, 1:周期提醒 2:按自定义周期
     */
    private Integer urgeTimeCategory;

    /**
     * urge_time_category=1时使用,0:天, 1:周 2:月
     */
    private Integer urgeCycleType;

    /**
     * urgeType=4时使用,0:全部 1:未完成
     */
    private Integer urgeRangeWithManager;

    private Integer studyStandard;

    private Integer ojtMode;

    private String customCycleExt;

}
