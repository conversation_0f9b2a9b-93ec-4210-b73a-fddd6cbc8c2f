package com.yxt.aom.base.bean.common;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @createDate 2025/5/29 19:07:15
 */
@Data
public class Audit4Create {

    @Schema(description = "活动/项目的具体类型(UACD注册表中定义)", example = "proj_o2o", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank
    private String regId;

    @Schema(description = "活动id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank
    private String actvId;

    @Schema(description = "发起自选节点,如果是自选，需要传入")
    private String optionalBeans;

    @Min(value = 0)
    @Max(value = 1)
    @Schema(description = "审核通过后是否自动发布（0：否， 1：是）", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer autoRelease;

    @Schema(description = "自定义参数")
    private Map<String, Object> params;
}
