package com.yxt.aom.activity.service.rollup.impl;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yxt.aom.activity.facade.bean.control.ResultCopyReq;
import com.yxt.aom.base.bean.common.ResultCopyMq;
import com.yxt.aom.base.common.AomMqConstants;
import com.yxt.aom.base.component.common.AomMqComponent;
import com.yxt.aom.activity.custom.SingleActivityCompo;
import com.yxt.aom.base.entity.arrange.ActivityArrangeItem;
import com.yxt.aom.base.entity.control.ActivityObjectiveResult;
import com.yxt.aom.base.entity.control.BaseActivityResult;
import com.yxt.aom.base.manager.common.AomRegistryManager;
import com.yxt.aom.base.manager.control.MemberStatisticsManager;
import com.yxt.aom.base.mapper.control.ActivityObjectiveResultMapper;
import com.yxt.aom.base.mapper.control.AssessmentActivityResultMapper;
import com.yxt.aom.base.mapper.control.BaseActivityResultMapper;
import com.yxt.aom.base.mapper.control.ContentActivityResultMapper;
import com.yxt.aom.base.mapper.control.OtherActivityResultMapper;
import com.yxt.aom.base.mapper.control.PracticeActivityResultMapper;
import com.yxt.aom.base.service.control.AbstractActivityResult;
import com.yxt.aom.common.util.AomBeanNameUtils;
import com.yxt.aom.datamodel.activityresult.AssessmentActivityResult;
import com.yxt.aom.datamodel.activityresult.ContentActivityResult;
import com.yxt.aom.datamodel.activityresult.OtherActivityResult;
import com.yxt.aom.datamodel.activityresult.PracticeActivityResult;
import com.yxt.aom.datamodel.common.ActionEnum;
import com.yxt.aom.datamodel.common.Actor;
import com.yxt.aom.datamodel.common.TargetObject;
import com.yxt.aom.datamodel.event.ActivityResultEvent;
import com.yxt.common.enums.YesOrNo;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.BeanHelper;
import com.yxt.common.util.Validate;
import com.yxt.leaf.service.SnowflakeKeyGenerator;
import com.yxt.uacd.facade.bean.RegistryConfigBean;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.yxt.aom.activity.common.AomActvApiError.MISSING_PARAMS;
import static com.yxt.aom.base.common.DefaultValueConstants.Numbers.LONG_0;
import static com.yxt.common.Constants.INT_1;

@Service
@Slf4j
public class ResultRollUpService extends AbstractActivityResult {
    private final AomRegistryManager aomRegistryManager ;

    public ResultRollUpService(BaseActivityResultMapper baseActivityResultMapper, AssessmentActivityResultMapper assessmentActivityResultMapper,
                               ActivityObjectiveResultMapper activityObjectiveResultMapper, AomMqComponent aomMqComponent,
                               RedissonClient redissonClient, AomRegistryManager aomRegistryManager,
                               SnowflakeKeyGenerator snowflakeKeyGenerator , MemberStatisticsManager memberStatisticsManager,
                               ContentActivityResultMapper contentActivityResultMapper, PracticeActivityResultMapper practiceActivityResultMapper,
                               OtherActivityResultMapper otherActivityResultMapper) {
        super(baseActivityResultMapper, assessmentActivityResultMapper, activityObjectiveResultMapper,aomMqComponent,
                redissonClient, snowflakeKeyGenerator,memberStatisticsManager, contentActivityResultMapper, otherActivityResultMapper,practiceActivityResultMapper);
        this.aomRegistryManager = aomRegistryManager;
    }

    public void rollUpAssessmentActivityResult(String orgId, ActionEnum action, Actor actor, TargetObject targetObject,
                                               AssessmentActivityResult generated) {
        ActivityResultEvent<AssessmentActivityResult> activityResultEvent = new ActivityResultEvent<>();
        String traceId = activityResultEvent.getEventId();
        log.info("traceId : {} rollUpAssessmentActivityResult action  : {}  , userId : {} ,targetObject : {} ,AssessmentActivityResult : {}  " , traceId,action, actor.getUserId() , JSON.toJSONString(targetObject),
                JSON.toJSONString(generated));

        if(!filterAction(action)){
            log.info("traceId : {} rollUpAssessmentActivityResult action return action : {} ", traceId,action);
            return;
        }
        generated.setOrgId(orgId);
        Validate.isTrue(Objects.nonNull(targetObject.getTargetType()), MISSING_PARAMS);

        activityResultEvent.setAction(action);
        activityResultEvent.setActor(actor);
        activityResultEvent.setTargetObject(targetObject);
        activityResultEvent.setGenerated(generated);
        activityResultEvent.setOrgId(orgId);

        assessmentActivityResultDb(orgId, activityResultEvent,null, targetObject.getTargetType(),INT_1,targetObject.getTargetType());
        RegistryConfigBean registryConfigBean = aomRegistryManager.getRegistryConfig(targetObject.getTargetType());
        String resultTopic = registryConfigBean.getResultMqTopic();

        aomMqComponent.sendRocketMessage(resultTopic,
                BeanHelper.bean2Json(activityResultEvent, JsonInclude.Include.ALWAYS));
        log.info("traceId : {}  send mq message" ,traceId);
    }

    public void rollUpContentActivityResult(String orgId, ActionEnum action, Actor actor, TargetObject targetObject,
                                               ContentActivityResult generated) {
        ActivityResultEvent<ContentActivityResult> activityResultEvent = new ActivityResultEvent<>();
        String traceId = activityResultEvent.getEventId();
        log.info("traceId : {} rollUpContentActivityResult action  : {}  , userId : {} ,targetObject : {} ,rollUpContentActivityResult : {}  " , traceId,action, actor.getUserId() , JSON.toJSONString(targetObject),
                JSON.toJSONString(generated));

        if(!filterAction(action)){
            log.info("traceId : {} rollUpContentActivityResult action return action : {} ", traceId,action);
            return;
        }
        generated.setOrgId(orgId);
        Validate.isTrue(Objects.nonNull(targetObject.getTargetType()), MISSING_PARAMS);

        activityResultEvent.setAction(action);
        activityResultEvent.setActor(actor);
        activityResultEvent.setTargetObject(targetObject);
        activityResultEvent.setGenerated(generated);
        activityResultEvent.setOrgId(orgId);
        contentActivityResultDb(orgId, activityResultEvent,null, targetObject.getTargetType(),INT_1,targetObject.getTargetType());

        RegistryConfigBean registryConfigBean = aomRegistryManager.getRegistryConfig(targetObject.getTargetType());
        String resultTopic = registryConfigBean.getResultMqTopic();

        aomMqComponent.sendRocketMessage(resultTopic,
                BeanHelper.bean2Json(activityResultEvent, JsonInclude.Include.ALWAYS));
        log.info("traceId : {}  send mq message" ,traceId);
    }

    public void rollUpPracticeActivityResult(String orgId, ActionEnum action, Actor actor, TargetObject targetObject,
                                             PracticeActivityResult generated) {
        ActivityResultEvent<PracticeActivityResult> activityResultEvent = new ActivityResultEvent<>();
        String traceId = activityResultEvent.getEventId();
        log.info("traceId : {} rollUpPracticeActivityResult action  : {}  , userId : {} ,targetObject : {} ,rollUpContentActivityResult : {}  " , traceId,action, actor.getUserId() , JSON.toJSONString(targetObject),
                JSON.toJSONString(generated));

        if(!filterAction(action)){
            log.info("traceId : {} rollUpPracticeActivityResult action return action : {} ", traceId,action);
            return;
        }
        generated.setOrgId(orgId);
        Validate.isTrue(Objects.nonNull(targetObject.getTargetType()), MISSING_PARAMS);

        activityResultEvent.setAction(action);
        activityResultEvent.setActor(actor);
        activityResultEvent.setTargetObject(targetObject);
        activityResultEvent.setGenerated(generated);
        activityResultEvent.setOrgId(orgId);
        practiceActivityResultDb(orgId, activityResultEvent,null, targetObject.getTargetType(),INT_1,targetObject.getTargetType());

        RegistryConfigBean registryConfigBean = aomRegistryManager.getRegistryConfig(targetObject.getTargetType());
        String resultTopic = registryConfigBean.getResultMqTopic();

        aomMqComponent.sendRocketMessage(resultTopic,
                BeanHelper.bean2Json(activityResultEvent, JsonInclude.Include.ALWAYS));
        log.info("traceId : {}  send mq message" ,traceId);
    }

    public void rollUpOtherActivityResult(String orgId, ActionEnum action, Actor actor, TargetObject targetObject,
                                          OtherActivityResult generated) {
        ActivityResultEvent<OtherActivityResult> activityResultEvent = new ActivityResultEvent<>();
        String traceId = activityResultEvent.getEventId();
        log.info("traceId : {} rollUpOtherActivityResult action  : {}  , userId : {} ,targetObject : {} ,rollUpOtherActivityResult : {}  " , traceId,action, actor.getUserId() , JSON.toJSONString(targetObject),
                JSON.toJSONString(generated));

        if(!filterAction(action)){
            log.info("traceId : {} rollUpOtherActivityResult action return action : {} ", traceId,action);
            return;
        }
        generated.setOrgId(orgId);
        Validate.isTrue(Objects.nonNull(targetObject.getTargetType()), MISSING_PARAMS);

        activityResultEvent.setAction(action);
        activityResultEvent.setActor(actor);
        activityResultEvent.setTargetObject(targetObject);
        activityResultEvent.setGenerated(generated);
        activityResultEvent.setOrgId(orgId);
        otherActivityResultDb(orgId, activityResultEvent,null, targetObject.getTargetType(),INT_1,targetObject.getTargetType());

        RegistryConfigBean registryConfigBean = aomRegistryManager.getRegistryConfig(targetObject.getTargetType());
        String resultTopic = registryConfigBean.getResultMqTopic();

        aomMqComponent.sendRocketMessage(resultTopic,
                BeanHelper.bean2Json(activityResultEvent, JsonInclude.Include.ALWAYS));
        log.info("traceId : {}  send mq message" ,traceId);
    }


    private boolean filterAction(ActionEnum action) {
        return Objects.equals(action,ActionEnum.SUBMITTED) || Objects.equals(action,ActionEnum.COMPLETED) || Objects.equals(action,ActionEnum.REPEAT)
                || Objects.equals(action,ActionEnum.RESET) || Objects.equals(action,ActionEnum.INIT);
    }

    @Override
    protected void initBaseActivityResult(BaseActivityResult dbBaseResult, Date now, TargetObject targetObject, String orgId, Actor actor, ActivityArrangeItem item,
                                          Integer actvType,Integer subType) {
        dbBaseResult.setId(snowflakeKeyGenerator.generateKey());
        dbBaseResult.setOrgId(orgId);
        dbBaseResult.setActvId(targetObject.getTargetId());
        dbBaseResult.setUserId(actor.getUserId());
        dbBaseResult.setActvType(actvType);
        dbBaseResult.setSubType(subType);
        //activity 本上没有 itemId item 类型  必选修

        dbBaseResult.setDeleted(YesOrNo.NO.getValue());
        dbBaseResult.setCreateTime(now);
        dbBaseResult.setUpdateTime(now);
        dbBaseResult.setCreateUserId(actor.getUserId());
        dbBaseResult.setUpdateUserId(actor.getUserId());
    }

    @Override
    protected <T extends com.yxt.aom.datamodel.activityresult.BaseActivityResult> void compo(Actor actor , TargetObject targetObject, ActivityArrangeItem item, String traceId,T dbBaseResult, String currentRegId) {
        SingleActivityCompo singleActivityCompo = AomBeanNameUtils.getCustomBean(SingleActivityCompo.class,
                currentRegId);
        if (singleActivityCompo != null) {
            singleActivityCompo.resultRollUpCallBack(actor,targetObject,item,traceId, dbBaseResult);
            log.info("activity result roll up call back success {}", JSON.toJSONString(dbBaseResult));
        }
    }


    /**
     * 复制活动结果
     *
     * @param bean ResultCopyReq
     */
    public void copyActivityResult(ResultCopyReq bean) {
        Map<String, String> userMap = bean.getUserMap();
        if (MapUtils.isEmpty(userMap)) {
            return;
        }

        // TBD: userMap超大时需要分批次复制
        ArrayList<String> srcUserIds = new ArrayList<>(userMap.keySet());
        List<BaseActivityResult> baseResults = baseActivityResultMapper.getByActvIdsAndUserIds(bean.getSrcOrgId(),
                Lists.newArrayList(bean.getSrcActvId()), srcUserIds);
        if (CollectionUtils.isEmpty(baseResults)) {
            return;
        }

        // todo: 复制其他类型的活动结果
        List<com.yxt.aom.base.entity.control.AssessmentActivityResult> assessmentResults = assessmentActivityResultMapper.getByActvIdsAndUserIds(
                bean.getSrcOrgId(), Collections.singleton(bean.getSrcActvId()), srcUserIds);
        Set<Long> baseIds = baseResults.stream().map(BaseActivityResult::getId).collect(Collectors.toSet());
        List<ActivityObjectiveResult> objectiveResults = CollectionUtils.isNotEmpty(baseIds) ?
                activityObjectiveResultMapper.getByBaseIds(bean.getSrcOrgId(), bean.getSrcActvId(), baseIds) :
                Collections.emptyList();

        updateIdFields4DemoCopy(bean, assessmentResults, objectiveResults);

        Map<Long, Long> baseIdMap = Maps.newHashMap();
        List<BaseActivityResult> newBaseResults = Lists.newArrayList();
        baseResults.forEach(baseResult -> {
            BaseActivityResult newBaseResult = clone(baseResult, bean, userMap);
            if (newBaseResult != null) {
                newBaseResults.add(newBaseResult);
                baseIdMap.put(baseResult.getId(), newBaseResult.getId());
            }
        });
        List<com.yxt.aom.base.entity.control.AssessmentActivityResult> newAssessmentResults = toNewAssessmentActivityResultList(
                bean, assessmentResults, userMap, baseIdMap);
        List<ActivityObjectiveResult> newObjectiveResults = toNewActivityObjectiveResultList(bean, objectiveResults,
                userMap, baseIdMap);

        boolean sendMq = saveActivityResult(newBaseResults, newAssessmentResults, newObjectiveResults);
        if (sendMq) {
            sendCopyResultMq(bean, newBaseResults, newAssessmentResults, newObjectiveResults);
        }
    }

    private boolean saveActivityResult(List<BaseActivityResult> newBaseResults,
            List<com.yxt.aom.base.entity.control.AssessmentActivityResult> newAssessmentResults,
            List<ActivityObjectiveResult> newObjectiveResults) {
        boolean sendMq = false;
        if (CollectionUtils.isNotEmpty(newBaseResults)) {
            baseActivityResultMapper.batchInsertBaseActivityResult(newBaseResults);
            sendMq = true;
        }
        if (CollectionUtils.isNotEmpty(newAssessmentResults)) {
            assessmentActivityResultMapper.batchInsertAssessmentActivityResult(newAssessmentResults);
            sendMq = true;
        }
        if (CollectionUtils.isNotEmpty(newObjectiveResults)) {
            activityObjectiveResultMapper.batchInsert(newObjectiveResults);
            sendMq = true;
        }
        return sendMq;
    }

    private void sendCopyResultMq(ResultCopyReq bean, List<BaseActivityResult> newBaseResults,
            List<com.yxt.aom.base.entity.control.AssessmentActivityResult> newAssessmentResults,
            List<ActivityObjectiveResult> newObjectiveResults) {
        ResultCopyMq mq = new ResultCopyMq();
        ResultCopyReq req = new ResultCopyReq();
        BeanCopierUtil.copy(bean, req);
        //userMap没必要传下去了
        req.setUserMap(Collections.emptyMap());
        mq.setReq(req);
        mq.setBaseResults(newBaseResults);
        mq.setAssessmentResults(newAssessmentResults);
        mq.setObjectiveResults(newObjectiveResults);
        aomMqComponent.sendRocketMessage(AomMqConstants.TOPIC_AOM_DEMOCOPY_ACTIVITY_RESULT,
                BeanHelper.bean2Json(mq, JsonInclude.Include.ALWAYS));
    }

    private List<ActivityObjectiveResult> toNewActivityObjectiveResultList(ResultCopyReq bean,
            List<ActivityObjectiveResult> objectiveResults, Map<String, String> userMap, Map<Long, Long> baseIdMap) {
        List<ActivityObjectiveResult> newObjectiveResults = Lists.newArrayList();
        objectiveResults.forEach(objectiveResult -> {
            ActivityObjectiveResult newObjectiveResult = clone(objectiveResult, bean, userMap, baseIdMap);
            if (newObjectiveResult != null) {
                newObjectiveResults.add(newObjectiveResult);
            }
        });
        return newObjectiveResults;
    }

    private List<com.yxt.aom.base.entity.control.AssessmentActivityResult> toNewAssessmentActivityResultList(
            ResultCopyReq bean, List<com.yxt.aom.base.entity.control.AssessmentActivityResult> assessmentResults,
            Map<String, String> userMap, Map<Long, Long> baseIdMap) {
        List<com.yxt.aom.base.entity.control.AssessmentActivityResult> newAssessmentResults = Lists.newArrayList();
        assessmentResults.forEach(assessmentResult -> {
            com.yxt.aom.base.entity.control.AssessmentActivityResult newAssessmentResult = clone(assessmentResult, bean,
                    userMap, baseIdMap);
            if (newAssessmentResult != null) {
                newAssessmentResults.add(newAssessmentResult);
            }
        });
        return newAssessmentResults;
    }

    private void updateIdFields4DemoCopy(ResultCopyReq bean,
            List<com.yxt.aom.base.entity.control.AssessmentActivityResult> assessmentResults,
            List<ActivityObjectiveResult> objectiveResults) {
        SingleActivityCompo singleActivityCompo = AomBeanNameUtils.getCustomBean(SingleActivityCompo.class,
                bean.getActvRegId());
        if (singleActivityCompo != null) {
            singleActivityCompo.updateIdFields4DemoCopy(bean, assessmentResults, objectiveResults);
        }
    }

    private ActivityObjectiveResult clone(ActivityObjectiveResult oldOne, ResultCopyReq bean,
            Map<String, String> userMap, Map<Long, Long> baseIdMap) {
        String newUserId = userMap.get(oldOne.getUserId());
        if (StringUtils.isBlank(newUserId)) {
            return null;
        }
        ActivityObjectiveResult newOne = new ActivityObjectiveResult();
        BeanCopierUtil.copy(oldOne, newOne, false);
        newOne.setId(snowflakeKeyGenerator.generateKey());
        newOne.setOrgId(bean.getTgtOrgId());
        newOne.setActvId(bean.getTgtActvId());
        newOne.setUserId(newUserId);
        newOne.setBaseActvResultId(baseIdMap.getOrDefault(oldOne.getBaseActvResultId(), oldOne.getBaseActvResultId()));
        newOne.setCreateUserId(userMap.getOrDefault(oldOne.getCreateUserId(), oldOne.getCreateUserId()));
        newOne.setUpdateUserId(userMap.getOrDefault(oldOne.getUpdateUserId(), oldOne.getUpdateUserId()));
        return newOne;
    }

    private com.yxt.aom.base.entity.control.AssessmentActivityResult clone(
            com.yxt.aom.base.entity.control.AssessmentActivityResult oldOne, ResultCopyReq bean,
            Map<String, String> userMap, Map<Long, Long> baseIdMap) {
        String newUserId = userMap.get(oldOne.getUserId());
        if (StringUtils.isBlank(newUserId)) {
            return null;
        }
        com.yxt.aom.base.entity.control.AssessmentActivityResult newOne = new com.yxt.aom.base.entity.control.AssessmentActivityResult();
        BeanCopierUtil.copy(oldOne, newOne, false);
        newOne.setId(snowflakeKeyGenerator.generateKey());
        newOne.setOrgId(bean.getTgtOrgId());
        newOne.setActvId(bean.getTgtActvId());
        newOne.setUserId(newUserId);
        newOne.setItemId(LONG_0);
        newOne.setBaseActvResultId(baseIdMap.getOrDefault(oldOne.getBaseActvResultId(), oldOne.getBaseActvResultId()));
        newOne.setCreateUserId(userMap.getOrDefault(oldOne.getCreateUserId(), oldOne.getCreateUserId()));
        newOne.setUpdateUserId(userMap.getOrDefault(oldOne.getUpdateUserId(), oldOne.getUpdateUserId()));
        return newOne;
    }

    private BaseActivityResult clone(BaseActivityResult oldOne, ResultCopyReq bean, Map<String, String> userMap) {
        String newUserId = userMap.get(oldOne.getUserId());
        if (StringUtils.isBlank(newUserId)) {
            return null;
        }
        BaseActivityResult newOne = new BaseActivityResult();
        BeanCopierUtil.copy(oldOne, newOne, false);
        newOne.setId(snowflakeKeyGenerator.generateKey());
        newOne.setOrgId(bean.getTgtOrgId());
        newOne.setActvId(bean.getTgtActvId());
        newOne.setUserId(newUserId);
        newOne.setItemId(LONG_0);
        newOne.setCreateUserId(userMap.getOrDefault(oldOne.getCreateUserId(), oldOne.getCreateUserId()));
        newOne.setUpdateUserId(userMap.getOrDefault(oldOne.getUpdateUserId(), oldOne.getUpdateUserId()));
        return newOne;
    }

}
