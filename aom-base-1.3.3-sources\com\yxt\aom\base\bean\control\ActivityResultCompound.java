package com.yxt.aom.base.bean.control;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024/11/7  11:10
 * @description 描述
 */
@Getter
@Setter
@EqualsAndHashCode
public class ActivityResultCompound {

    /**
     * 活动id
     */
    private String actvId;

    /**
     * 学员id
     */
    private String userId;

    /**
     * 叶节点id
     */
    private Long itemId;

    /**
     * 叶节点引用对象的具体类型(UACD注册表中定义)
     */
    private String refRegId;

    /**
     * 是否必修 0否 1是
     */
    private Integer required;

    /**
     * 0未开始，1进行中，2已完成
     */
    private Integer resultStatus;


}
