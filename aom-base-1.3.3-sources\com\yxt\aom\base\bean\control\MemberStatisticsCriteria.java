package com.yxt.aom.base.bean.control;

import com.yxt.aom.base.entity.control.ActivityMemberStatistics;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @date 2024/11/22  17:02
 * @description 操作数据库实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MemberStatisticsCriteria extends ActivityMemberStatistics {
    @Serial
    private static final long serialVersionUID = 605350934655276305L;

    /**
     * 是否更新必修
     */
    private Boolean refreshRequired;

    /**
     * 是否更新任务完成数
     */
    private Boolean refreshCompleteCount;

    /**
     * 是否更新全部任务
     */
    private Boolean refreshAllCount;

}
