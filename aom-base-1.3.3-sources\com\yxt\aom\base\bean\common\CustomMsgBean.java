package com.yxt.aom.base.bean.common;

import lombok.Data;

/**
 * CustomMsgBean
 */
@Data
public class CustomMsgBean {
    /**
     * AOM原始消息模板code
     */
    private String defaultTemplateCode;

    /**
     * 活动id
     */
    private String actvId;

    /**
     * 替换后的消息模板
     */
    private String templateCode;

    /**
     * 跳转短地址
     */
    private String shortUrl;

    /**
     * 开启按钮：默认都关闭，则表示此项目设计器不发送这条消息，开启则表示发送
     * (0 关闭,1 启用)
     */
    private Integer enabled;

    /**
     * AOM设计器id
     */
    private Long designerId;
}
