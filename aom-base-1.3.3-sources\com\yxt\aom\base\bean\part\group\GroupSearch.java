package com.yxt.aom.base.bean.part.group;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 小组查询入参bean
 *
 * <AUTHOR>
 * @since 2024/12/12
 */
@Getter
@Setter
@NoArgsConstructor
public class GroupSearch {

    @Schema(description = "活动id")
    @NotBlank
    private String actvId;

    @Schema(description = "UACD注册表中定义Id")
    @NotBlank
    private String regId;

    @Schema(description = "参与id")
    @NotBlank
    private String participationId;

    @Schema(description = "查询关键字")
    private String keyword;
}
