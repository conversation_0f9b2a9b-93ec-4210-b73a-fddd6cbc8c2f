package com.yxt.aom.base.bean.part.title;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * @description:
 * @author: dingjh
 * @date: 2024/12/13 16:16
 */
@Schema(name = "称号编辑")
@Getter
@Setter
public class ActivityTitleUpdateRequest {

    @Schema(description = "称号id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long titleId;

    @Schema(description = "称号名称")
    private String titleName;

    @Schema(description = "是否优秀")
    private Integer outstanding;
}
