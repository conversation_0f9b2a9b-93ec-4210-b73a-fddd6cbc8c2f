package com.yxt.aom.base.common;
public final class DefaultValueConstants {

    /**
     * 0
     */
    public static final Long ZERO = 0L;
    /**
     * 无id
     */
    public static final Long IDNONE = 0L;

    public static final String DOUBLE_HYPHEN = "--";

    /**
     * 1
     */
    public static final Integer ONE = 1;

    private DefaultValueConstants() {
        throw new UnsupportedOperationException();
    }

    public static class Numbers {

        public static final int INT_0 = 0;
        public static final int INT_1 = 1;
        public static final int INT_2 = 2;
        public static final int INT_3 = 3;
        public static final int INT_4 = 4;
        public static final int INT_5 = 5;
        public static final int INT_6 = 6;
        public static final int INT_7 = 7;
        public static final int INT_8 = 8;
        public static final int INT_9 = 9;
        public static final int INT_10 = 10;
        public static final int INT_11 = 11;
        public static final int INT_12 = 12;
        public static final int INT_13 = 13;
        public static final int INT_14 = 14;
        public static final int INT_15 = 15;
        public static final int INT_16 = 16;
        public static final int INT_18 = 18;
        public static final int INT_20 = 20;
        public static final int INT_21 = 21;
        public static final int INT_23 = 23;
        public static final int INT_24 = 24;
        public static final int INT_30 = 30;
        public static final int INT_46 = 46;
        public static final int INT_50 = 50;
        public static final int INT_59 = 59;
        public static final int INT_60 = 60;
        public static final int INT_99 = 99;
        public static final int INT_100 = 100;
        public static final int INT_120 = 120;
        public static final int INT_200 = 200;
        public static final int INT_256 = 256;
        public static final int INT_400 = 400;
        public static final int INT_500 = 500;
        public static final int INT_2500 = 2500;
        public static final int INT_10000 = 10000;
        public static final int INT_10000000 = 10_000_000;
        public static final int INT_3600000 = 3600000;

        public static final String STR_0 = "0";

        public static final byte BYTE_1 = (byte) 1;
        public static final byte BYTE_2 = (byte) 2;
        public static final byte BYTE_5 = (byte) 5;
        public static final byte BYTE_6 = (byte) 6;

        public static final byte BYTE_50 = (byte) 50;

        public static final byte BYTE_51 = (byte) 51;

        public static final long LONG_0 = 0L;
        public static final long LONG_1 = 1L;
        public static final long LONG_2 = 2L;
        public static final long LONG_3 = 3L;
        public static final long LONG_7 = 7L;
        public static final long LONG_60 = 60L;

        public static final long LONG_100 = 100L;
        public static final long LONG_180 = 180L;
        public static final long LONG_1000 = 1_000L;
        public static final long LONG_NEGATIVE_1 = -1;
        public static final double DOUBLE_ZERO = 0D;
        public static final int INT_NEGATIVE_1 = -1;
        public static final Integer INTEGER_NEGATIVE_1 = -1;

        public static final int INT_NEGATIVE_2 = -2;
        public static final int INT_NEGATIVE_5 = -5;
        public static final int INT_1000 = 1000;
        public static final int INT_1999 = 1999;
        public static final int INT_2000 = 2000;
        public static final int INT_3000 = 3000;
        public static final int INT_3600 = 3600;
        public static final int INT_4000 = 4000;
        public static final int INT_5000 = 5000;
        public static final double DOUBLE_0 = 0.0D;

        private Numbers() {
        }
    }

}
