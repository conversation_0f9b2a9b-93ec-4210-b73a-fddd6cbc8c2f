package com.yxt.aom.base.bean.control;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/31  13:51
 * @description 描述
 */
@Getter
@Setter
@Schema(name = "获取项目统计数据响应")
public class ProjectStatisticsResp {

    @Schema(description = "活动/项目id")
    private String actvId;

    @Schema(description = "名称（项目/阶段）")
    private String actvName;

    @Schema(description = "学员数量")
    private Integer memberNum = 0;

    @Schema(description = "任务总数")
    private Integer taskNum = 0;

    @Schema(description = "学员需要完成的任务总数")
    private Integer taskStuTotalNum = 0;

    @Schema(description = "学员完成任务总数")
    private Integer taskStuFinishNum = 0;

    @Schema(description = "总任务完成率")
    private BigDecimal allRate = BigDecimal.ZERO;

    @Schema(description = "项目完成率")
    private BigDecimal projectCompleteRate = BigDecimal.ZERO;

    @Schema(description = "必修任务数量")
    private Integer requiredTaskNum = 0;

    @Schema(description = "学员需要完成的必修任务数量")
    private Integer requiredTaskStuNum = 0;

    @Schema(description = "学员完成必修任务的数量")
    private Integer requiredTaskStuFinishNum = 0;

    @Schema(description = "必修完成率")
    private BigDecimal requiredRate = BigDecimal.ZERO;

    @Schema(description = "选修任务数")
    private Integer unRequiredTaskNum = 0;

    @Schema(description = "学员需要完成选修任务的数量")
    private Integer unRequiredTaskStuNum = 0;

    @Schema(description = "选修任务学员完成数量")
    private Integer unRequiredTaskStuFinishNum = 0;

    @Schema(description = "选修完成率")
    private BigDecimal unRequiredRate = BigDecimal.ZERO;

    @Schema(description = "课程完成率")
    private BigDecimal kngFinishRate = BigDecimal.ZERO;

    @Schema(description = "必修课程任务是否存在 0不存在  1存在")
    private Integer kngRequiredExist = 0;

    @Schema(description = "考试通过率")
    private BigDecimal examPassedRate = BigDecimal.ZERO;

    @Schema(description = "必修考试任务是否存在 0不存在  1存在")
    private Integer examRequiredExist = 0;

    @Schema(description = "作业完成率")
    private BigDecimal hwFinishRate = BigDecimal.ZERO;

    @Schema(description = "必修作业任务是否存在 0不存在  1存在")
    private Integer hwRequiredExist = 0;

    @Schema(description = "阶段数据")
    private List<ProjectStatisticsResp> periodStatisticsList;

}
