package com.yxt.aom.base.component.common;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yxt.aom.base.bean.common.Activity4Create;
import com.yxt.aom.base.bean.common.Activity4List;
import com.yxt.aom.base.bean.common.Activity4Saveas;
import com.yxt.aom.base.bean.common.Activity4Update;
import com.yxt.aom.base.bean.common.ActivityCopyOption;
import com.yxt.aom.base.bean.common.ActivityExt;
import com.yxt.aom.base.bean.common.ActivitySearchReq;
import com.yxt.aom.base.bean.config.AllConfig;
import com.yxt.aom.base.bean.config.AuditConfig;
import com.yxt.aom.base.bean.config.OrganizerConfig;
import com.yxt.aom.base.bean.msg.AomMsgCustomParam;
import com.yxt.aom.base.common.AomConstants;
import com.yxt.aom.base.common.BaseErrorConsts;
import com.yxt.aom.base.custom.CustomActivitySendMsgCheckCompo;
import com.yxt.aom.base.custom.CustomArrangeCompo;
import com.yxt.aom.base.entity.common.Activity;
import com.yxt.aom.base.entity.common.ActivityContract;
import com.yxt.aom.base.entity.part.ActivityAct;
import com.yxt.aom.base.entity.part.ActivityDynamicGroup;
import com.yxt.aom.base.entity.part.ActivityParticipation;
import com.yxt.aom.base.enums.ActivitySaveasTypeEnum;
import com.yxt.aom.base.enums.ActivityUsageTypeEnum;
import com.yxt.aom.base.enums.ActvRelationTypeEnum;
import com.yxt.aom.base.enums.AomActivityStatusEnum;
import com.yxt.aom.base.enums.AomTimeModelEnum;
import com.yxt.aom.base.enums.DesignerAuthorityPointEnum;
import com.yxt.aom.base.enums.PrincipalJoinMethodEnum;
import com.yxt.aom.base.manager.common.AomDesignerManager;
import com.yxt.aom.base.manager.common.AomRegistryManager;
import com.yxt.aom.base.mapper.part.ActivityActMapper;
import com.yxt.aom.base.service.arrange.ArrangeService;
import com.yxt.aom.base.service.common.ActivityService;
import com.yxt.aom.base.service.part.impl.ActivityDynamicGroupService;
import com.yxt.aom.base.service.part.impl.ActivityGroupScoreService;
import com.yxt.aom.base.service.part.impl.ActivityGroupService;
import com.yxt.aom.base.service.part.impl.ActivityParticipationMemberService;
import com.yxt.aom.base.service.part.impl.ActivityParticipationRelationService;
import com.yxt.aom.base.service.part.impl.ActivityParticipationService;
import com.yxt.aom.base.service.part.impl.AomHonorService;
import com.yxt.aom.base.wrapper.EnrollWrapper;
import com.yxt.aom.common.config.AomDataSourceTypeHolder;
import com.yxt.aom.common.util.AomBeanNameUtils;
import com.yxt.common.Constants;
import com.yxt.common.component.SpringContextHolder;
import com.yxt.common.enums.YesOrNo;
import com.yxt.common.exception.ApiException;
import com.yxt.common.pojo.IdName;
import com.yxt.common.pojo.api.PageRequest;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.BeanHelper;
import com.yxt.common.util.DateUtil;
import com.yxt.common.util.Validate;
import com.yxt.dbrouter.core.toolkit.DBRouteHolder;
import com.yxt.leaf.service.SnowflakeKeyGenerator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import static com.yxt.aom.base.common.DefaultValueConstants.Numbers.INT_0;
import static com.yxt.aom.base.common.DefaultValueConstants.Numbers.INT_1;
import static com.yxt.aom.base.common.DefaultValueConstants.Numbers.INT_2;

/**
 * AomActivityComponent
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AomActivityComponent {
    private final AomRegistryManager registryManager;
    private final AomDesignerManager designerManager;
    private final ActivityService activityService;
    private final EnrollWrapper enrollWrapper;
    private final AomFactorComponent factorComponent;
    private final ActivityActMapper activityActMapper;
    private final SnowflakeKeyGenerator snowflakeKeyGenerator;
    private final ActivityParticipationService partService;
    private final ActivityParticipationMemberService participationMemberService;
    private final AomHonorService aomHonorService;
    private final ActivityParticipationRelationService relationService;
    private final ActivityDynamicGroupService activityDynamicGroupService;
    private final ActivityGroupScoreService activityGroupScoreService;
    private final ArrangeService arrangeService;

    /**
     * Gets all config.
     *
     * @param orgId  the org id
     * @param actvId the actv id
     * @param regId  the reg id
     * @return the all config
     */
    public AllConfig getAllConfig(String orgId, String actvId, String regId) {
        return activityService.getAllConfig(orgId, actvId, regId);
    }

    /**
     * 获取活动/项目基本信息
     *
     * @param orgId     机构id
     * @param actvId    活动/项目id
     * @param actvRegId 活动/项目注册id
     * @return the activity
     */
    public Activity getActivity(String orgId, String actvId, String actvRegId) {
        AomDataSourceTypeHolder.set(actvRegId);
        return activityService.findById(orgId, actvId);
    }


    public ActivityAct getActivityActByActvId(String orgId, String actvId) {
        return activityActMapper.getByActvId(orgId, actvId);
    }

    /**
     * 管理端分页搜索活动/项目
     *
     * @param orgId       机构id
     * @param operatorId  操作者id（最好是admin，否则创建出来后管理界面查看不了）
     * @param bean        the bean
     * @param pageRequest the page request
     * @return the paging list
     */
    public PagingList<Activity4List> searchActivity(String orgId, String operatorId, ActivitySearchReq bean,
            PageRequest pageRequest) {
        return searchActivity(toUserCacheDetail(orgId, operatorId), bean, pageRequest);
    }

    /**
     * 管理端分页搜索活动/项目
     *
     * @param user        the user
     * @param bean        the bean
     * @param pageRequest the page request
     * @return the paging list
     */
    public PagingList<Activity4List> searchActivity(UserCacheDetail user, ActivitySearchReq bean,
            PageRequest pageRequest) {
        String actvRegId = bean.getActvRegId();
        verifyRegId(actvRegId);
        designerManager.checkDesignerAuthCodes(user, actvRegId, DesignerAuthorityPointEnum.ACT_MANANG);

        PagingList<Activity4List> pagingList = activityService.searchActivity(user, bean, pageRequest);

        CustomArrangeCompo arrangeCompo = AomBeanNameUtils.getCustomBean(CustomArrangeCompo.class, actvRegId);
        if (arrangeCompo != null && CollectionUtils.isNotEmpty(pagingList.getDatas())) {
            pagingList.setDatas(arrangeCompo.searchActivity(pagingList.getDatas()));
        }
        return pagingList;
    }

    public List<Activity4List> searchActivityList(UserCacheDetail user, ActivitySearchReq bean) {
        String actvRegId = bean.getActvRegId();
        verifyRegId(actvRegId);
        designerManager.checkDesignerAuthCodes(user, actvRegId, DesignerAuthorityPointEnum.ACT_MANANG);

        List<Activity4List> list = activityService.searchActivityList(user, bean);

        CustomArrangeCompo arrangeCompo = AomBeanNameUtils.getCustomBean(CustomArrangeCompo.class, actvRegId);
        if (arrangeCompo != null && CollectionUtils.isNotEmpty(list)) {
            arrangeCompo.searchActivity(list);
        }
        return list;
    }


    /**
     * 学员端分页搜索活动/项目
     *
     * @param orgId       机构id
     * @param userId      学员id
     * @param bean        the bean
     * @param pageRequest the page request
     * @return the paging list
     */
    public PagingList<Activity4List> searchActivity4Student(String orgId, String userId, ActivitySearchReq bean,
            PageRequest pageRequest) {
        bean.setOrgId(orgId);
        bean.setUserId(userId);
        return activityService.searchActivity4Student(bean, pageRequest);
    }

    /**
     * 创建活动/项目
     *
     * @param orgId      机构id
     * @param operatorId 操作者id（最好是admin，否则创建出来后管理界面查看不了）
     * @param bean       the bean
     * @return the string
     */
    public String createActivity(String orgId, String operatorId, Activity4Create bean) {
        return createActivity(toUserCacheDetail(orgId, operatorId), bean);
    }

    /**
     * 创建活动/项目
     *
     * @param user the user
     * @param bean the bean
     * @return the string
     */
    public String createActivity(UserCacheDetail user, Activity4Create bean) {
        String actvRegId = bean.getActvRegId();
        verifyRegId(actvRegId);
        designerManager.checkDesignerAuthCodes(user, actvRegId, DesignerAuthorityPointEnum.ACT_MANANG);

        CustomArrangeCompo arrangeCompo = AomBeanNameUtils.getCustomBean(CustomArrangeCompo.class, bean.getActvRegId());
        if (arrangeCompo != null) {
            arrangeCompo.checkBeforeCreateActivity(user.getOrgId(), user.getUserId(), bean);
        }
        String id = activityService.create(user, bean);
        if (arrangeCompo != null) {
            bean.setId(id);
            arrangeCompo.invokeAfterCreateActivity(user.getOrgId(), user.getUserId(), bean);
        }
        return id;
    }

    /**
     * 更新活动/项目
     *
     * @param orgId      机构id
     * @param operatorId 操作者id（最好是admin，否则创建出来后管理界面查看不了）
     * @param bean       the bean
     * @param id         the id
     * @param copyFields the copy fields
     */
    public void updateActivity(String orgId, String operatorId, Activity4Update bean, String id, String[] copyFields) {
        updateActivity(toUserCacheDetail(orgId, operatorId), bean, id, copyFields);
    }

    /**
     * 更新活动/项目
     *
     * @param user       the user
     * @param bean       the bean
     * @param id         the id
     * @param copyFields the copy fields
     */
    public void updateActivity(UserCacheDetail user, Activity4Update bean, String id, String[] copyFields) {
        String actvRegId = bean.getActvRegId();
        verifyRegId(actvRegId);
        designerManager.checkDesignerAuthCodes(user, actvRegId, DesignerAuthorityPointEnum.ACT_MANANG);

        AomDataSourceTypeHolder.set(actvRegId);
        Activity activity = activityService.findById(user.getOrgId(), id);
        if (activity == null) {
            throw new ApiException(BaseErrorConsts.ID_INVALID);
        }

        bean.setId(id);
        CustomArrangeCompo arrangeCompo = AomBeanNameUtils.getCustomBean(CustomArrangeCompo.class, actvRegId);
        if (arrangeCompo != null) {
            arrangeCompo.checkBeforeUpdateActivity(user.getOrgId(), user.getUserId(), bean);
        }

        boolean timeChange = activityTimeChange(activity, bean);
        String oldName = activity.getActvName();
        BeanHelper.copyProperties(bean, activity, copyFields);
        activity.setCategoryId(ObjectUtils.defaultIfNull(activity.getCategoryId(), StringUtils.EMPTY));
        activity.setAutoEnd(ObjectUtils.defaultIfNull(activity.getAutoEnd(), INT_0));
        activity.setAutoArchive(ObjectUtils.defaultIfNull(activity.getAutoArchive(), INT_0));
        activity.setAuditEnabled(ObjectUtils.defaultIfNull(activity.getAuditEnabled(), INT_0));
        activity.setAuditStatus(ObjectUtils.defaultIfNull(activity.getAuditStatus(),
                activity.getAuditEnabled() == INT_1 ? INT_0 : INT_2));
        activity.setUpdateUserId(user.getUserId());
        activity.setUpdateTime(DateUtil.currentTime());
        if (bean.getEnableMultiShiftTasks() != null || StringUtils.isNotBlank(bean.getSkinId())
                || bean.getCustom() != null) {
            ActivityExt ext = activity.getExtValue();
            ext.setEnableMultiShiftTasks(ObjectUtils.defaultIfNull(bean.getEnableMultiShiftTasks(), INT_0));
            ext.setSkinId(ObjectUtils.defaultIfNull(bean.getSkinId(), StringUtils.EMPTY));
            ext.setCustom(bean.getCustom());
            activity.putExtValue(ext);
        }
        activityService.update(user, activity, !StringUtils.equals(oldName, bean.getActvName()), timeChange,
                bean.getMgrUserIds(), bean.getOwnerDynamicUserGroupId(), toAllConfig(bean));
        //更新活动时更新报名
        updateEnroll(activity, user.getOrgId(), user.getUserId());
        if (arrangeCompo != null) {
            arrangeCompo.invokeAfterUpdateActivity(user.getOrgId(), user.getUserId(), bean);
        }
    }

    /**
     * 活动/活动模板另存为新的活动/活动模板
     *
     * @param actvId the actv id
     * @param user   the user
     * @param bean   the bean
     * @return the string
     */
    public String saveActivityAs(String actvId, UserCacheDetail user, Activity4Saveas bean, String newId) {
        String actvRegId = bean.getActvRegId();
        String orgId = user.getOrgId();

        verifyRegId(actvRegId);
        designerManager.checkDesignerAuthCodes(user, actvRegId, DesignerAuthorityPointEnum.ACT_MANANG);
        if (bean.getOpeateType() == ActivitySaveasTypeEnum.PRO2TEM.getValue()) {
            factorComponent.checkFactor(orgId, AomConstants.CODE_TEMPLATELIST);
        }

        String userId = user.getUserId();

        AomDataSourceTypeHolder.set(bean.getActvRegId());
        Activity activity = activityService.requireAvailableActivity(orgId, actvId);
        int opeateType = bean.getOpeateType();
        checkOpeateType(opeateType, activity.getUsageType());

        Activity newActivity = getNewActivity(bean, userId, activity, opeateType, newId);
        AllConfig allConfig = getAllConfig(orgId, actvId, bean.getActvRegId());

        Long partId = partService.getParticipationId(orgId, actvId);
        Set<String> mgrUserIds = getMgrUserIds(orgId, userId, actvId, partId);
        String ownerDynamicUserGroupId = getOwnerDynamicUserGroupId(orgId, actvId, partId);

        activityService.create(user, mgrUserIds, newActivity, allConfig, ownerDynamicUserGroupId);
        activityService.savaActivityArrange(orgId, userId, newActivity);
        arrangeService.draftCopy(orgId, actvId, orgId, newActivity.getId(), userId, bean, YesOrNo.NO.getValue());

        // 新参与ID
        Long newPartId = partService.getParticipationId(orgId, newActivity.getId());
        Map<Long, Long> partIdMap = Map.of(partId, newPartId);
        // 新旧小组ID map
        Map<Long, Long> groupsCopyMaps = Maps.newHashMap();
        if (bean.needCopyMember()) {
            // 复制小组
            groupsCopyMaps = SpringContextHolder.getBean(ActivityGroupService.class)
                    .copyActivityGroup(activity, newActivity, partIdMap);
            // 复制小组荣誉
            aomHonorService.copyGroupHonor(orgId, newActivity.getCreateUserId(), groupsCopyMaps);
            // 复制学员
            participationMemberService.copyParticipationMember(activity, newActivity, groupsCopyMaps, partIdMap, null , Maps.newHashMap());
        }

        if (bean.needCopyScore()) {
            // todo: 复制奖励规则
            // 小组积分
            activityGroupScoreService.copyActivityGroupScore(activity, newActivity, groupsCopyMaps);
        }

        if (bean.needCopyMsg()) {
            // todo: 复制消息模板
        }

        if (bean.needCopyOjt()) {
            // todo: 复制带教
        }

        return newActivity.getId();
    }

    /**
     * Copy activity.
     *
     * @param contractId    the contract id
     * @param srcOrgId      the src org id
     * @param srcActvId     the src actv id
     * @param regId         the reg id
     * @param tgtOrgId      the tgt org id
     * @param tgtOperatorId the tgt operator id
     * @param demoCopy      the demo copy
     */
    public void copyActivity(String contractId, String srcOrgId, String srcActvId, String regId, String tgtOrgId,
            String tgtOperatorId, Integer demoCopy) {
        AomDataSourceTypeHolder.set(regId);
        DBRouteHolder.push(srcOrgId);
        Activity srcActivity = activityService.findById(srcOrgId, srcActvId);
        if (srcActivity == null) {
            log.warn("copyActivity srcActivity is null, contractId: {} srcOrgId: {} srcActvId: {}", contractId,
                    srcOrgId, srcActvId);
            DBRouteHolder.poll();
            return;
        }
        List<ActivityContract> existedActivityContracts = activityService.findActivityContract(srcOrgId, srcActvId,
                contractId, tgtOrgId);
        if (CollectionUtils.isNotEmpty(existedActivityContracts)) {
            log.warn("copyActivity existedActivityContracts is not empty, contractId: {} srcOrgId: {} srcActvId: {}",
                    contractId, srcOrgId, srcActvId);
            DBRouteHolder.poll();
            return;
        }
        AllConfig allConfig = getAllConfig(srcActivity.getOrgId(), srcActivity.getId(), regId);
        DBRouteHolder.poll();

        Activity tgtActivity = toTgtActivity(srcActivity, tgtOrgId, tgtOperatorId);

        DBRouteHolder.push(tgtOrgId);
        activityService.create(toUserCacheDetail(tgtOrgId, tgtOperatorId), Sets.newHashSet(tgtOperatorId), tgtActivity,
                allConfig, null);
        activityService.createActivityContract(
                toActivityContract(contractId, srcOrgId, srcActvId, tgtOrgId, tgtActivity.getId(), tgtOperatorId));
        activityService.savaActivityArrange(tgtOrgId, tgtOperatorId, tgtActivity);
        DBRouteHolder.poll();

        ActivityCopyOption option = new ActivityCopyOption();
        option.setTaskFlag(true);
        arrangeService.draftCopy(srcOrgId, srcActvId, tgtOrgId, tgtActivity.getId(), tgtOperatorId, option, demoCopy);
    }

    /**
     * List activity mgr list.
     *
     * @param orgId  the org id
     * @param actvId the actv id
     * @return the list
     */
    public List<IdName> listActivityMgr(String orgId, String actvId) {
        return activityService.listActivityMgr(orgId, actvId);
    }

    private ActivityContract toActivityContract(String contractId, String srcOrgId, String sourceActvId,
            String tgtorgId, String tgtActvId, String operatorId) {
        ActivityContract contract = new ActivityContract();
        contract.setId(snowflakeKeyGenerator.generateKey());
        contract.setOrgId(srcOrgId);
        contract.setContractId(contractId);
        contract.setSourceActvId(sourceActvId);
        contract.setTgtOrgId(tgtorgId);
        contract.setTgtActvId(tgtActvId);
        contract.setCreateUserId(operatorId);
        contract.setUpdateUserId(operatorId);
        contract.setCreateTime(DateUtil.currentTime());
        contract.setUpdateTime(contract.getCreateTime());
        return contract;
    }

    private Activity toTgtActivity(Activity srcActivity, String tgtOrgId, String tgtOperatorId) {
        Activity tgtActivity = new Activity();
        BeanCopierUtil.copy(srcActivity, tgtActivity, false);
        tgtActivity.setId(StringUtils.isNumeric(srcActivity.getId()) ?
                String.valueOf(snowflakeKeyGenerator.generateKey()) :
                ApiUtil.getUuid());
        tgtActivity.setActvStatus(AomActivityStatusEnum.DRAFT.getType());
        tgtActivity.setOrgId(tgtOrgId);
        tgtActivity.setCreateUserId(tgtOperatorId);
        tgtActivity.setUpdateUserId(tgtOperatorId);
        tgtActivity.setCreateTime(DateUtil.currentTime());
        tgtActivity.setUpdateTime(tgtActivity.getCreateTime());
        tgtActivity.setSourceId(StringUtils.EMPTY);
        tgtActivity.setSourceRegId(StringUtils.EMPTY);
        tgtActivity.setSourceName(StringUtils.EMPTY);
        return tgtActivity;
    }

    private void checkOpeateType(int opeateType, Integer usageType) {
        if (Objects.equals(usageType, ActivityUsageTypeEnum.NORMAL.getType())) {
            Validate.isTrue(opeateType == ActivitySaveasTypeEnum.PRO2TEM.getValue()
                    || opeateType == ActivitySaveasTypeEnum.PRO2PRO.getValue(), BaseErrorConsts.ID_INVALID);
        } else if (Objects.equals(usageType, ActivityUsageTypeEnum.TEMPLATE.getType())) {
            Validate.isTrue(opeateType == ActivitySaveasTypeEnum.TEM2PRO.getValue()
                    || opeateType == ActivitySaveasTypeEnum.TEM2TEM.getValue(), BaseErrorConsts.ID_INVALID);
        }
    }

    private Activity getNewActivity(Activity4Saveas bean, String userId, Activity activity, int opeateType,
            String newId) {
        Activity newActivity = new Activity();
        BeanCopierUtil.copy(activity, newActivity, false);
        String newActivityId = StringUtils.isNotBlank(newId) ? newId : generateAcivityId(activity);
        newActivity.setId(newActivityId);
        newActivity.setActvName(bean.getName());
        newActivity.setActvStatus(AomActivityStatusEnum.DRAFT.getType());
        newActivity.setCreateUserId(userId);
        newActivity.setUpdateUserId(userId);
        newActivity.setCreateTime(DateUtil.currentTime());
        newActivity.setUpdateTime(newActivity.getCreateTime());
        if (opeateType == ActivitySaveasTypeEnum.PRO2TEM.getValue()) {
            newActivity.setUsageType(ActivityUsageTypeEnum.TEMPLATE.getType());
        } else if (opeateType == ActivitySaveasTypeEnum.TEM2PRO.getValue()) {
            newActivity.setUsageType(ActivityUsageTypeEnum.NORMAL.getType());
        }
        if (StringUtils.isNotBlank(bean.getCategoryId())) {
            newActivity.setCategoryId(bean.getCategoryId());
        }
        newActivity.setTemplateId(activity.getId());
        return newActivity;
    }

    private String generateAcivityId(Activity activity) {
        return StringUtils.isNumeric(activity.getId()) ?
                String.valueOf(snowflakeKeyGenerator.generateKey()) :
                ApiUtil.getUuid();
    }

    private Set<String> getMgrUserIds(String orgId, String userId, String actvId, Long partId) {
        List<String> mgrList = relationService.listPartRelation(orgId, actvId, partId,
                ActvRelationTypeEnum.PRINCIPAL.getType(), PrincipalJoinMethodEnum.ALL.getType());
        if (CollectionUtils.isEmpty(mgrList)) {
            return Sets.newHashSet(userId);
        }
        return Sets.newHashSet(mgrList);
    }

    private String getOwnerDynamicUserGroupId(String orgId, String actvId, Long partId) {
        ActivityDynamicGroup dynamicGroup = activityDynamicGroupService.getByActvAndPart(orgId, actvId, partId,
                Constants.INT_1);
        return dynamicGroup == null ? null : dynamicGroup.getUserGroupId();
    }

    private void updateEnroll(Activity activity, String orgId, String userId) {
        //查询活动对应的参与信息
        ActivityParticipation participationInfo = partService.getParticipationInfoByActvId(orgId, activity.getId());
        if (participationInfo != null && StringUtils.isNotEmpty(participationInfo.getApplyId())) {

            CustomActivitySendMsgCheckCompo customBean = AomBeanNameUtils.getCustomBean(
                    CustomActivitySendMsgCheckCompo.class, activity.getActvRegId());

            Integer sendMsg = 1;
            if (customBean != null) {
                AomMsgCustomParam aomMsgCustomParam = new AomMsgCustomParam();
                aomMsgCustomParam.setMsgType(Constants.INT_1);
                aomMsgCustomParam.setActvId(activity.getId());
                aomMsgCustomParam.setOrgId(activity.getOrgId());
                aomMsgCustomParam.setRegId(activity.getActvRegId());
                Boolean b = customBean.sendMsgCheck(aomMsgCustomParam);
                if (!b) {
                    sendMsg = 0;
                }
            }
            enrollWrapper.updateInfo4Enroll(activity, userId, participationInfo.convertEnrollIds(),
                    participationInfo.getId(), sendMsg);
        }
    }

    private boolean activityTimeChange(Activity oldActivity, Activity4Update bean) {
        Integer timeModel = oldActivity.getTimeModel();
        Integer newTimeModel = bean.getTimeModel();
        Date startTime = oldActivity.getStartTime();
        Date endTime = oldActivity.getEndTime();
        Date newStartTime = bean.getStartTime();
        Date newEndTime = bean.getEndTime();
        if (!Objects.equals(timeModel, newTimeModel)) {
            return true;
        }

        if (Objects.equals(newTimeModel, AomTimeModelEnum.FIX.getType())) {
            //起止时间模式
            return !Objects.equals(startTime, newStartTime) || !Objects.equals(endTime, newEndTime);
        } else if (Objects.equals(newTimeModel, AomTimeModelEnum.RELATIVE.getType())) {
            return !Objects.equals(oldActivity.getStartDayOffset(), bean.getStartDayOffset()) || !Objects.equals(
                    oldActivity.getEndDayOffset(), bean.getEndDayOffset());
        }
        return false;
    }

    private void verifyRegId(String actvRegId) {
        if (registryManager.getRegistryConfig(actvRegId) == null) {
            throw new ApiException(BaseErrorConsts.ACTV_REGID_INVALID);
        }
    }

    private UserCacheDetail toUserCacheDetail(String orgId, String operatorId) {
        UserCacheDetail user = new UserCacheDetail();
        user.setOrgId(orgId);
        user.setUserId(operatorId);
        user.setAdmin(AomConstants.ONE);
        return user;
    }

    private AllConfig toAllConfig(Activity4Update bean) {
        AllConfig allConfig = new AllConfig();
        if (bean.getAutoRelease() != null) {
            AuditConfig auditConfig = new AuditConfig();
            auditConfig.setAutoRelease(bean.getAutoRelease());
            allConfig.setAuditConfig(auditConfig);
        }
        if (CollectionUtils.isNotEmpty(bean.getOrgDeptIds())) {
            OrganizerConfig organizerConfig = new OrganizerConfig();
            organizerConfig.setDeptIds(bean.getOrgDeptIds());
            allConfig.setOrganizerConfig(organizerConfig);
        }
        allConfig.setImConfig(bean.getImConfig());
        allConfig.setDynamicUserGroupConfig(bean.getDynamicUserGroupConfig());
        return allConfig;
    }
}
