package com.yxt.aom.base.bean.part.group;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

import java.util.List;

/**
 * @description:重新分组
 * @author: shenb
 * @date: 2019/12/28 13:46
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "重新分组")
public class Group4DivideAgain {

    @Schema(description = "活动id")
    @NotNull
    private String actvId;

    @Schema(description = "UACD注册表中定义Id")
    @NotNull
    private String regId;

    @Schema(description = "参与id")
    private Long participationId;

    @Schema(description = "分组规则 0 默认 1 相同部门尽量分开 ", example = "0", requiredMode = Schema.RequiredMode.REQUIRED)
    @Range(min = 0, max = 1)
    private int type;

    @Schema(description = "需要重新分组的小组列表", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty
    private List<Long> groupIds;
}
