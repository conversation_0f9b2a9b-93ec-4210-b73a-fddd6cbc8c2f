package com.yxt.aom.base.bean.part.honor;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class Honor4Del {

    @Schema(description = "UACD注册表中定义Id", requiredMode = Schema.RequiredMode.REQUIRED)
    private String regId;

    @Schema(description = "荣誉id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull
    private Long id;
}
