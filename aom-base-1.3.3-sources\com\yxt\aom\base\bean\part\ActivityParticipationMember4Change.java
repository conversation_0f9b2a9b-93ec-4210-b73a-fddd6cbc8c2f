package com.yxt.aom.base.bean.part;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yxt.aom.base.enums.AomActivityMemberChangeEnum;
import com.yxt.common.Constants;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;


@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
public class ActivityParticipationMember4Change extends ActivityMember4Add {

    /**
     * 注册表id,方便找到对应类型
     */
    @Schema(description = "活动/项目注册id")
    private String regId;

    /**
     * 具体活动ids
     */
    private List<String> refIds;

    @Schema(description = "生效时间")
    private String orgId;

    /**
     * 操作人id
     */
    private String optUserId;

    /**
     * 操作人姓名
     */
    private String fullName = StringUtils.EMPTY;

    @Schema(description = "生效时间")
    private Date effectTime;

    private AomActivityMemberChangeEnum eventType;

    private Date currentTime;

    @Schema(description = "设置的开始学习时间")
    @JsonFormat(pattern = Constants.SDF_YEAR2SECOND, timezone = Constants.STR_GMT8)
    private String setStartTime;

    @Schema(description = "设置的结束学习时间")
    @JsonFormat(pattern = Constants.SDF_YEAR2SECOND, timezone = Constants.STR_GMT8)
    private String setEndTime;

    private String retryTopic;

    @Schema(description = "类型(1-活动, 2-项目)")
    private Integer actvType;

    /**
     * 是否推送消息配置 true-推送消息 false-不推送消息
     */
    private Boolean sendMsg = Boolean.FALSE;

    private String sourceCode;

    @Schema(description = "小组id，项目添加学员不传，小组添加学员传对应小组id")
    private Long groupId = 0L;

}
