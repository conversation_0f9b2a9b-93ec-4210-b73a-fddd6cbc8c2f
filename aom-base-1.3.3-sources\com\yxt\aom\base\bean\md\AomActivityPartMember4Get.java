package com.yxt.aom.base.bean.md;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.yxt.common.annotation.timezone.DateFormatField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
@Getter
@Setter
@Schema(description = "活动参与成员详情返回")
public class AomActivityPartMember4Get implements Serializable {

    @Serial
    private static final long serialVersionUID = -515052022323011335L;

    @Schema(description="主键;id主键")
    private String id;

    @Schema(description="机构号;机构id")
    private String orgId;

    @Schema(description="参与身份")
    private String formal;

    @Schema(description="加入方式")
    private String joinMethod;

    @Schema(description="加入时间")
    private Date joinTime;

    @Schema(description="参与人ID;按照业务需求,返回应用的实体字段")
    @JsonProperty("@userId")
    private AomDrawer4RespDTO userId;

    @Schema(description="参与统计;按照业务需求,返回应用的实体字段")
    @JsonProperty("@memberStatistics")
    @DateFormatField(isobj = true)
    private AomDrawer4RespDTO memberStatistics;

    @Schema(description = "状态")
    private String status;

    /**
     * 活动id
     */
    private String actvId;

    /**
     * 活动对应的参与id
     */
    private Long partId;

}
