package com.yxt.aom.base.bean.part.group;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class TutorBean {

    private String id;

    private String fullname;

    private long groupId;

    @Schema(description = "部门名称")
    private String deptName;

    @Schema(description = "人员头像")
    private String imageUrl;
}
