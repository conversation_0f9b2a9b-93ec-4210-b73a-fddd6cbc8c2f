package com.yxt.aom.base.bean.common;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @createDate 2025/5/29 19:07:15
 */
@Data
public class Audit4Revoke {

    @Schema(description = "活动/项目的具体类型(UACD注册表中定义)", example = "proj_o2o", requiredMode = Schema.RequiredMode.REQUIRED)
    private String regId;

    @Schema(description = "活动id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull
    private String actvId;

}
