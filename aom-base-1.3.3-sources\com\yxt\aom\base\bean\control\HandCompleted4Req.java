package com.yxt.aom.base.bean.control;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/2/27 14:28
 */
@Data
public class HandCompleted4Req {
    @Schema(description = "项目id")
    private String actvId;
    @Schema(description = "任务id")
    private Long itemId;
    @Schema(description = "是否全部 0 否 1是")
    private int type;
    @Schema(description = "全部情况下需要过滤用户ids")
    private Set<String> filterUserIds;

    @Schema(description = "非全部情况下选择的用户账号")
    private List<String> userNames;

    @Schema(description = "1正式学员 0旁听学员",requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer formal;

    @Schema(description = "部门列表")
    private List<String> deptList;

    @Schema(description = "用户关键词")
    private String keyword;

    @Schema(description = "作业提交 0-未提交 1-已提交")
    private Integer submitStatus;

    @Schema(description = "作业批阅状态  1-待批阅 2-已合格 3-不合格 4-批阅中 5-退回重做 不传-查全部")
    private Integer remarkStatus;

    @Schema(description = "考试是否已通过: 0未通过；1已通过；-1或者不传:所有", example = "-1")
    private Integer passed;

    @Schema(description = "用户考试状态。0：未开始；1：考试中；2：已提交；3：批阅中；4：已批阅；-1或者不传：所有", example = "-1")
    private Integer examStatus;

    @Schema(description = "鉴定状态,0-未申请,1-待鉴定,2-已合格,3-不合格; 不传查全部")
    private Integer appraisalStatus;

    @Schema(description = "账号状态 0-已禁用 1-已启用 2-已删除")
    private Integer accountStatus;

}
