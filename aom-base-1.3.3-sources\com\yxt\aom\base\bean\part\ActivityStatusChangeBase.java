package com.yxt.aom.base.bean.part;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

@Getter
@Setter
@Schema(name = "活动状态改变")
public class ActivityStatusChangeBase {

    @Schema(description = "活动Id列表", example = "活动Id", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> actvIds;

    @Schema(description = "操作人")
    private String optUserId;

    @Schema(description = "机构Id")
    private String orgId;

    @Schema(description = "活动注册id")
    private String regId;

    @Schema(description = "活动来源id")
    private String sourceId;

    @Schema(description = "老的状态")
    private Integer oldStatus;

    @Schema(description = "用户登陆的source, 例如501,502等")
    private String source;

    /**
     * 业务定制参数
     */
    @Schema(description = "业务定制参数")
    private Map<String, Object> params;
}
