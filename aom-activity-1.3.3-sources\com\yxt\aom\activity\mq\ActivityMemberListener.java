package com.yxt.aom.activity.mq;

import com.alibaba.fastjson.JSON;
import com.yxt.aom.base.bean.part.ActivityParticipationMember4Change;
import com.yxt.aom.base.bean.part.AomAddActivityMemberMsgBean;
import com.yxt.aom.base.common.AomConstants;
import com.yxt.aom.base.service.part.impl.ActivityParticipationMemberService;
import com.yxt.aom.common.config.AomDataSourceTypeHolder;
import com.yxt.aom.common.config.AomDbProperties;
import com.yxt.dbrouter.core.toolkit.DBRouteHolder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.Map;

import static com.yxt.aom.base.common.AomMqConstants.TOPIC_ACTIVITY_MEMBER_ADD_THIRD;

/**
 * 同步学员
 */
@Slf4j
@RequiredArgsConstructor
@Component
@ConditionalOnProperty(name = "aom.rocketmq.consumer-enabled", havingValue = "true", matchIfMissing = true)
@RocketMQMessageListener(consumerGroup = AomConstants.MQ_GROUP_PREFIX
        + TOPIC_ACTIVITY_MEMBER_ADD_THIRD, topic = TOPIC_ACTIVITY_MEMBER_ADD_THIRD,
        consumeMode = ConsumeMode.CONCURRENTLY, consumeThreadNumber = 3)
public class ActivityMemberListener implements RocketMQListener<String> {
    private final ActivityParticipationMemberService activityParticipationMemberService;
    private final AomDbProperties aomDbProperties;

    @Override
    public void onMessage(String message) {
        log.info("ActivityMemberListener, message: {}", message);
        try {
            if(StringUtils.isEmpty(message)) {
                return;
            }
            AomAddActivityMemberMsgBean bean = JSON.parseObject(message, AomAddActivityMemberMsgBean.class);
            if (bean == null || bean.getBean() == null) {
                log.warn("ActivityMemberListener invalid message: {}", message);
                return;
            }
            ActivityParticipationMember4Change changeBean = bean.getBean();
            String regId = changeBean.getRegId();
            Map<String, String> dsmap = aomDbProperties.getDsmap();
            if (!dsmap.containsKey(regId)) {
                return;
            }
            changeBean.setRetryTopic(TOPIC_ACTIVITY_MEMBER_ADD_THIRD);
            AomDataSourceTypeHolder.set(regId);
            DBRouteHolder.push(changeBean.getOrgId());
            activityParticipationMemberService.addMemberV2(bean.getMemberList(), changeBean);
        } catch (Exception ex) {
            log.error("ActivityMemberListener message failed. message: {} , err: ", message, ex);
        }
    }
}
