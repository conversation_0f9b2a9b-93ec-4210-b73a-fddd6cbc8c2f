package com.yxt.aom.base.bean.part.group;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class Honor4Get {

    @Schema(description = "荣誉id", example = "123", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonSerialize(using = ToStringSerializer.class)
    private long id;

    @Schema(description = "机构id", example = "123", requiredMode = Schema.RequiredMode.REQUIRED)
    private String orgId;

    @Schema(description = "荣誉名称", example = "name", requiredMode = Schema.RequiredMode.REQUIRED)
    private String name;

    @Schema(description = "荣誉图标", example = "http://...", requiredMode = Schema.RequiredMode.REQUIRED)
    private String imageUrl;

    @Schema(description = "状态 0未发放 1已发放", example = "0", requiredMode = Schema.RequiredMode.REQUIRED)
    private int status;
}
