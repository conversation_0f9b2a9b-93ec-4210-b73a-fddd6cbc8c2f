package com.yxt.aom.base.bean.part;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.yxt.bifrost.udp.localization.L10NContent;
import com.yxt.bifrost.udp.localization.annotation.L10NProperty;
import com.yxt.bifrost.udp.localization.enums.ShapeEnum;
import com.yxt.common.annotation.timezone.DateFormatField;
import com.yxt.common.enums.YesOrNo;
import com.yxt.udpfacade.bean.enums.ResourceTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

import static com.yxt.common.Constants.INT_0;

/**
 * <AUTHOR>
 * @since 2019/12/19 20:17
 */
@Getter
@Setter
public class PartMember4List implements L10NContent {

    @Schema(description = "主键", example = "1210805511352545282")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @Schema(description = "用户id", example = "1210805511352545282")
    @L10NProperty(resourceType = ResourceTypeEnum.USER, shape = ShapeEnum.KEY)
    private String userId;

    @Schema(description = "工号", example = "sok")
    private String userNo;

    @Schema(description = "账号", example = "sok")
    private String username;

    @Schema(description = "姓名", example = "张三")
    @L10NProperty(resourceType = ResourceTypeEnum.USER, shape = ShapeEnum.VALUE)
    private String fullname;

    @L10NProperty(resourceType = ResourceTypeEnum.DEPT, shape = ShapeEnum.KEY)
    private String deptId;

    @Schema(description = "部门", example = "测试部门")
    @L10NProperty(resourceType = ResourceTypeEnum.DEPT, shape = ShapeEnum.VALUE)
    private String deptName;

    @L10NProperty(resourceType = ResourceTypeEnum.POSITION, shape = ShapeEnum.KEY)
    private String positionId;

    @Schema(description = "岗位", example = "测试岗位")
    @L10NProperty(resourceType = ResourceTypeEnum.POSITION, shape = ShapeEnum.VALUE)
    private String positionList;

    @Schema(description = "电话", example = "13878232412")
    private String mobile;

    @Schema(description = "小组名", example = "测试小组")
    private String groupName;

    @Schema(description = "小组id", example = "1210805511352545282")
    @JsonSerialize(using = ToStringSerializer.class)
    private long groupId;

    @Schema(description = "任务完成数量", example = "5")
    private int completeCount;

    @Schema(description = "是否合格", example = "1")
    private int passed;

    @Schema(description = "项目成绩", example = "66")
    private BigDecimal activityScore;

    @Schema(description = "学员项目评价", example = "测试评价")
    private String activityComment;

    @Schema(description = "辅导员名字 逗号隔开", example = "张三,李四,王五")
    private String tutorNames;

    @Schema(description = "座位号", example = "17")
    private String seatNo = "-1";

    @Schema(description = "是否优秀学员 0：不是 1：是 ", example = "0")
    private int outstanding;

    @Schema(description = "组长 0：不是 1：是 ", example = "0")
    private int leader;

    @Schema(description = "项目任务数", example = "10")
    private int taskCount;

    @Schema(description = "头像")
    private String avatarUrl;

    @Schema(description = "是否是讲师；0：否；1：是；")
    private Integer wasTeacher = YesOrNo.NO.getValue();

    @Schema(description = "加入方式（0：Unknown（默认）；1：管理员手动加入；2：自动加入；3：通过报名加入；5：通过动态用户组加入）")
    private Integer joinMethod;

    @Schema(description = "视图用户状态；0：已禁用；1：启用")
    private Integer status;

    @Schema(description = "视图用户删除状态；0：正常；1：已删除；")
    private Integer deleted;

    @Schema(description = "列表用户状态；0：正常；1：已禁用；2：已删除")
    private Integer userStatus;

    @DateFormatField(isDate = true)
    @Schema(description = "最近学习时间", example = "2021-08-08 17:11:58")
    private Date lastStudyTime;

    @DateFormatField(isDate = true)
    @Schema(description = "学员学习开始时间", example = "2021-08-08 17:11:58")
    private Date startTime;

    @DateFormatField(isDate = true)
    @Schema(description = "学员学习结束时间", example = "2021-08-08 17:11:58")
    private Date endTime;

    @Schema(description = "证书")
    private int certificateNum;

    @DateFormatField(isDate = true)
    @Schema(description = "入职时间", example = "2021-08-08")
    private Date hireDate;

    @Schema(description = "国籍")
    private String nationality;

    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "分数", example = "1000")
    private BigDecimal credit;

    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "积分", example = "50")
    private BigDecimal integral;

    @Schema(description = "加入时间", example = "2022-10-01 00:00:00")
    @DateFormatField(isDate = true)
    private Date joinTime;

    @Schema(description = "完成所有任务的时间", example = "2022-10-09 00:00:00")
    @DateFormatField(isDate = true)
    private Date finishedTime;

    @Schema(description = "称号名称")
    private String title;

    @Schema(description = "称号id")
    private Long titleId;

    @Schema(description = "是否逾期 0-未逾期 1-逾期", example = "1")
    private Integer overdue = INT_0;

    @Schema(description = "所属平台名称")
    private String orgName;

    @Schema(description = "所属平台ID")
    private String orgId;

    @Schema(description = "1正式学员 0旁听学员", example = "1")
    private Integer formal;

    @Schema(description = "职级", example = "P1")
    private String gradeName;

    @Schema(description = "学员收到项目时间", example = "2022-10-09 00:00:00")
    @DateFormatField(isDate = true)
    private Date acceptedTime;

    @Schema(description = "第一次学习项目时间", example = "2022-10-09 00:00:00")
    @DateFormatField(isDate = true)
    private Date firstStudyTime;

    @Schema(description = "选修任务+必修任务完成数量", example = "10")
    private Integer completeAllTaskCount;

    @Schema(description = "选修任务+必修任务总数量", example = "30")
    private Integer allTaskCount;

    @Schema(description = "必修任务完成数量", example = "10")
    private Integer completeRequiredCount;

    @Schema(description = "必修任务总数量", example = "30")
    private Integer requiredTaskCount;

    @Schema(description = "完成标准 默认0 完成所有必修 1完成所有任务 2完成项目内指定任务数量 3完成项目内指定阶段数量 4获得项目内指定学分")
    private Integer studyStandard;

    @Schema(description = "必修完成率")
    private BigDecimal requiredProcessRate;

    @Schema(description = "全部完成率")
    private BigDecimal allProcessRate;

    @Schema(description = "带教任务完成数")
    private Integer ojtCompleteAllCount;

    @Schema(description = "带教任务总数")
    private Integer ojtAllTotalCount;

    @Schema(description = "选修任务数")
    private Integer electiveCount;

    @Schema(description = "选修完成数")
    private Integer completeElectiveCount;

    @Schema(description = "项目完成进度")
    private BigDecimal completeProcessRate;

    @Schema(description = "0-未开始 1-进行中 2-已完成")
    private int completeStatus;

    @Schema(description = "项目完成时间", example = "2022-10-09 00:00:00")
    @DateFormatField(isDate = true)
    private Date completeTime;

    private Integer completePeriodCount;

    @Schema(description = "整体进度，根据完成标准做呈现")
    private String fullProgress;

    /**
     * 项目下阶段数量
     */
    private int periodCount = 0;

    private String allProgress;

    /**
     * 已获得学分
     */
    private BigDecimal completeStudyScore;

     /**
     * 学员活动完成状态：0-未开始 1-进行中 2-已完成
     */
    private Integer actvCompletedStatus;

    @Schema(description = "活动完成率")
    private BigDecimal actCompletedRate;

    @Schema(description = "必修活动完成率")
    private BigDecimal requiredTaskCompletedRate;

    @Schema(description = "选修活动完成进度")
    private BigDecimal electiveTaskCompletedRate;

    @Schema(description = "毕业状态(0-未毕业 1-已毕业 2-取消毕业) -- V6.1", example = "1")
    private Integer graduated;

    /**
     * 活动id
     */
    private String actvId;

    /**
     * 参与id
     */
    private Long partId;

}
