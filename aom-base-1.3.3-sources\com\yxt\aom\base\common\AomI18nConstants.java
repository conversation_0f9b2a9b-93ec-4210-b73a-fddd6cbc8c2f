package com.yxt.aom.base.common;

import com.google.common.collect.Lists;

import java.util.Collections;
import java.util.List;

/**
 * 国际化key
 *
 * <AUTHOR> shan
 * @since 2021 /7/23 12:14
 */
public final class AomI18nConstants {

    private AomI18nConstants() {
    }

    /**
     * The type Member column.
     */
    public static class MemberColumn {

        private MemberColumn() {

        }

        /**
         * The constant STATUS_DELETED.
         */
        public static final String STATUS_DELETED = "apis.aom.label.user.status.deleted";
        /**
         * The constant STATUS_DISABLED.
         */
        public static final String STATUS_DISABLED = "apis.aom.label.user.status.disabled";
        /**
         * The constant STATUS_ENABLED.
         */
        public static final String STATUS_ENABLED = "apis.aom.label.user.status.enabled";
        /**
         * The constant STATUS_NOTSTARTED.
         */
        public static final String STATUS_NOTSTARTED = "apis.aom.label.user.status.notStarted";
        /**
         * The constant STATUS_UNDERWAY.
         */
        public static final String STATUS_UNDERWAY = "apis.aom.label.user.status.learning";
        /**
         * The constant STATUS_COMPLETED.
         */
        public static final String STATUS_COMPLETED = "apis.aom.label.user.status.completed";
        /**
         * 学员活动完成状态  已逾期
         */
        public static final String STATUS_HAS_DELAY = "apis.aom.label.user.status.has.delay";

        /**
         * The constant STATUS_DELAY_COMPLETED.
         */
        public static final String STATUS_DELAY_COMPLETED = "apis.aom.label.user.status.delayCompleted";
        /**
         * The constant FORMAL.
         */
        public static final String FORMAL = "apis.aom.label.user.formal";
        /**
         * The constant IN_FORMAL.
         */
        public static final String IN_FORMAL = "apis.aom.label.user.informal";
        /**
         * The constant SUBMIT_TYPE.
         */
        public static final String SUBMIT_TYPE = "apis.aom.label.user.type.submit";
        /**
         * The constant UN_SUBMIT_TYPE.
         */
        public static final String UN_SUBMIT_TYPE = "apis.aom.label.user.type.unsubmit";

        /**
         * The constant JOINMETHOD_BY_ADMIN.
         */
        public static final String JOINMETHOD_BY_ADMIN = "apis.aom.label.join.method.byadmin";
        /**
         * The constant JOINMETHOD_AUTO_JOIN.
         */
        public static final String JOINMETHOD_AUTO_JOIN = "apis.aom.label.join.method.autojoin";
        /**
         * The constant JOINMETHOD_BY_ENROLL.
         */
        public static final String JOINMETHOD_BY_ENROLL = "apis.aom.label.join.method.byenroll";
        /**
         * The constant JOINMETHOD_BY_SIGN.
         */
        public static final String JOINMETHOD_BY_SIGN = "apis.aom.label.join.method.bysign";
        /**
         * The constant JOINMETHOD_DYNAMIC_GROUP.
         */
        public static final String JOINMETHOD_DYNAMIC_GROUP = "apis.aom.label.join.method.dynamic.group";


        public static final String JOIN_FLIP_BY_SCAN = "apis.aom.label.join.flip.scan.add";

        public static final String UN_JOIN_FLIP = "apis.aom.label.join.flip.unjoin";

        public static final String HAS_OVERDUE = "apis.aom.label.user.actv.status.has.overdue";

        public static final String NOT_OVERDUE = "apis.aom.label.user.actv.status.not.overdue";


        /**
         * Keys list.
         *
         * @return the list
         */
        public static List<String> KEYS() { // NOSONAR
            return Lists
                    .newArrayList(STATUS_DELETED, STATUS_DISABLED, STATUS_ENABLED, STATUS_NOTSTARTED, STATUS_UNDERWAY,
                            STATUS_COMPLETED, STATUS_DELAY_COMPLETED,FORMAL,IN_FORMAL, SUBMIT_TYPE, UN_SUBMIT_TYPE,
                        JOINMETHOD_BY_ADMIN,JOINMETHOD_AUTO_JOIN,JOINMETHOD_BY_ENROLL,JOINMETHOD_BY_SIGN,
                            JOINMETHOD_DYNAMIC_GROUP,UN_JOIN_FLIP, HAS_OVERDUE, NOT_OVERDUE);
        }
    }

    /**
     * The type Task result column.
     */
    public static class TaskResultColumn {

        private TaskResultColumn() {

        }

        /**
         * The constant PASS_UN_PASS.
         */
        public static final String PASS_UN_PASS = "apis.aom.label.result.pass.un_pass";
        /**
         * The constant PASS_PASS.
         */
        public static final String PASS_PASS = "apis.aom.label.result.pass.pass";
        /**
         * The constant PASS_GOOD.
         */
        public static final String PASS_GOOD = "apis.aom.label.taskresult.pass.good";
        /**
         * The constant PASS_EXCELLENT.
         */
        public static final String PASS_EXCELLENT = "apis.aom.label.taskresult.pass.excellent";
        /**
         * The constant PASS_ALREADY.
         */
        public static final String PASS_ALREADY = "apis.aom.label.taskresult.pass.already";
        /**
         * The constant PASS_IN_PROGRESS.
         */
        public static final String PASS_IN_PROGRESS = "apis.aom.label.taskresult.pass.inprogress";
        /**
         * The constant WITHDRAW.
         */
        public static final String WITHDRAW = "apis.aom.label.taskresult.withdraw";

        /**
         * The constant UNCOMMITTED.
         */
        public static final String UNCOMMITTED = "apis.aom.label.taskresult.remark.uncommitted";
        /**
         * The constant TO_REMARK.
         */
        public static final String TO_REMARK = "apis.aom.label.taskresult.remark.to_remark";

        /**
         * The constant APPRAISAL_UNCOMMITTED.
         */
        public static final String APPRAISAL_UNCOMMITTED = "apis.aom.label.taskresult.appraisal.uncommitted";
        /**
         * The constant TO_APPRAISAL.
         */
        public static final String TO_APPRAISAL = "apis.aom.label.taskresult.appraisal.to_appraisal";

        /**
         * The constant APPRAISAL_ING.
         */
        public static final String APPRAISAL_ING = "apis.aom.label.taskresult.appraisal.appraisal_ing";

        /**
         * The constant FINISH_FINISH.
         */
        public static final String FINISH_FINISH = "apis.aom.label.taskresult.finish.finish";
        /**
         * The constant FINISH_UNFINISH.
         */
        public static final String FINISH_UNFINISH = "apis.aom.label.taskresult.finish.un_finish";

        /**
         * The constant STATUS_NOT_STARTED.
         */
        public static final String STATUS_NOT_STARTED = "apis.aom.label.taskresult.status.not_started";
        /**
         * The constant STATUS_IN_PROGRESS.
         */
        public static final String STATUS_IN_PROGRESS = "apis.aom.label.taskresult.status.in_progress";
        /**
         * The constant STATUS_DONE.
         */
        public static final String STATUS_DONE = "apis.aom.label.taskresult.status.done";
        /**
         * The constant STATUS_DONE_DELAY.
         */
        public static final String STATUS_DONE_DELAY = "apis.aom.label.taskresult.status.done_delay";
        /**
         * The constant STATUS_REJECTION.
         */
        public static final String STATUS_REJECTION = "apis.aom.label.taskresult.status.rejection";
        /**
         * The constant STATUS_UN_DONE.
         */
        public static final String STATUS_UN_DONE = "apis.aom.label.taskresult.status.un_done";


        /**
         * The constant SIGN_STATUS_LEAVE.
         */
        public static final String SIGN_STATUS_LEAVE = "apis.aom.label.taskresult.sign.status.leave";
        /**
         * The constant SIGN_STATUS_SIGN_IN.
         */
        public static final String SIGN_STATUS_SIGN_IN = "apis.aom.label.taskresult.sign.status.sign_in";
        /**
         * The constant SIGN_STATUS_LATE_SIGN_IN.
         */
        public static final String SIGN_STATUS_LATE_SIGN_IN = "apis.aom.label.taskresult.sign.status.late_sign_in";
        /**
         * The constant SIGN_STATUS_NO_SIGN.
         */
        public static final String SIGN_STATUS_NO_SIGN = "apis.aom.label.taskresult.sign.status.no_sign";
        /**
         * The constant SIGN_STATUS_SIGN_OUT.
         */
        public static final String SIGN_STATUS_SIGN_OUT = "apis.aom.label.taskresult.sign.status.sign_out";
        /**
         * The constant SIGN_STATUS_SIGN_OUT_EARLY.
         */
        public static final String SIGN_STATUS_SIGN_OUT_EARLY = "apis.aom.label.taskresult.sign.status.sign_out_early";
        /**
         * 精华
         */
        public static final String ESSENTIAL_HW = "apis.aom.label.taskresult.essential.homework";
        /**
         * 非精华
         */
        public static final String NON_ESSENTIAL_HW = "apis.aom.label.taskresult.non.essential.homework";

        /**
         * Keys list.
         *
         * @return the list
         */
        public static List<String> KEYS() { // NOSONAR
            return Lists
                .newArrayList(PASS_UN_PASS,
                    PASS_PASS,
                    PASS_GOOD,
                    PASS_EXCELLENT,
                    PASS_ALREADY,
                    PASS_IN_PROGRESS,
                    WITHDRAW,
                    UNCOMMITTED,
                    TO_REMARK,
                    APPRAISAL_UNCOMMITTED,
                    TO_APPRAISAL,
                    APPRAISAL_ING,
                    FINISH_FINISH,
                    FINISH_UNFINISH,
                    STATUS_NOT_STARTED,
                    STATUS_IN_PROGRESS,
                    STATUS_DONE,
                    STATUS_DONE_DELAY,
                    STATUS_REJECTION,
                    STATUS_UN_DONE,
                    SIGN_STATUS_LEAVE,
                    SIGN_STATUS_SIGN_IN,
                    SIGN_STATUS_LATE_SIGN_IN,
                    SIGN_STATUS_NO_SIGN,
                    SIGN_STATUS_SIGN_OUT,
                    SIGN_STATUS_SIGN_OUT_EARLY,
                    PASSED,
                    UNPASSED,UNMARKED,MARKED, ESSENTIAL_HW, NON_ESSENTIAL_HW);
        }
    }

    /**
     * The type Project column.
     */
    public static class ProjectColumn {

        private ProjectColumn() {

        }

        /**
         * The constant STATUS_DRAFT.
         */
        public static final String STATUS_DRAFT = "apis.aom.label.project.status.draft";
        /**
         * The constant STATUS_ING.
         */
        public static final String STATUS_ING = "apis.aom.label.project.status.ing";
        /**
         * The constant STATUS_END.
         */
        public static final String STATUS_END = "apis.aom.label.project.status.end";
        /**
         * The constant STATUS_PIGEONHOLE.
         */
        public static final String STATUS_PIGEONHOLE = "apis.aom.label.project.status.pigeonhole";
        /**
         * The constant STATUS_DELETE.
         */
        public static final String STATUS_DELETE = "apis.aom.label.project.status.delete";
        /**
         * The constant STATUS_PAUSE.
         */
        public static final String STATUS_PAUSE = "apis.aom.label.project.status.pause";
        /**
         * The constant STATUS_WITHDRAW.
         */
        public static final String STATUS_WITHDRAW = "apis.aom.label.project.status.withdraw";
        /**
         * The constant STATUS_RELEASED.
         */
        public static final String STATUS_RELEASED = "apis.aom.label.project.status.released";

        /**
         * Keys list.
         *
         * @return the list
         */
        public static List<String> KEYS() {
            return Collections.unmodifiableList(
                    Lists.newArrayList(STATUS_DRAFT, STATUS_ING, STATUS_END, STATUS_PIGEONHOLE, STATUS_DELETE,
                            STATUS_PAUSE, STATUS_WITHDRAW, STATUS_RELEASED));
        }
    }

    /**
     * The type Ote column.
     */
    public static class OteColumn {

        private OteColumn() {

        }

        /**
         * The constant YES.
         */
        public static final String YES = "apis.aom.ote.label.yes";
        /**
         * The constant NO.
         */
        public static final String NO = "apis.aom.ote.label.no";
        /**
         * The constant DAY.
         */
        public static final String DAY = "apis.aom.ote.label.day";
        /**
         * The constant HOUR.
         */
        public static final String HOUR = "apis.aom.ote.label.hour";
        /**
         * The constant MINUTE.
         */
        public static final String MINUTE = "apis.aom.ote.label.minute";
        /**
         * The constant MINUTES.
         */
        public static final String MINUTES = "apis.aom.ote.label.minutes";
        /**
         * The constant SECOND.
         */
        public static final String SECOND = "apis.aom.ote.label.second";

        /**
         * 参考设备
         */
        public static final String PC = "apis.aom.ote.label.device.pc";
        /**
         * The constant H5_ANDROID.
         */
        public static final String H5_ANDROID = "apis.aom.ote.label.device.h5_android";
        /**
         * The constant H5_IOS.
         */
        public static final String H5_IOS = "apis.aom.ote.label.device.h5_ios";
        /**
         * The constant H5.
         */
        public static final String H5 = "apis.aom.ote.label.device.h5";
        /**
         * The constant WECHAT.
         */
        public static final String WECHAT = "apis.aom.ote.label.device.wechat";
        /**
         * The constant ORIGINAL_ANDROID.
         */
        public static final String ORIGINAL_ANDROID = "apis.aom.ote.label.device.original_android";
        /**
         * The constant ORIGINAL_IOS.
         */
        public static final String ORIGINAL_IOS = "apis.aom.ote.label.device.original_ios";
        /**
         * The constant DING_TALK.
         */
        public static final String DING_TALK = "apis.aom.ote.label.device.ding_talk";

        public static final String WE_COM = "apis.aom.ote.label.device.we_com";


        /**
         * Keys list.
         *
         * @return the list
         */
        public static List<String> KEYS() { // NOSONAR
            return Lists
                .newArrayList(YES,
                    NO,
                    DAY,
                    HOUR,
                    MINUTE,
                    MINUTES,
                    SECOND,
                    PC,
                    H5_ANDROID,
                    H5_IOS,
                    H5,WECHAT,
                    ORIGINAL_ANDROID,
                    ORIGINAL_IOS,
                    DING_TALK,WE_COM);
        }
    }

    /**
     * The type Import plan error column.
     */
    public static class ImportPlanErrorColumn {

        private ImportPlanErrorColumn() {

        }

        /**
         * The constant REQUIRED.
         */
        public static final String REQUIRED = "apis.aom.label.importplanerror.content.required";
        /**
         * The constant NAME_LENGTH_MAX.
         */
        public static final String NAME_LENGTH_MAX = "apis.aom.label.importplanerror.content.name_length_max";
        /**
         * The constant YEAR_FORMAT.
         */
        public static final String YEAR_FORMAT = "apis.aom.label.importplanerror.content.year_format";
        /**
         * The constant QUARTER_FORMAT.
         */
        public static final String QUARTER_FORMAT = "apis.aom.label.importplanerror.content.quarter_format";
        /**
         * The constant MONTH_FORMAT.
         */
        public static final String MONTH_FORMAT = "apis.aom.label.importplanerror.content.month_format";
        /**
         * The constant STARTTIME_FORMAT.
         */
        public static final String STARTTIME_FORMAT = "apis.aom.label.importplanerror.content.starttime_format";
        /**
         * The constant ENDTIME_FORMAT.
         */
        public static final String ENDTIME_FORMAT = "apis.aom.label.importplanerror.content.endtime_format";
        /**
         * The constant BUDGET_FORMAT.
         */
        public static final String BUDGET_FORMAT = "apis.aom.label.importplanerror.content.budget_format";
        /**
         * The constant PERSONNUM_FORMAT.
         */
        public static final String PERSONNUM_FORMAT = "apis.aom.label.importplanerror.content.personnum_format";
        /**
         * The constant YEAR_ARRANGE.
         */
        public static final String YEAR_ARRANGE = "apis.aom.label.importplanerror.content.year_arrange";
        /**
         * The constant START_BEFORE_END.
         */
        public static final String START_BEFORE_END = "apis.aom.label.importplanerror.content.start_before_end";
        /**
         * The constant START_END_ARRANGE.
         */
        public static final String START_END_ARRANGE = "apis.aom.label.importplanerror.content.start_end_arrange";
        /**
         * The constant NO_DEPT.
         */
        public static final String NO_DEPT = "apis.aom.label.importplanerror.content.no_dept";
        /**
         * The constant BUDGET_MAX.
         */
        public static final String BUDGET_MAX = "apis.aom.label.importplanerror.content.budget_max";
        /**
         * The constant PERSONNUM_MAX.
         */
        public static final String PERSONNUM_MAX = "apis.aom.label.importplanerror.content.personnum_max";
        /**
         * The constant DESC_LENGTH_MAX.
         */
        public static final String DESC_LENGTH_MAX = "apis.aom.label.importplanerror.content.desc_length_max";
        /**
         * The constant NO_DEPT_AUTH.
         */
        public static final String NO_DEPT_AUTH = "apis.aom.label.importplanerror.content.no_dept_auth";
        /**
         * The constant EXT_REQUIRED.
         */
        public static final String EXT_REQUIRED = "apis.aom.label.importplanerror.content.ext_required";
        /**
         * The constant EXT_LENGTH_MAX.
         */
        public static final String EXT_LENGTH_MAX = "apis.aom.label.importplanerror.content.ext_length_max";
        /**
         * The constant EXT_NUMBER_FORMAT.
         */
        public static final String EXT_NUMBER_FORMAT = "apis.aom.label.importplanerror.content.ext_number_format";
        /**
         * The constant EXT_OPTIONS_FORMAT.
         */
        public static final String EXT_OPTIONS_FORMAT = "apis.aom.label.importplanerror.content.ext_options_format";
        /**
         * The constant EXT_DATE_FORMAT.
         */
        public static final String EXT_DATE_FORMAT = "apis.aom.label.importplanerror.content.ext_date_format";
        /**
         * 您填写的负责人不存在或格式错误
         */
        public static final String PRINCIPAL_NAME_FORMAT = "apis.aom.label.importplanerror.content.principalname_format";
        /**
         * 最多支持添加30个负责人
         */
        public static final String PRINCIPAL_NAME_OVERFLOW = "apis.aom.label.importplanerror.content.principalname_overflow";
        /**
         * 您填写的负责人账号已被禁用或锁定
         */
        public static final String PRINCIPAL_NAME_STATUS = "apis.aom.label.importplanerror.content.principalname_error_status";
        /**
         * 您添加的负责人不在您的数据权限内
         */
        public static final String NO_PRINCIPAL_AUTH = "apis.aom.label.importplanerror.content.no_principal_auth";
        /**
         * 您所填写的年度无预算信息，请确认该年度是否已添加预算
         */
        public static final String NO_BUDGETYEAR = "apis.aom.label.importplanerror.content.no.budgetyear";
        /**
         * 您所填写的部门无预算信息，请确认该部门是否已添加预算
         */
        public static final String NO_BUDGETDEPT = "apis.aom.label.importplanerror.content.no.budgetdept";
        /**
         *请填写预算对应的年度
         */
        public static final String BUDGET_HAS_DEPT_NO_YEAR = "apis.aom.label.importplanerror.content.budget.dept.must.year";
        /**
         *预算类型不存在
         */
        public static final String NO_BUDGET_TYPE = "apis.aom.label.importplanerror.content.no.budgetType";
        /**
         * 预算类型金额超出预算
         */
        public static final String BUDGET_AMOUNT_OVERFLOW = "apis.aom.label.importplanerror.content.budgetAmount.overflow";
        /**
         * 只填写预算类型及金额
         */
        public static final String BUDGET_HAS_TYPE_NO_YEAR = "apis.aom.label.importplanerror.content.budget.type.must.year";
        /**
         * 填写了预算年度和预算部门，未填写预算类型及金额
         */
        public static final String BUDGET_HAS_YEAY_NO_TYPE = "apis.aom.label.importplanerror.content.budget.year.must.type";
        /**
         * 只填写预算类型，未填写金额
         */
        public static final String BUDGET_HAS_TYPE_NO_AMOUNT = "apis.aom.label.importplanerror.content.budget.type.must.amount";
        /**
         * 该年度预算模式是严谨模式，填写的预算类型不在该年度预算中
         */
        public static final String NO_BUDGET_RIGOR_TYPE = "apis.aom.label.importplanerror.content.no.rigor.budgetType";
        /**
         * 预算类型重复
         */
        public static final String BUDGET_TYPE_REPEAT = "apis.aom.label.importplanerror.content.budget.type.repeat";
        /**
         *  预算金额格式错误
         */
        public static final String BUDGET_AMOUNT_FORMAT = "apis.aom.label.importplanerror.content.budget.amount.format";

        /**
         * Keys list.
         *
         * @return the list
         */
        public static List<String> KEYS() { // NOSONAR
            return Lists
                .newArrayList(REQUIRED, NAME_LENGTH_MAX, YEAR_FORMAT, QUARTER_FORMAT, MONTH_FORMAT, STARTTIME_FORMAT,
                    ENDTIME_FORMAT, BUDGET_FORMAT, PERSONNUM_FORMAT, YEAR_ARRANGE, START_BEFORE_END, START_END_ARRANGE,
                    NO_DEPT, BUDGET_MAX, PERSONNUM_MAX, DESC_LENGTH_MAX, NO_DEPT_AUTH, EXT_REQUIRED, EXT_LENGTH_MAX,
                    EXT_NUMBER_FORMAT, EXT_OPTIONS_FORMAT, EXT_DATE_FORMAT, PRINCIPAL_NAME_FORMAT, NO_PRINCIPAL_AUTH,
                    NO_BUDGETYEAR, NO_BUDGETDEPT, BUDGET_HAS_DEPT_NO_YEAR, NO_BUDGET_TYPE, BUDGET_AMOUNT_OVERFLOW,
                    BUDGET_HAS_TYPE_NO_YEAR, BUDGET_HAS_YEAY_NO_TYPE, BUDGET_HAS_TYPE_NO_AMOUNT, NO_BUDGET_RIGOR_TYPE,
                    BUDGET_TYPE_REPEAT, BUDGET_AMOUNT_FORMAT, PRINCIPAL_NAME_STATUS, PRINCIPAL_NAME_OVERFLOW
                    );
        }
    }


    /**
     * The type Import assign error column.
     */
    public static class ImportAssignErrorColumn {

        private ImportAssignErrorColumn() {

        }

        /**
         * The constant SAME_ACCOUNT_TE_STU.
         */
        public static final String SAME_ACCOUNT_TE_STU = "apis.aom.label.importassignerror.same_account_te_stu";
        /**
         * The constant NO_AUTH_ASSIGN_TE.
         */
        public static final String NO_AUTH_ASSIGN_TE = "apis.aom.label.importassignerror.no_auth_assign_te";
        /**
         * The constant NO_AUTH_ASSIGN_STU.
         */
        public static final String NO_AUTH_ASSIGN_STU = "apis.aom.label.importassignerror.no_auth_assign_stu";
        /**
         * The constant NOT_FOUND_TE.
         */
        public static final String NOT_FOUND_TE = "apis.aom.label.importassignerror.not_found_te";
        /**
         * The constant NOT_FOUND_STU.
         */
        public static final String NOT_FOUND_STU = "apis.aom.label.importassignerror.not_found_stu";
        /**
         * The constant NO_ACCOUNT_TE_STU.
         */
        public static final String NO_ACCOUNT_TE_STU = "apis.aom.label.importassignerror.no_account_te_stu";
        /**
         * The constant CANOT_ASSIGN_MORE.
         */
        public static final String CANOT_ASSIGN_MORE = "apis.aom.label.importassignerror.canot_assign_more";
        /**
         * The constant ASSIGN_OVER_MAX_QUANTITY.
         */
        public static final String ASSIGN_OVER_MAX_QUANTITY = "apis.aom.label.importassignerror.over_max_quantity";
        /**
         * The constant ASSIGN_PERIOD_ERROR.
         */
        public static final String ASSIGN_PERIOD_ERROR = "apis.aom.label.importassignerror.period.error";
        /**
         * The constant TEACHER_OVER_MAX_QUANTITY.
         */
        public static final String TEACHER_OVER_MAX_QUANTITY = "apis.aom.label.importassignerror.student.teacher.more.than.fifteen";
        /**
         * The constant THE_SUM_WEIGHT_NOT_100.
         */
        public static final String THE_SUM_WEIGHT_NOT_100 = "apis.aom.label.importassignerror.weight.sum.invalid";
        /**
         * The constant THE_SUM_WEIGHT_INVALID.
         */
        public static final String THE_SUM_WEIGHT_INVALID = "apis.aom.label.importassignerror.weight.invalid";
        /**
         * The constant THE_ACCOUNT_ALREADY_EXISTS.
         */
        public static final String THE_ACCOUNT_ALREADY_EXISTS = "apis.aom.label.importassignerror.account.already.exists";

        /**
         * Keys list.
         *
         * @return the list
         */
        public static List<String> KEYS() { // NOSONAR
            return Lists
                .newArrayList(SAME_ACCOUNT_TE_STU,
                    NO_AUTH_ASSIGN_TE,
                    NO_AUTH_ASSIGN_STU,
                    NOT_FOUND_TE,
                    NOT_FOUND_STU,
                    NO_ACCOUNT_TE_STU,
                    CANOT_ASSIGN_MORE,
                    ASSIGN_OVER_MAX_QUANTITY,
                    ASSIGN_PERIOD_ERROR,
                    TEACHER_OVER_MAX_QUANTITY,
                        THE_SUM_WEIGHT_NOT_100,
                        THE_SUM_WEIGHT_INVALID,
                        THE_ACCOUNT_ALREADY_EXISTS);
        }
    }

    /**
     * The type Assign column.
     */
    public static class AssignColumn {

        private AssignColumn() {

        }

        /**
         * The constant SAME_ACCOUNT_TE_STU.
         */
        public static final String SAME_ACCOUNT_TE_STU = "apis.aom.label.assign.same_account_te_stu";
        /**
         * The constant NO_AUTH_ASSIGN_TE.
         */
        public static final String NO_AUTH_ASSIGN_TE = "apis.aom.label.assign.no_auth_assign_te";
        /**
         * The constant NO_AUTH_ASSIGN_STU.
         */
        public static final String NO_AUTH_ASSIGN_STU = "apis.aom.label.assign.no_auth_assign_stu";
        /**
         * The constant FINISH.
         */
        public static final String FINISH = "apis.aom.label.assign.finish";
        /**
         * The constant UN_FINISH.
         */
        public static final String UN_FINISH = "apis.aom.label.assign.un_finish";
        /**
         * The constant OVER_MAX_NUM.
         */
        public static final String OVER_MAX_NUM = "apis.aom.ojt.student.over.max.quantity";
        /**
         * The constant TEACHER_MORE_THAN_TEN.
         */
        public static final String TEACHER_MORE_THAN_TEN = "apis.aom.ojt.teacher.more.than.fifteen";

        /**
         * Keys list.
         *
         * @return the list
         */
        public static List<String> KEYS() { // NOSONAR
            return Lists
                .newArrayList(SAME_ACCOUNT_TE_STU,
                    NO_AUTH_ASSIGN_TE,
                    NO_AUTH_ASSIGN_STU,
                    FINISH,
                    UN_FINISH,
                    OVER_MAX_NUM,
                    TEACHER_MORE_THAN_TEN);
        }
    }


    /**
     * The type Import facelive error column.
     */
    public static class ImportFaceliveErrorColumn {

        private ImportFaceliveErrorColumn() {

        }

        /**
         * The constant NO_USERNAME_FULLNAME_SCORE.
         */
        public static final String NO_USERNAME_FULLNAME_SCORE = "apis.aom.label.imporfaceliveerror.no_username_fullname_score";
        /**
         * The constant NOT_FOUND_USER.
         */
        public static final String NOT_FOUND_USER = "apis.aom.label.imporfaceliveerror.not_found_user";
        /**
         * The constant SCORE_FORMAT_ERROR.
         */
        public static final String SCORE_FORMAT_ERROR = "apis.aom.label.imporfaceliveerror.score_format_error";

        /**
         * Keys list.
         *
         * @return the list
         */
        public static List<String> keys() {
            return Lists.newArrayList(NO_USERNAME_FULLNAME_SCORE, NOT_FOUND_USER,SCORE_FORMAT_ERROR);
        }
    }

    /**
     * The type Sub task result column.
     */
    public static class SubTaskResultColumn {

        private SubTaskResultColumn() {

        }

        /**
         * The constant NEW.
         */
        public static final String NEW = "apis.aom.label.subtaskresult.new";
        /**
         * The constant ING.
         */
        public static final String ING = "apis.aom.label.subtaskresult.ing";
        /**
         * The constant END.
         */
        public static final String END = "apis.aom.label.subtaskresult.end";

        /**
         * The constant CREATE_BY_HAND.
         */
        public static final String CREATE_BY_HAND = "apis.aom.label.subtaskresult.create_by_hand";
        /**
         * The constant AUTO_CREATE.
         */
        public static final String AUTO_CREATE = "apis.aom.label.subtaskresult.auto_create";

        /**
         * Keys list.
         *
         * @return the list
         */
        public static List<String> KEYS() { // NOSONAR
            return Lists.newArrayList(NEW, ING, END,CREATE_BY_HAND,AUTO_CREATE);
        }
    }

    /**
     * The type Import archive error column.
     */
    public static class ImportArchiveErrorColumn {

        private ImportArchiveErrorColumn() {

        }

        /**
         * The constant PROJECT_NAME_EMPTY.
         */
        public static final String PROJECT_NAME_EMPTY = "apis.aom.label.importarchiveerror.project_name_empty";
        /**
         * The constant PROJECT_NAME_LENGTH_MAX.
         */
        public static final String PROJECT_NAME_LENGTH_MAX = "apis.aom.label.importarchiveerror.project_name_length_max";
        /**
         * The constant ARCHIVE_ID_EMPTY.
         */
        public static final String ARCHIVE_ID_EMPTY = "apis.aom.label.importarchiveerror.archive_id_empty";
        /**
         * The constant ARCHIVE_ID_LENGTH_MAX.
         */
        public static final String ARCHIVE_ID_LENGTH_MAX = "apis.aom.label.importarchiveerror.archive_id_length_max";
        /**
         * The constant ARCHIVE_ID_IS_STOCK.
         */
        public static final String ARCHIVE_ID_IS_STOCK = "apis.aom.label.importarchiveerror.archive_id_is_stock";
        /**
         * The constant ARCHIVE_ID_NOT_EXIST.
         */
        public static final String ARCHIVE_ID_NOT_EXIST = "apis.aom.label.importarchiveerror.archive_id_not_exist";
        /**
         * The constant ARCHIVE_ID_SAME.
         */
        public static final String ARCHIVE_ID_SAME = "apis.aom.label.importarchiveerror.archive_id_same";
        /**
         * The constant ARCHIVE_ID_ERROR.
         */
        public static final String ARCHIVE_ID_ERROR = "apis.aom.label.importarchiveerror.archive_id_error";
        /**
         * The constant TIMETYPE_ERROR.
         */
        public static final String TIMETYPE_ERROR = "apis.aom.label.importarchiveerror.timetype_error";
        /**
         * The constant STARTTIME_FORMAT.
         */
        public static final String STARTTIME_FORMAT = "apis.aom.label.importarchiveerror.starttime_format";
        /**
         * The constant ENDTIME_FORMAT.
         */
        public static final String ENDTIME_FORMAT = "apis.aom.label.importarchiveerror.endtime_format";
        /**
         * The constant CYCLE_EMPTY.
         */
        public static final String CYCLE_EMPTY = "apis.aom.label.importarchiveerror.cycle_empty";
        /**
         * The constant CYCLE_FORMAT.
         */
        public static final String CYCLE_FORMAT = "apis.aom.label.importarchiveerror.cycle_format";
        /**
         * The constant PRINCIPALS_EMPTY.
         */
        public static final String PRINCIPALS_EMPTY = "apis.aom.label.importarchiveerror.principals_empty";
        /**
         * The constant USERNAME_NOT_FOUND.
         */
        public static final String USERNAME_NOT_FOUND = "apis.aom.label.importarchiveerror.username_not_found";
        /**
         * The constant DESCRIPTION_LENGTH_MAX.
         */
        public static final String DESCRIPTION_LENGTH_MAX = "apis.aom.label.importarchiveerror.description_length_max";
        /**
         * The constant ARCHIVE_DATE_FORMAT.
         */
        public static final String ARCHIVE_DATE_FORMAT = "apis.aom.label.importarchiveerror.archive_date_format";
        /**
         * The constant ARCHIVE_DATE_EMPTY.
         */
        public static final String ARCHIVE_DATE_EMPTY = "apis.aom.label.importarchiveerror.archive_date_empty";
        /**
         * The constant SCENE_NAME_REPEAT.
         */
        public static final String SCENE_NAME_REPEAT = "apis.aom.label.importarchiveerror.scene_name_repeat";
        /**
         * The constant SCENE_NAME_NOT_FOUND.
         */
        public static final String SCENE_NAME_NOT_FOUND = "apis.aom.label.importarchiveerror.scene_name_not_found";
        /**
         * The constant PLAN_NAME_REPEAT.
         */
        public static final String PLAN_NAME_REPEAT = "apis.aom.label.importarchiveerror.plan_name_repeat";
        /**
         * The constant PLAN_NAME_NOT_FOUND.
         */
        public static final String PLAN_NAME_NOT_FOUND = "apis.aom.label.importarchiveerror.plan_name_not_found";
        /**
         * The constant SATISFACTION_SCORE_FORMAT.
         */
        public static final String SATISFACTION_SCORE_FORMAT = "apis.aom.label.importarchiveerror.satisfaction_score_format";
        /**
         * The constant TASK_NAME_EMPTY.
         */
        public static final String TASK_NAME_EMPTY = "apis.aom.label.importarchiveerror.task_name_empty";
        /**
         * The constant TASK_NAME_LENGTH_MAX.
         */
        public static final String TASK_NAME_LENGTH_MAX = "apis.aom.label.importarchiveerror.task_name_length_max";
        /**
         * The constant TASK_NO_EMPTY.
         */
        public static final String TASK_NO_EMPTY = "apis.aom.label.importarchiveerror.task_no_empty";
        /**
         * The constant TASK_NO_LENGTH_MAX.
         */
        public static final String TASK_NO_LENGTH_MAX = "apis.aom.label.importarchiveerror.task_no_length_max";
        /**
         * The constant TASK_NO_SAME.
         */
        public static final String TASK_NO_SAME = "apis.aom.label.importarchiveerror.task_no_same";
        /**
         * The constant TASK_NO_ERROR.
         */
        public static final String TASK_NO_ERROR = "apis.aom.label.importarchiveerror.task_no_error";
        /**
         * The constant TASK_TYPE_ERROR.
         */
        public static final String TASK_TYPE_ERROR = "apis.aom.label.importarchiveerror.task_type_error";
        /**
         * The constant TASK_REQUIRED_EMPTY.
         */
        public static final String TASK_REQUIRED_EMPTY = "apis.aom.label.importarchiveerror.task_required_empty";
        /**
         * The constant TASK_SCORE_FORMAT.
         */
        public static final String TASK_SCORE_FORMAT = "apis.aom.label.importarchiveerror.task_score_format";
        /**
         * The constant TASK_SCORE_FORMAT_POINT.
         */
        public static final String TASK_SCORE_FORMAT_POINT = "apis.aom.label.importarchiveerror.task_score_format_point";
        /**
         * The constant TASK_POINT_FORMAT.
         */
        public static final String TASK_POINT_FORMAT = "apis.aom.label.importarchiveerror.task_point_format";
        /**
         * The constant TASK_DURATION_FORMAT.
         */
        public static final String TASK_DURATION_FORMAT = "apis.aom.label.importarchiveerror.task_duration_format";
        /**
         * The constant TASK_PASSSCORE_FORMAT.
         */
        public static final String TASK_PASSSCORE_FORMAT = "apis.aom.label.importarchiveerror.task_passscore_format";
        /**
         * The constant MEMBER_USERNAME_EMPTY.
         */
        public static final String MEMBER_USERNAME_EMPTY = "apis.aom.label.importarchiveerror.member_username_empty";
        /**
         * The constant MEMBER_USERNAME_SAME.
         */
        public static final String MEMBER_USERNAME_SAME = "apis.aom.label.importarchiveerror.member_username_same";
        /**
         * The constant MEMBER_USERNAME_ERROR.
         */
        public static final String MEMBER_USERNAME_ERROR = "apis.aom.label.importarchiveerror.member_username_error";
        /**
         * The constant MEMBER_FULLNAME_ERROR.
         */
        public static final String MEMBER_FULLNAME_ERROR = "apis.aom.label.importarchiveerror.member_fullname_error";
        /**
         * The constant MEMBER_TASK_NOT_FOUND.
         */
        public static final String MEMBER_TASK_NOT_FOUND = "apis.aom.label.importarchiveerror.member_task_not_found";
        /**
         * The constant MEMBER_TASK_NO_NOT_FOUND.
         */
        public static final String MEMBER_TASK_NO_NOT_FOUND = "apis.aom.label.importarchiveerror.member_task_no_not_found";
        /**
         * The constant MEMBER_STATUS_EMPTY.
         */
        public static final String MEMBER_STATUS_EMPTY = "apis.aom.label.importarchiveerror.member_status_empty";
        /**
         * The constant MEMBER_SCORE_FORMAT.
         */
        public static final String MEMBER_SCORE_FORMAT = "apis.aom.label.importarchiveerror.member_score_format";
        /**
         * The constant MEMBER_RESULT_NOT_FOUND.
         */
        public static final String MEMBER_RESULT_NOT_FOUND = "apis.aom.label.importarchiveerror.member_result_not_found";
        /**
         * The constant MEMBER_FIRST_STUDY_TIME_FORMAT.
         */
        public static final String MEMBER_FIRST_STUDY_TIME_FORMAT = "apis.aom.label.importarchiveerror.member_first_study_time_format";
        /**
         * The constant MEMBER_LAST_STUDY_TIME_FORMAT.
         */
        public static final String MEMBER_LAST_STUDY_TIME_FORMAT = "apis.aom.label.importarchiveerror.member_last_study_time_format";
        /**
         * The constant MEMBER_PROJECT_SCORE_ERROR.
         */
        public static final String MEMBER_PROJECT_SCORE_ERROR = "apis.aom.label.importarchiveerror.member_project_score_error";
        /**
         * The constant MEMBER_PASSED_ERROR.
         */
        public static final String MEMBER_PASSED_ERROR = "apis.aom.label.importarchiveerror.member_passed_error";


        /**
         * Keys list.
         *
         * @return the list
         */
        public static List<String> KEYS() { // NOSONAR
            return Lists
                .newArrayList(PROJECT_NAME_EMPTY, PROJECT_NAME_LENGTH_MAX, ARCHIVE_ID_EMPTY, ARCHIVE_ID_LENGTH_MAX,
                    ARCHIVE_ID_IS_STOCK, ARCHIVE_ID_NOT_EXIST, ARCHIVE_ID_SAME, ARCHIVE_ID_ERROR, TIMETYPE_ERROR,
                    STARTTIME_FORMAT, ENDTIME_FORMAT, CYCLE_EMPTY, CYCLE_FORMAT, PRINCIPALS_EMPTY, USERNAME_NOT_FOUND,
                    DESCRIPTION_LENGTH_MAX, ARCHIVE_DATE_FORMAT, ARCHIVE_DATE_EMPTY, SCENE_NAME_REPEAT,
                    SCENE_NAME_NOT_FOUND, PLAN_NAME_REPEAT, PLAN_NAME_NOT_FOUND, SATISFACTION_SCORE_FORMAT, TASK_NAME_EMPTY,
                    TASK_NAME_LENGTH_MAX, TASK_NO_EMPTY, TASK_NO_LENGTH_MAX, TASK_NO_SAME, TASK_NO_ERROR,
                    TASK_TYPE_ERROR, TASK_REQUIRED_EMPTY, TASK_SCORE_FORMAT, TASK_SCORE_FORMAT_POINT,TASK_POINT_FORMAT,
                    TASK_DURATION_FORMAT, TASK_PASSSCORE_FORMAT, MEMBER_USERNAME_EMPTY, MEMBER_USERNAME_SAME,
                    MEMBER_USERNAME_ERROR, MEMBER_FULLNAME_ERROR, MEMBER_TASK_NOT_FOUND, MEMBER_TASK_NO_NOT_FOUND,
                    MEMBER_STATUS_EMPTY, MEMBER_SCORE_FORMAT, MEMBER_RESULT_NOT_FOUND, MEMBER_FIRST_STUDY_TIME_FORMAT,
                    MEMBER_LAST_STUDY_TIME_FORMAT, MEMBER_PROJECT_SCORE_ERROR, MEMBER_PASSED_ERROR);
        }
    }

    /**
     * The type Import archive time type column.
     */
    public static class ImportArchiveTimeTypeColumn {
        private ImportArchiveTimeTypeColumn() {

        }

        /**
         * 项目模式
         */
        public static final String START_END = "apis.aom.label.importarchive.start_end";
        /**
         * The constant CYCLE.
         */
        public static final String CYCLE = "apis.aom.label.importarchive.cycle";

        /**
         * Keys list.
         *
         * @return the list
         */
        public static List<String> KEYS() { // NOSONAR
            return Lists
                .newArrayList(START_END, CYCLE);
        }
    }

    /**
     * The type Import archive task type column.
     */
    public static class ImportArchiveTaskTypeColumn {
        private ImportArchiveTaskTypeColumn() {

        }

        /**
         * 任务类型
         */
        public static final String SIGN = "apis.aom.label.importarchive.sign";
        /**
         * The constant OFFLINE.
         */
        public static final String OFFLINE = "apis.aom.label.importarchive.offline";
        /**
         * The constant EXAM.
         */
        public static final String EXAM = "apis.aom.label.importarchive.exam";
        /**
         * The constant HOMEWORK.
         */
        public static final String HOMEWORK = "apis.aom.label.importarchive.homework";
        /**
         * The constant ACTIVITY.
         */
        public static final String ACTIVITY = "apis.aom.label.importarchive.activity";
        /**
         * The constant COURSE.
         */
        public static final String COURSE = "apis.aom.label.importarchive.course";
        /**
         * The constant OTHER.
         */
        public static final String OTHER = "apis.aom.label.importarchive.other";
        /**
         * The constant LIVE.
         */
        public static final String LIVE = "apis.aom.label.importarchive.live";
        /**
         * The constant EXERCISE.
         */
        public static final String EXERCISE = "apis.aom.label.importarchive.exercise";
        /**
         * The constant SURVEY.
         */
        public static final String SURVEY = "apis.aom.label.importarchive.survey";
        /**
         * The constant APPRAISAL.
         */
        public static final String APPRAISAL = "apis.aom.label.importarchive.appraisal";
        /**
         * The constant SPARRING.
         */
        public static final String SPARRING = "apis.aom.label.importarchive.sparring";
        /**
         * The constant SPARRING.
         */
        public static final String DISCUSS = "apis.aom.label.importarchive.discuss";

        /**
         * Keys list.
         *
         * @return the list
         */
        public static List<String> KEYS() { // NOSONAR
            return Lists
                .newArrayList(SIGN, OFFLINE, EXAM, HOMEWORK, ACTIVITY, COURSE, OTHER, LIVE, EXERCISE, SURVEY, APPRAISAL, SPARRING, DISCUSS);
        }
    }

    /**
     * The type Import archive task required column.
     */
    public static class ImportArchiveTaskRequiredColumn {
        private ImportArchiveTaskRequiredColumn() {

        }

        /**
         * 任务是否必修
         */
        public static final String NOT_REQUIRED = "apis.aom.label.importarchive.not_required";
        /**
         * The constant REQUIRED.
         */
        public static final String REQUIRED = "apis.aom.label.importarchive.required";

        /**
         * Keys list.
         *
         * @return the list
         */
        public static List<String> KEYS() { // NOSONAR
            return Lists
                .newArrayList(NOT_REQUIRED, REQUIRED);
        }
    }

    /**
     * The type Import archive result status column.
     */
    public static class ImportArchiveResultStatusColumn {
        private ImportArchiveResultStatusColumn() {

        }

        /**
         * 任务是否完成
         */
        public static final String UN_FINISH = "apis.aom.label.importarchive.un_finish";
        /**
         * The constant FINISH.
         */
        public static final String FINISH = "apis.aom.label.importarchive.finish";

        /**
         * Keys list.
         *
         * @return the list
         */
        public static List<String> KEYS() { // NOSONAR
            return Lists
                .newArrayList(UN_FINISH, FINISH);
        }
    }

    /**
     * The type Import archive member passed column.
     */
    public static class ImportArchiveMemberPassedColumn {
        private ImportArchiveMemberPassedColumn() {

        }

        /**
         * 任务是否完成
         */
        public static final String UN_PASSED = "apis.aom.label.importarchive.un_passed";
        /**
         * The constant PASSED.
         */
        public static final String PASSED = "apis.aom.label.importarchive.passed";

        /**
         * Keys list.
         *
         * @return the list
         */
        public static List<String> KEYS() { // NOSONAR
            return Lists
                .newArrayList(UN_PASSED, PASSED);
        }
    }

    /**
     * The type Cert conditions.
     */
    public static class CertConditions {
        private CertConditions() {}
        /**
         * The constant FINISH_REQUIRED.
         */
        public static final String FINISH_REQUIRED = "api.o2o.label.cert.conditions.finish_required";
        /**
         * The constant EXAM_PASSED.
         */
        public static final String EXAM_PASSED = "api.o2o.label.cert.conditions.exam_passed";
        /**
         * The constant EXAM_EXCELLENT.
         */
        public static final String EXAM_EXCELLENT = "api.o2o.label.cert.conditions.exam_excellent";
        /**
         * The constant TOTAL_SCORE.
         */
        public static final String TOTAL_SCORE = "api.o2o.label.cert.conditions.total_score";
        /**
         * The constant PASS_ALL.
         */
        public static final String PASS_ALL = "api.o2o.label.cert.conditions.pass_all";
        /**
         * The constant PERIOD_FINISH.
         */
        public static final String PERIOD_FINISH = "api.o2o.label.cert.conditions.period_finish";
        /**
         * The constant HANDLE.
         */
        public static final String HANDLE = "api.o2o.label.cert.conditions.handle";
        /**
         * The constant PERIOD_GRADUATE.
         */
        public static final String PERIOD_GRADUATE = "api.o2o.label.cert.conditions.graduate";
        /**
         * The constant PROJECT_GRADUATE.
         */
        public static final String PROJECT_GRADUATE = "api.o2o.label.cert.conditions.project.graduate";
        /**
         * The constant TASK_GRADUATE.
         */
        public static final String TASK_GRADUATE = "api.o2o.label.cert.conditions.task.graduate";
        /**
         * The constant PROJECT_FINISH.
         */
        public static final String PROJECT_FINISH = "api.o2o.label.cert.conditions.project.finish";
        /**
         * The constant TASK_GROUP_FINISH.
         */
        public static final String TASK_GROUP_FINISH = "api.o2o.label.cert.conditions.task.group.finish";
        /**
         * The constant PROJECT_INTEGRATE_FINISH.
         */
        public static final String PROJECT_INTEGRATE_FINISH = "api.o2o.label.cert.conditions.project.integrate.finish";
        /**
         * The constant PROJECT_SUB_FINISH.
         */
        public static final String PROJECT_SUB_FINISH = "api.o2o.label.cert.conditions.project.sub.finish";

        /**
         * Keys list.
         *
         * @return the list
         */
        public static List<String> KEYS() { // NOSONAR
            return Lists
                .newArrayList(FINISH_REQUIRED, EXAM_PASSED, EXAM_EXCELLENT, TOTAL_SCORE, PASS_ALL, PERIOD_FINISH,
                    HANDLE, PERIOD_GRADUATE, PROJECT_GRADUATE, TASK_GRADUATE, PROJECT_FINISH, TASK_GROUP_FINISH,
                    PROJECT_INTEGRATE_FINISH, PROJECT_SUB_FINISH);
        }
    }

    /**
     * 导入积分错误提示语
     */
    public static class ImportScoreErrorColumn {

        private ImportScoreErrorColumn() {

        }

        /**
         * The constant USER_NOT_EXIST.
         */
        public static final String USER_NOT_EXIST = "apis.aom.label.importscoreerror.user_not_exist";
        /**
         * The constant SCORE_NOT_BLANK.
         */
        public static final String SCORE_NOT_BLANK = "apis.aom.label.importscoreerror.score_not_blank";
        /**
         * The constant SCORE_TYPE_NOT_BLANK.
         */
        public static final String SCORE_TYPE_NOT_BLANK = "apis.aom.label.importscoreerror.score_type_not_blank";
        /**
         * The constant SCORE_FORMAT.
         */
        public static final String SCORE_FORMAT = "apis.aom.label.importscoreerror.score_format";
        /**
         * The constant SAME_DATA.
         */
        public static final String SAME_DATA = "apis.aom.label.importscoreerror.same_data";


        /**
         * Keys list.
         *
         * @return the list
         */
        public static List<String> KEYS() { // NOSONAR
            return Lists
                .newArrayList(USER_NOT_EXIST, SCORE_NOT_BLANK, SCORE_TYPE_NOT_BLANK, SCORE_FORMAT, SAME_DATA);
        }
    }

    /**
     * 部门预算导入模板
     */
    public static class ImportBudgetTemplateColumn {
        private ImportBudgetTemplateColumn() {

        }

        /**
         * The constant FUNCTIONNAME.
         */
        public static final String FUNCTIONNAME = "apis.aom.export.functionName.importbudgetstemp";
        /**
         * The constant MODULENAME.
         */
        public static final String MODULENAME = "apis.budgettemplate.modulename";
        /**
         * The constant MODE0_NOTE.
         */
        public static final String MODE0_NOTE = "apis.budgettemplate.mode0note";
        /**
         * The constant MODE1_NOTE.
         */
        public static final String MODE1_NOTE = "apis.budgettemplate.mode1note";
        /**
         * The constant HEADER1.
         */
        public static final String HEADER1 = "apis.budgettemplate.header1";
        /**
         * The constant HEADER2.
         */
        public static final String HEADER2 = "apis.budgettemplate.header2";
        /**
         * The constant HEADER3.
         */
        public static final String HEADER3 = "apis.budgettemplate.header3";
        /**
         * The constant HEADER4.
         */
        public static final String HEADER4 = "apis.budgettemplate.header4";

        /**
         * Keys list.
         *
         * @return the list
         */
        public static List<String> keys() {
            return Collections.unmodifiableList(
                Lists.newArrayList(FUNCTIONNAME, MODULENAME, MODE0_NOTE, MODE1_NOTE, HEADER1, HEADER2, HEADER3,
                    HEADER4));
        }
    }


    /**
     * 导入部门预算错误提示语
     */
    public static class ImportBudgetErrorColumn {
        private ImportBudgetErrorColumn() {

        }

        /**
         * The constant FILENAMEPREFIX.
         */
        public static final String FILENAMEPREFIX = "apis.importbudgetserror.filenameprefix";

        /**
         * The constant TITLE.
         */
        public static final String TITLE = "apis.importbudgetserror.title";
        /**
         * The constant HEADER1.
         */
        public static final String HEADER1 = "apis.importbudgetserror.header1";
        /**
         * The constant HEADER2.
         */
        public static final String HEADER2 = "apis.importbudgetserror.header2";
        /**
         * The constant HEADER3.
         */
        public static final String HEADER3 = "apis.importbudgetserror.header3";
        /**
         * The constant HEADER4.
         */
        public static final String HEADER4 = "apis.importbudgetserror.header4";
        /**
         * The constant HEADER5.
         */
        public static final String HEADER5 = "apis.importbudgetserror.header5";
        /**
         * The constant BUDGETNAME_REQUIRED.
         */
        public static final String BUDGETNAME_REQUIRED = "apis.importbudgetserror.info.budgetName.required";
        /**
         * The constant BUDGETNAME_MAXLENGTH.
         */
        public static final String BUDGETNAME_MAXLENGTH = "apis.importbudgetserror.info.budgetName.maxlength";
        /**
         * The constant DEPTNAME_INVALID.
         */
        public static final String DEPTNAME_INVALID = "apis.importbudgetserror.info.deptName.invalid";
        /**
         * The constant DEPTNAME_REQUIRED.
         */
        public static final String DEPTNAME_REQUIRED = "apis.importbudgetserror.info.deptName.required";
        /**
         * The constant DEPTNAME_DUPLICATED.
         */
        public static final String DEPTNAME_DUPLICATED = "apis.importbudgetserror.info.deptName.duplicated";
        /**
         * The constant DEPTNAME_EXISTED.
         */
        public static final String DEPTNAME_EXISTED = "apis.importbudgetserror.info.deptName.existed";
        /**
         * The constant OWNERUSERNAME_REQUIRED.
         */
        public static final String OWNERUSERNAME_REQUIRED = "apis.importbudgetserror.info.ownerUserName.required";
        /**
         * The constant OWNERUSERNAME_INVALID.
         */
        public static final String OWNERUSERNAME_INVALID = "apis.importbudgetserror.info.ownerUserName.invalid";
        /**
         * The constant OWNERUSERNAME_MAXCOUNT.
         */
        public static final String OWNERUSERNAME_MAXCOUNT = "apis.importbudgetserror.info.ownerUserName.maxcount";
        /**
         * The constant BUDGETAMOUNT_REQUIRED.
         */
        public static final String BUDGETAMOUNT_REQUIRED = "apis.importbudgetserror.info.budgetAmount.required";
        /**
         * The constant BUDGETAMOUNT_EXCEED.
         */
        public static final String BUDGETAMOUNT_EXCEED = "apis.importbudgetserror.info.budgetAmount.exceed";
        /**
         * The constant BUDGETAMOUNT_FORMAT.
         */
        public static final String BUDGETAMOUNT_FORMAT = "apis.importbudgetserror.info.budgetAmount.format";
        /**
         * The constant BUDGETTYPE_REQUIRED.
         */
        public static final String BUDGETTYPE_REQUIRED = "apis.importbudgetserror.info.budgetType.required";
        /**
         * The constant BUDGETTYPE_INVALID.
         */
        public static final String BUDGETTYPE_INVALID = "apis.importbudgetserror.info.budgetType.invalid";
        /**
         * The constant EXTFIELD_REQUIRED.
         */
        public static final String EXTFIELD_REQUIRED = "apis.importbudgetserror.info.extField.required";
        /**
         * The constant EXTFIELD_MAXLENGTH.
         */
        public static final String EXTFIELD_MAXLENGTH = "apis.importbudgetserror.info.extField.maxlength";
        /**
         * The constant EXTFIELD_NUMBERFORMAT.
         */
        public static final String EXTFIELD_NUMBERFORMAT = "apis.importbudgetserror.info.extField.numberformat";
        /**
         * The constant EXTFIELD_OPTIONSFORMAT.
         */
        public static final String EXTFIELD_OPTIONSFORMAT = "apis.importbudgetserror.info.extField.optionsformat";
        /**
         * The constant EXTFIELD_DATEFORMAT.
         */
        public static final String EXTFIELD_DATEFORMAT = "apis.importbudgetserror.info.extField.dateformat";

        /**
         * Keys list.
         *
         * @return the list
         */
        public static List<String> keys() {
            return Collections.unmodifiableList(
                Lists.newArrayList(FILENAMEPREFIX, TITLE, HEADER1, HEADER2, HEADER3, HEADER4, HEADER5,
                    BUDGETNAME_REQUIRED, BUDGETNAME_MAXLENGTH, DEPTNAME_INVALID, DEPTNAME_REQUIRED, DEPTNAME_DUPLICATED,
                    DEPTNAME_EXISTED, OWNERUSERNAME_REQUIRED, OWNERUSERNAME_INVALID, OWNERUSERNAME_MAXCOUNT,
                    BUDGETAMOUNT_REQUIRED, BUDGETAMOUNT_EXCEED, BUDGETAMOUNT_FORMAT, BUDGETTYPE_REQUIRED,
                    BUDGETTYPE_INVALID, EXTFIELD_REQUIRED, EXTFIELD_MAXLENGTH, EXTFIELD_NUMBERFORMAT,
                    EXTFIELD_OPTIONSFORMAT, EXTFIELD_DATEFORMAT));
        }
    }

    /**
     * 导入学分错误提示语
     */
    public static class ImportCreditErrorColumn {

        private ImportCreditErrorColumn() {
        }

        /**
         * The constant CREDIT_USER_NOT_EXIST.
         */
        public static final String CREDIT_USER_NOT_EXIST = "apis.aom.label.importscoreerror.user_not_exist";
        /**
         * The constant CREDIT_NOT_BLANK.
         */
        public static final String CREDIT_NOT_BLANK = "apis.aom.label.importscoreerror.credit_not_blank";
        /**
         * The constant CREDIT_TYPE_NOT_BLANK.
         */
        public static final String CREDIT_TYPE_NOT_BLANK = "apis.aom.label.importscoreerror.credit_type_not_blank";
        /**
         * The constant CREDIT_OUT_RANGE.
         */
        public static final String CREDIT_OUT_RANGE = "apis.aom.label.importscoreerror.credit_out_range";
        /**
         * The constant CREDIT_FORMAT.
         */
        public static final String CREDIT_FORMAT = "apis.aom.label.importscoreerror.credit_format";
        /**
         * The constant CREDIT_SAME_DATA.
         */
        public static final String CREDIT_SAME_DATA = "apis.aom.label.importscoreerror.creditsame_data";
        /**
         * The constant CREDIT_OUT_RANGE_DESCRIPTION.
         */
        public static final String CREDIT_OUT_RANGE_DESCRIPTION = "apis.aom.label.importscoreerror.credit_out_range_description";


        /**
         * Keys list.
         *
         * @return the list
         */
        public static List<String> KEYS() { // NOSONAR
            return Lists
                .newArrayList(CREDIT_USER_NOT_EXIST, CREDIT_NOT_BLANK, CREDIT_TYPE_NOT_BLANK,
                    CREDIT_FORMAT, CREDIT_SAME_DATA,CREDIT_OUT_RANGE,CREDIT_OUT_RANGE_DESCRIPTION);
        }
    }

    /**
     * The type Export eval passed column.
     */
    public static class ExportEvalPassedColumn {
        private ExportEvalPassedColumn() {

        }

        /**
         * The constant DETAIL_UN_PASS.
         */
        public static final String DETAIL_UN_PASS = "un_pass";
        /**
         * The constant DETAIL_PASS.
         */
        public static final String DETAIL_PASS = "pass";
        /**
         * The constant UNEVAL.
         */
        public static final String UNEVAL = "uneval";
        /**
         * The constant EVALED.
         */
        public static final String EVALED = "evaled";
    }

    /**
     * The type Default score type enum.
     */
    public static class DefaultScoreTypeEnum {

        private DefaultScoreTypeEnum() {

        }

        /**
         * The constant STUDY_INTEGRAL.
         */
        public static final String STUDY_INTEGRAL = "apis.aom.label.jf_study_integral";
        /**
         * The constant SHARE_INTEGRAL.
         */
        public static final String SHARE_INTEGRAL = "apis.aom.label.jf_share_integral";
        /**
         * The constant STUDY_CREDIT.
         */
        public static final String STUDY_CREDIT = "apis.aom.label.jf_study_credit";
        /**
         * The constant SHARE_CREDIT.
         */
        public static final String SHARE_CREDIT = "apis.aom.label.jf_share_credit";

        /**
         * Keys list.
         *
         * @return the list
         */
        public static List<String> KEYS() { // NOSONAR
            return Lists.newArrayList(STUDY_INTEGRAL, SHARE_INTEGRAL);
        }
    }

    /**
     * 学分积分来源
     */
    public static class ScoreSourceColumn {

        private ScoreSourceColumn(){
        }

        /**
         * The constant OPTIONAL_TASK_DONE.
         */
        public static final String OPTIONAL_TASK_DONE = "apis.aom.score.task.done";
        /**
         * The constant COMPULSORY_TASK_DONE.
         */
        public static final String COMPULSORY_TASK_DONE = "apis.aom.score.task.complete";
        /**
         * The constant COMPLETION_PHASE.
         */
        public static final String COMPLETION_PHASE = "apis.aom.score.period.complete";
        /**
         * The constant COMPLETE_PROJECT.
         */
        public static final String COMPLETE_PROJECT = "apis.aom.score.project.complete";
        /**
         * The constant GROUP_POINTS.
         */
        public static final String GROUP_POINTS = "apis.aom.score.group.points";
        /**
         * The constant EXCELLENT_STUDENT_TYPE.
         */
        public static final String EXCELLENT_STUDENT_TYPE = "apis.aom.score.excellent.student";
        /**
         * The constant EXCELLENT_STUDENT_SOURCE.
         */
        public static final String EXCELLENT_STUDENT_SOURCE = "apis.aom.score.excellent.student.source";
        /**
         * The constant GROUP_JOINT_TYPE.
         */
        public static final String GROUP_JOINT_TYPE = "apis.aom.score.group.joint.hw";
        /**
         * The constant SOURCE_CUSTOM_INTEGRAL.
         */
        public static final String SOURCE_CUSTOM_INTEGRAL = "apis.aom.score.source.custom";
        /**
         * The constant OJT_EVAL_TYPE.
         */
        public static final String OJT_EVAL_TYPE = "apis.aom.score.source.eval";
        /**
         * The constant OJT_EVAL_PASSED.
         */
        public static final String OJT_EVAL_PASSED = "apis.aom.score.eval.passed";
        /**
         * The constant OJT_EVAL_NO_PASSED.
         */
        public static final String OJT_EVAL_NO_PASSED = "apis.aom.score.eval.not.pass";
        /**
         * The constant OJT_EVAL_QUALIFIED.
         */
        public static final String OJT_EVAL_QUALIFIED = "apis.aom.score.eval.qualified";
        /**
         * The constant OJT_EVAL_UNQUALIFIED.
         */
        public static final String OJT_EVAL_UNQUALIFIED = "apis.aom.score.eval.unqualified";
        /**
         * The constant TYPE_DISCUSS_PRAISED.
         */
        public static final String TYPE_DISCUSS_PRAISED = "apis.aom.score.type.discuss.praised";
        /**
         * The constant TYPE_DISCUSS_COMMENT.
         */
        public static final String TYPE_DISCUSS_COMMENT = "apis.aom.score.type.discuss.comment";
        /**
         * 精华作业类型
         */
        public static final String REMARKED_ESSENTIAL_HW = "apis.aom.score.type.remarked.essential.hw";

        /**
         * Keys list.
         *
         * @return the list
         */
        public static List<String> KEYS() { // NOSONAR
            return Lists.newArrayList(OPTIONAL_TASK_DONE, COMPULSORY_TASK_DONE, COMPLETION_PHASE, COMPLETE_PROJECT,
                GROUP_POINTS, EXCELLENT_STUDENT_TYPE, EXCELLENT_STUDENT_SOURCE, SOURCE_CUSTOM_INTEGRAL,
                OJT_EVAL_TYPE, OJT_EVAL_PASSED, OJT_EVAL_NO_PASSED, OJT_EVAL_QUALIFIED, OJT_EVAL_UNQUALIFIED,
                GROUP_JOINT_TYPE, TYPE_DISCUSS_PRAISED, TYPE_DISCUSS_COMMENT, REMARKED_ESSENTIAL_HW);
        }

    }


    public static class RewardTypeEnumI18N {
        private RewardTypeEnumI18N(){

        }

        /**
         * 完成
         */
        public static final String REWARD_TYPE_COMPLETED = "apis.aom.reward.type.completed";

        /**
         * 通过
         */
        public static final String REWARD_TYPE_PASSED = "apis.aom.reward.type.passed";

        /**
         * 优秀
         */
        public static final String REWARD_TYPE_EXCELLENT = "apis.aom.reward.type.excellent";

        /**
         * 评论
         */
        public static final String REWARD_TYPE_COMMENTED = "apis.aom.reward.type.commented";

        /**
         * 点赞
         */
        public static final String REWARD_TYPE_LIKED = "apis.aom.reward.type.liked";

        /**
         * 扣减
         */
        public static final String REWARD_TYPE_REDUCED = "apis.aom.reward.type.reduced";

        /**
         * 精华
         */
        public static final String REWARD_TYPE_FEATURED = "apis.aom.reward.type.featured";
        /**
         * 评价合格
         */
        public static final String REWARD_EVAL_PASSED = "apis.aom.reward.type.eval.passed";
        /**
         * 评价不合格
         */
        public static final String REWARD_EVAL_UNPASSED = "apis.aom.reward.type.eval.unpassed";
    }

    /**
     * 学员端任务大纲多语言
     */
    public static final String RECENT_STUDY = "apis.aom.task.study.recent.study";
    /**
     * The constant NO_LOCK_TASK.
     */
    public static final String NO_LOCK_TASK = "apis.aom.task.study.no.lock.task";
    /**
     * The constant START_LEARNING.
     */
    public static final String START_LEARNING = "apis.aom.task.study.start.learning";
    /**
     * The constant IS_UNLOCK.
     */
    public static final String IS_UNLOCK = "apis.aom.task.study.is.unlock";
    /**
     * The constant START.
     */
    public static final String START = "apis.aom.task.study.start";
    /**
     * The constant TODAY.
     */
    public static final String TODAY = "apis.aom.task.study.today";
    /**
     * The constant SIGN.
     */
    public static final String SIGN  = "apis.aom.task.study.sign";
    /**
     * The constant ERROR_MSG_SIGN_H5.
     */
    public static final String ERROR_MSG_SIGN_H5 = "apis.aom.task.study.error.msg.sign.h5";
    /**
     * The constant ERROR_MSG_SIGN_END.
     */
    public static final String ERROR_MSG_SIGN_END = "apis.aom.task.study.error.msg.sign.end";
    /**
     * The constant SIGN_DELAY_TEXT.
     */
    public static final String SIGN_DELAY_TEXT = "apis.aom.task.study.sign.delay";
    /**
     * The constant LEAVE_TEXT.
     */
    public static final String LEAVE_TEXT = "apis.aom.task.study.leave";
    /**
     * The constant HAVE.
     */
    public static final String HAVE = "apis.aom.task.study.have";
    /**
     * The constant SIGN_OUT.
     */
    public static final String SIGN_OUT = "apis.aom.task.study.signout";
    /**
     * The constant EARLY_LEAVE.
     */
    public static final String EARLY_LEAVE = "apis.aom.task.study.early.leave";
    /**
     * The constant SUBMIT.
     */
    public static final String SUBMIT= "apis.aom.task.study.submit";
    /**
     * The constant STOP.
     */
    public static final String STOP = "apis.aom.task.study.stop";
    /**
     * The constant OVERDUE.
     */
    public static final String OVERDUE = "apis.aom.task.study.overdue";
    /**
     * The constant ING_ING.
     */
    public static final String ING_ING = "apis.aom.task.study.ing.ing";
    /**
     * The constant ING.
     */
    public static final String ING = "apis.aom.task.study.ing";
    /**
     * The constant END.
     */
    public static final String END = "apis.aom.task.study.end";
    /**
     * The constant REJECTION.
     */
    public static final String REJECTION = "apis.aom.task.study.rejection";
    /**
     * The constant SCORE.
     */
    public static final String SCORE = "apis.aom.task.study.score";
    /**
     * The constant PASSED.
     */
    public static final String PASSED = "apis.aom.task.study.pass";
    /**
     * The constant UNPASSED.
     */
    public static final String UNPASSED = "apis.aom.task.study.unpass";
    /** 待批阅 */
    public static final String UNMARKED= "apis.aom.task.study.unmarked";
    /** 已批阅 */
    public static final String MARKED = "apis.aom.task.study.marked";
    /**
     * The constant UNSIGN_IN.
     */
    public static final String UNSIGN_IN = "apis.aom.task.sign.in.end";
    /**
     * The constant NEW_SIGN_QR_CODE.
     */
    public static final String NEW_SIGN_QR_CODE = "apis.aom.task.new.sign.qr.code";
    /**
     * The constant UNSIGN_OUT.
     */
    public static final String UNSIGN_OUT = "apis.aom.task.sign.out.end";
    /**
     * The constant SIGN_IN_H5.
     */
    public static final String SIGN_IN_H5 = "apis.aom.task.sign.in.position";
    /**
     * The constant SIGN_OUT_H5.
     */
    public static final String SIGN_OUT_H5 = "apis.aom.task.sign.out.position";
    /**
     * The constant SIGN_IN_OUT_H5.
     */
    public static final String SIGN_IN_OUT_H5 = "apis.aom.task.sign.in.out.position";
    /**
     * The constant SIGN_IN_DONE.
     */
    public static final String SIGN_IN_DONE = "apis.aom.task.sign.have.done";
    /**
     * The constant HAVE_SIGN_DELAY_TEXT.
     */
    public static final String HAVE_SIGN_DELAY_TEXT = "apis.aom.task.sign.in.delay";
    /**
     * The constant HAVE_LEAVE_TEXT.
     */
    public static final String HAVE_LEAVE_TEXT = "apis.aom.task.study.have.leave";
    /**
     * The constant LEAVE_EARLY.
     */
    public static final String  LEAVE_EARLY= "apis.aom.task.sign.leave.early";
    /**
     * The constant HAVE_SIGN_OUT.
     */
    public static final String  HAVE_SIGN_OUT= "apis.aom.task.sign.out.have.done";
    /**
     * The constant PERIOD_NOT_FIND.
     */
    public static final String  PERIOD_NOT_FIND= "apis.aom.task.period.have.not.find";
    /**
     * The constant PERIOD_LOCKED.
     */
    public static final String  PERIOD_LOCKED= "apis.aom.task.period.locked";
    /**
     * The constant ERROR_MSG_ISNT_OJT_STUDENT.
     */
    public static final String  ERROR_MSG_ISNT_OJT_STUDENT= "apis.aom.ojt.student.period.error";
    /**
     * The constant ERROR_MSG_NOT_STUDENT.
     */
    public static final String  ERROR_MSG_NOT_STUDENT= "apis.aom.member.not.in.activity";
    /**
     * The constant ERROR_MSG_NOT_START_TIME.
     */
    public static final String  ERROR_MSG_NOT_START_TIME= "apis.aom.activity.not.start.time";
    /**
     * The constant ERROR_MSG_PREV_TASK_NOT_COMPLETED.
     */
    public static final String  ERROR_MSG_PREV_TASK_NOT_COMPLETED= "apis.aom.prev.task.not.completed";
    /**
     * The constant ERROR_MSG_PROJECT_NOT_RELEASED.
     */
    public static final String  ERROR_MSG_PROJECT_NOT_RELEASED= "apis.aom.activity.not.released";
    /**
     * The constant ERROR_MSG_PROJECT_ARCHIVED.
     */
    public static final String  ERROR_MSG_PROJECT_ARCHIVED = "apis.aom.activity.archived";
    /**
     * The constant ERROR_MSG_PROJECT_END.
     */
    public static final String  ERROR_MSG_PROJECT_END = "apis.aom.activity.end";
    /**
     * The constant ERROR_MSG_AGENCY_NOT_CONSUMEABLE.
     */
    public static final String  ERROR_MSG_AGENCY_NOT_CONSUMEABLE = "apis.aom.agency.not.consumeable";
    /**
     * The constant IS_NOT_IN_SUB.
     */
    public static final String IS_NOT_IN_SUB = "apis.aom.task.user.is.not.in.sub";
    /**
     * The constant ERROR_MSG_ISNT_SUB_STUDENT.
     */
    public static final String ERROR_MSG_ISNT_SUB_STUDENT = "apis.aom.sub.task.student.not.find";

    /**
     * The constant KNG_CHECK_MSG.
     */
    public static final String  KNG_CHECK_MSG= "apis.aom.kng.check_msg";
    /**
     * The constant DOWNLOAD_FUNCTIONNAME_PREFIX.
     */
    public static final String  DOWNLOAD_FUNCTIONNAME_PREFIX= "apis.aom.export.functionName.";

    /**
     * The constant PROJECT_CLOSED.
     */
    public static final String  PROJECT_CLOSED= "apis.aom.learn.project.closed";
    /**
     * The constant PROJECT_START_TIME.
     */
    public static final String  PROJECT_START_TIME= "apis.aom.project.start.time";
    /**
     * The constant PROJECT_START_DATETIME.
     */
    public static final String  PROJECT_START_DATETIME= "apis.aom.project.start.datetime";

    /**
     * The constant DEFAULT_TIME.
     */
    public static final String DEFAULT_TIME = "00:00";


    /**
     * The constant TODAY_TEXT.
     */
    public static final String TODAY_TEXT = "apis.aom.study.task.remark.today_text";
    /**
     * The constant DAY_HOU.
     */
    public static final String DAY_HOU = "apis.aom.study.task.remark.day_hou";
    /**
     * The constant WILL_START.
     */
    public static final String WILL_START = "apis.aom.study.task.remark.will_start";
    /**
     * The constant WILL_EXPIRE.
     */
    public static final String WILL_EXPIRE = "apis.aom.study.task.remark.will_expire";
    /**
     * The constant FACELIVE_STUDENT_MSG_SIGNTIME.
     */
    public static final String FACELIVE_STUDENT_MSG_SIGNTIME = "apis.aom.facelive.student.msg.signtime";

    /**
     * 任务催促
     */
    public static final String TASK_URGE_CONTENT = "apis.aom.task.urge.content";

    /**
     * The constant EVAL_CONFIG_MORE_TEN.
     */
    public static final String EVAL_CONFIG_MORE_TEN = "apis.aom.eval.config.num.max.ten";

    /**
     * The constant PROJECT_PERIOD.
     */
    public static final String PROJECT_PERIOD = "apis.aom.period";
    /**
     * The constant MORE.
     */
    public static final String MORE = "apis.aom.more";
    /**
     * The constant CHOOSE_EVAL_TIP.
     */
    public static final String CHOOSE_EVAL_TIP = "apis.aom.your.choose";
    /**
     * The constant HAVE_CHANGED_PLEASE_CHOOSE_TIP.
     */
    public static final String HAVE_CHANGED_PLEASE_CHOOSE_TIP = "apis.aom.your.choose.have.changed";

    /**
     * The constant CREATOR.
     */
    public static final String CREATOR = "apis.export.header.projectlist.createCnName";
    /**
     * 项目负责人/带教导师已指派您重复学习此任务，请退出后重新学习
     */
    public static final String ERROR_MSG_PROJECT_STUDY_REPEAT_INVALID = "apis.aom.project.study.repeat.invalid";

    /**
     * 我的部门/直属团队
     */
    public static final String TEAM_DEPT_DIRECT = "apis.aom.team.dept.direct";

    /**
     * 异步组件
     */
    public static final String ASYNC_INSERT_GROUPMEMBER = "apis.aom.async.insert.groupmember";
    /**
     * The constant ASYNC_DELETE_GROUPMEMBER.
     */
    public static final String ASYNC_DELETE_GROUPMEMBER = "apis.aom.async.delete.groupmember";
    /**
     * The constant ASYNC_IMPORT_GROUPMEMBER.
     */
    public static final String ASYNC_IMPORT_GROUPMEMBER = "apis.aom.async.import.groupmember";

    /**
     * The constant ASYNC_GROUPMEMBER_CHANGE_FORMAL.
     */
    public static final String ASYNC_GROUPMEMBER_CHANGE_FORMAL = "apis.aom.async.change.formal.groupmember";
    /**
     * 直属
     */
    public static final String TEAM_DIRECT_LEADER = "apis.aom.team.direct";
    /**
     * 部门
     */
    public static final String TEAM_DEPT_LEADER = "apis.aom.team.dept";

    /**
     * 团队
     */
    public static final String TEAM_TEAM = "apis.aom.team.team";

    /**
     * 带教
     */
    public static final String TEAM_OJT = "apis.aom.team.ojt";

    /**
     * The constant SYSTEM_ERROR_TRY_AGAIN.
     */
    public static final String SYSTEM_ERROR_TRY_AGAIN = "apis.aom.async.system.error.try.again";

    /**
     * 资源名称
     */
    public static final String RESOURCE_FIELD_RESOURCENAME = "apis.aom.resource.defaultfield.resourcename";

    /**
     * 请输入资源名称
     */
    public static final String RESOURCE_FIELD_RESOURCENAMEHINT = "apis.aom.resource.defaultfield.resourcenamehint";

    /**
     * 库存
     */
    public static final String RESOURCE_FIELD_STOCK = "apis.aom.resource.defaultfield.stock";

    /**
     * 请输入库存
     */
    public static final String RESOURCE_FIELD_STOCKHINT = "apis.aom.resource.defaultfield.stockhint";

    /**
     * 系统默认类型
     */
    public static final String BUILDIN_RESOURCE_DESCRIPTION = "apis.aom.resource.buildintype.description";

    /**
     * 系统默认分类
     */
    public static final String BUILDIN_RESOURCE_DESCRIPTION2 = "apis.aom.resource.buildintype.description2";

    /**
     * 培训预算
     */
    public static final String DEFAULT_TEMPLATE_NAME = "apis.aom.resource.defaulttemplate.name";

    /**
     * 已延期
     */
    public static final String PLANDELAY = "apis.aom.plan.delay";
    /**
     * 未延期
     */
    public static final String PLANUNDELAY = "apis.aom.plan.not.delay";
    /**
     * 未开始
     */
    public static final String PLANNOTSTART = "apis.aom.plan.not.start";
    /**
     * 进行中
     */
    public static final String PLANINPROGRESS= "apis.aom.plan.is.in.progress";
    /**
     * 已结束
     */
    public static final String PLANENDED = "apis.aom.plan.is.end";
    /**
     * 培训计划导出-审核状态
     */
    public static final String PLAN_AUDIT_WAIT = "apis.export.body.exportplans.auditstatus.wait";
    /**
     * The constant PLAN_AUDIT_ING.
     */
    public static final String PLAN_AUDIT_ING = "apis.export.body.exportplans.auditstatus.ing";
    /**
     * The constant PLAN_AUDIT_PASS.
     */
    public static final String PLAN_AUDIT_PASS = "apis.export.body.exportplans.auditstatus.pass";
    /**
     * The constant PLAN_AUDIT_UNPASS.
     */
    public static final String PLAN_AUDIT_UNPASS = "apis.export.body.exportplans.auditstatus.un_pass";

    /**
     * 正式学员
     */
    public static final String GM_FORMAL = "apis.aom.gm.formal.student";
    /**
     * 旁听学员
     */
    public static final String GM_IN_FORMAL = "apis.aom.gm.informal.student";

    /**
     * The constant PARTFILENAME.
     */
    public static final String PARTFILENAME = "apis.export.header.userfaceinfo.partfilename";

    /**
     * The constant USERFACEINFO.
     */
    public static final String USERFACEINFO = "userfaceinfo";

    /**
     * The constant TITLENAME.
     */
    public static final String TITLENAME = "apis.export.header.userfaceinfo.titlename";

    /**
     * The constant USERFACEINFO_PREFIX.
     */
    public static final String USERFACEINFO_PREFIX = "apis.export.header.userfaceinfo.";

    /**
     * The constant MODULENAME.
     */
    public static final String MODULENAME = "moduleName";

    /**
     * The constant FUNCTIONNAME.
     */
    public static final String FUNCTIONNAME = "functionName";

    /**
     * The constant FILENAME.
     */
    public static final String FILENAME = "fileName";

    /**
     * The constant CHATID.
     */
    public static final String CHATID = "chatId";

    /**
     * The constant LIVE_STU.
     */
    public static final String LIVE_STU_TODO = "apis.aom.global.live.stu.todo";

    /**
     * The constant TAGI18N.
     */
    public static final String TAGI18N = "tagI18n";

    /**
     * The constant EVAL_TEACHER.
     */
    public static final String EVAL_TEACHER = "apis.aom.label.eval.teacher";

    /**
     * The constant EVAL_USER.
     */
    public static final String EVAL_USER = "apis.aom.label.eval.user";

    /**
     * The constant OJT_TEAC.
     */
    public static final String OJT_TEAC_TODO = "apis.aom.global.ojt.teach.todo";

    /**
     * The constant PRINCIPALS_OVER_NUM.
     */
    public static final String PRINCIPALS_OVER_NUM = "apis.aom.project.principals.over.maximum.quantity";


    /**
     * 称号: 优秀学员
     */
    public static final String OUTSTANDING = "apis.aom.title.outStanding";

}
