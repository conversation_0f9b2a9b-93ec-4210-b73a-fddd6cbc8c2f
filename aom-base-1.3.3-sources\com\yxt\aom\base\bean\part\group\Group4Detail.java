package com.yxt.aom.base.bean.part.group;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/12/26 09:58
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Group4Detail {

    @Schema(description = "主键", example = "id")
    @JsonSerialize(using = ToStringSerializer.class)
    private long id;
    @Schema(description = "小组名称", example = "name")
    private String groupName;
    @Schema(description = "小组图标", example = "url")
    private String groupIcon;
    @Schema(description = "辅导员列表", example = "array")
    List<TutorBean> tutors = Lists.newArrayList();
    @Schema(description = "小组组长id", example = "id")
    private String leaderUserId;
    @Schema(description = "小组组长name", example = "name")
    private String leaderName;
    @Schema(description = "小组荣誉", example = "array")
    private List<Honor4Get> honors = Lists.newArrayList();
    @Schema(description = "小组排名", example = "100")
    private Integer groupRank;
    @Schema(description = "小组积分", example = "100")
    private Integer groupPoint;

    @Schema(description = "小组人数", example = "30")
    private long number;

    @Schema(description = "是否开启群聊,0:未开启;1已开启")
    private Integer openIm;
    @Schema(description = "群主用户id")
    private String ownerUserId;
    @Schema(description = "群主全名")
    private String ownerFullName;
    @Schema(description = "群主头像")
    private String ownerAvatarUrl;
    @Schema(description = "群名称")
    private String imName;
    @Schema(description = "群聊id")
    private String imId;
    /**
     * 项目群聊类型
     */
    @Schema(description = "项目群聊类型 '0-钉钉,1-飞书,2-企微,3-系统,4-不开启群聊'")
    private Integer imType;
    @Schema(description = "是否开启项目群聊 0:不开启 1:开启")
    private Integer imEnable;
}
