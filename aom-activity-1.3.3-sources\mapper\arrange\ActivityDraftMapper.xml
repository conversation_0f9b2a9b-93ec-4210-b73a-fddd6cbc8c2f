<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.aom.activity.mapper.arrange.ActivityDraftMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yxt.aom.activity.entity.arrange.ActivityDraft">
        <id column="id" property="id" />
        <result column="org_id" property="orgId" />
        <result column="actv_id" property="actvId" />
        <result column="ref_reg_id" property="refRegId" />
        <result column="ref_id" property="refId" />
        <result column="form_data" property="formData" />
        <result column="item_id" property="itemId" />
        <result column="draft_status" property="draftStatus" />
        <result column="create_user_id" property="createUserId" />
        <result column="update_user_id" property="updateUserId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, org_id, actv_id, ref_reg_id, ref_id, form_data, item_id, draft_status, create_user_id, update_user_id, create_time, update_time, deleted
    </sql>

    <insert id="batchInsert" parameterType="com.yxt.aom.activity.entity.arrange.ActivityDraft">
        insert into aom_activity_draft
        (<include refid="Base_Column_List"/>)
        values
        <foreach collection="list" separator="," item="item">
            (#{item.id}, #{item.orgId}, #{item.actvId}, #{item.refRegId},
            #{item.refId},#{item.formData},#{item.itemId},#{item.draftStatus},#{item.createUserId},#{item.updateUserId},
            #{item.createTime}, #{item.updateTime}, #{item.deleted})
        </foreach>
    </insert>

    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update aom_activity_draft
            <set>
                org_id = #{item.orgId}, actv_id = #{item.actvId}, ref_reg_id = #{item.refRegId},
                ref_id = #{item.refId}, form_data = #{item.formData}, item_id = #{item.itemId},
                draft_status = #{item.draftStatus}, create_user_id = #{item.createUserId},
                update_user_id = #{item.updateUserId}, create_time = #{item.createTime},
                update_time = #{item.updateTime}, deleted = #{item.deleted}
            </set>
            where id = #{item.id}
        </foreach>
    </update>

    <select id="listDraft4Remove" resultType="com.yxt.aom.activity.entity.arrange.ActivityDraft">
        select id, ref_id, item_id
        from aom_activity_draft
        where org_id = #{orgId}
        and actv_id = #{actvId}
        and ref_reg_id = #{refRegId}
        and deleted = 0
        <if test="excludeItemIds !=null and excludeItemIds.size > 0">
            and item_id not in
            <foreach collection="excludeItemIds" item="itemId" open="(" close=")" separator=",">
                #{itemId}
            </foreach>
        </if>
    </select>

    <update id="deleteByIds">
        update aom_activity_draft set deleted = 1, update_user_id = #{operatorId}, update_time = current_timestamp(3)
        where org_id = #{orgId}
        and id in
        <foreach collection="ids" item="itemId" open="(" close=")" separator=",">
            #{itemId}
        </foreach>
    </update>

    <update id="updateFormData" >
        update aom_activity_draft
           set form_data = #{formData}
        where org_id = #{orgId}
          and actv_id = #{actvId}
          and ref_id = #{refId}
          and deleted = 0
    </update>

    <select id="listDraft4ActvId" resultType="com.yxt.aom.activity.entity.arrange.ActivityDraft">
        select
        <include refid="Base_Column_List"/>
        from aom_activity_draft
        where org_id = #{orgId}
        and actv_id = #{actvId}
        and ref_reg_id = #{refRegId}
        and deleted = 0
    </select>
</mapper>
