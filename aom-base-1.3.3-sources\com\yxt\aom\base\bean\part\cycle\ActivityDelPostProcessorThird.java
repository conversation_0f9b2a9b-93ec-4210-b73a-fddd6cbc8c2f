package com.yxt.aom.base.bean.part.cycle;

import com.yxt.aom.base.bean.part.ActivityStatusChangeThird;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
public class ActivityDelPostProcessorThird extends ActivityStatusChangeThird {

    /**
     * 活动删除支持批量，活动任务id列表，当actvIdList不为空时，代表执行批量删除操作
     */
    private List<String> actvIdList;

}
