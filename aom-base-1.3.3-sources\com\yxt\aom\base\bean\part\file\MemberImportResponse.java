package com.yxt.aom.base.bean.part.file;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Builder
@Data
public class MemberImportResponse {

    @Schema(description = "错误数据文件url")
    private String errorFileUrl;

    @Schema(description = "成功条数", example = "1210805511352545282")
    private Integer successCount;

    @Schema(description = "失败条数", example = "1210805511352545282")
    private Integer failCount;

    @Schema(description = "错误类型 1-模版出错 2-枚举校验出错")
    private Integer errorKey;

    @Schema(description = "失败数据的uuid，用于下载失败数据传参使用", example = "1210805511352545282")
    private String errorFileId;

}
