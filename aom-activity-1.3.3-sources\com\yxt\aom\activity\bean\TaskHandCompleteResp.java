package com.yxt.aom.activity.bean;

import com.yxt.common.annotation.timezone.DateFormatField;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/11/14 20:21
 */
@Data
public class TaskHandCompleteResp {

    @Schema(description = "业务方id")
    @NotBlank
    private String activityId;

    @Schema(description = "活动编排itemId")
    @NotBlank
    private Long itemId;

    @Schema(description = "type=0时：选择的用户ids")
    private Set<String> userIds;

    @Schema(description = "参与id")
    private Long participationId;

    @Schema(description = "是否全选", example = "true")
    private boolean checkAll;

    @Schema(description = "排除学员id")
    private Set<String> excludeUserIds;

    @Schema(description = "1正式学员 0旁听学员", example = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer formal;

    @Schema(description = "部门id")
    private List<String> deptIds;

    @Schema(description = "岗位id")
    private List<String> positionIds;

    @Schema(description = "直属经理id")
    private List<String> managerIds;

    @Schema(description = "账号状态账号状态 0-禁用 1-启用 2-删除 不传取所有", example = "1")
    private Integer status;

    @Schema(description = "关键字")
    private String keyword;

    @DateFormatField()
    private String startTime;

    @DateFormatField()
    private String endTime;

}
