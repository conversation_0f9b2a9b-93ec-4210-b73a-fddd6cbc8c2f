package com.yxt.aom.activity.handle;

import com.google.common.collect.Maps;
import com.yxt.aom.base.bean.part.ActivityStatusChange4ProMsg;
import com.yxt.aom.base.enums.AomActivityStatusEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.function.Consumer;

@Component
@RequiredArgsConstructor
public class AomStatusChange4ActvFunctionMap implements InitializingBean {

    private final AomActivityStatusChange4ActvHandle handle;

    public final Map<Integer, Consumer<ActivityStatusChange4ProMsg>> map = Maps.newHashMap();


    @Override
    public void afterPropertiesSet() {
        map.put(AomActivityStatusEnum.ING.getType(), handle::release);
        map.put(AomActivityStatusEnum.WITHDRAW.getType(), handle::withdraw);
        map.put(AomActivityStatusEnum.END.getType(), handle::end);
        map.put(AomActivityStatusEnum.PIGEONHOLE.getType(), handle::archive);
        map.put(AomActivityStatusEnum.DELETE.getType(), handle::del);
    }
}
