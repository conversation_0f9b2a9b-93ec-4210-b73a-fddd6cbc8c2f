package com.yxt.aom.base.bean.arrange;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * AssessmentResult4Arrange
 */
@Data
@Schema(name = "学员测评类活动结果")
public class AssessmentResult4Arrange {
    /**
     * 记录id
     */
    @JsonIgnore
    private Long id;

    /**
     * 机构id
     */
    @JsonIgnore
    private String orgId;

    /**
     * 活动/项目id
     */
    @JsonIgnore
    private String actvId;

    /**
     * 学员id
     */
    @JsonIgnore
    private String userId;

    /**
     * 基础记录id
     */
    @JsonIgnore
    private Long baseActvResultId;

    /**
     * 任务重复类型 0：默认 1：多班次任务 2：指派重复学习
     */
    @Schema(description = "任务重复类型 0：默认 1：多班次任务 2：指派重复学习", example = "0")
    private Integer resultRepeatFlag;

    /**
     * 指派重新学习次数
     */
    @Schema(description = "指派重新学习次数", example = "0")
    private Integer repeatCount;

    /**
     * 生效的子任务结果Id
     */
    @Schema(description = "生效的子任务结果Id", example = "1849276367004880001")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long subTaskResultId;

    /**
     * 活动是否通过 0否 1是
     */
    @Schema(description = "活动是否通过 0否 1是", example = "0")
    private Integer passed;

    /**
     * 活动批阅状态 0：未开始；1：考试中；2：已提交；3：批阅中；4：已完成
     */
    @Schema(description = "活动批阅状态 0：未开始；1：考试中；2：已提交；3：批阅中；4：已完成", example = "0")
    private Integer targetStatus;

    /**
     * 活动得分
     */
    @Schema(description = "活动得分", example = "0")
    private BigDecimal score;

    /**
     * 活动总分
     */
    @Schema(description = "活动总分", example = "100")
    private BigDecimal totalScore;

    /**
     * 是否删除(0-未删除, 1-已删除)
     */
    @JsonIgnore
    private Integer deleted;

    /**
     * 创建人id
     */
    @JsonIgnore
    private String createUserId;

    /**
     * 更新人id
     */
    @JsonIgnore
    private String updateUserId;

    /**
     * 创建时间
     */
    @JsonIgnore
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonIgnore
    private Date updateTime;
}
