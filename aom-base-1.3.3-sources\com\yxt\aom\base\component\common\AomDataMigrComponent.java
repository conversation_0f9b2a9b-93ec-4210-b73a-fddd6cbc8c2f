package com.yxt.aom.base.component.common;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Sets;
import com.yxt.aom.base.bean.config.AllConfig;
import com.yxt.aom.base.bean.config.AuditConfig;
import com.yxt.aom.base.cache.AomCacheKey;
import com.yxt.aom.base.cache.AomRedisCache;
import com.yxt.aom.base.common.AomPropConstants;
import com.yxt.aom.base.entity.common.Activity;
import com.yxt.aom.base.enums.ActivityTypeEnum;
import com.yxt.aom.base.enums.AomActivityStatusEnum;
import com.yxt.aom.base.enums.AomCacheKeyEnum;
import com.yxt.aom.base.enums.AomTimeModelEnum;
import com.yxt.aom.base.manager.arrange.ArrangeManager;
import com.yxt.aom.base.manager.common.ActivityManager;
import com.yxt.aom.base.manager.common.AomRegistryManager;
import com.yxt.aom.base.manager.control.ControlManager;
import com.yxt.aom.base.manager.part.PartManager;
import com.yxt.aom.base.service.common.ActivityService;
import com.yxt.aom.base.wrapper.UacdWrapper;
import com.yxt.aom.common.config.AomDataSourceTypeHolder;
import com.yxt.aom.migr.common.MigrStepEnum;
import com.yxt.aom.migr.common.MqConstants;
import com.yxt.aom.migr.mq.MigrActivityArrangeMq;
import com.yxt.aom.migr.mq.MigrActivityFailedMq;
import com.yxt.aom.migr.mq.common.Activity4Migr;
import com.yxt.common.Constants;
import com.yxt.common.enums.YesOrNo;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.util.BatchOperationUtil;
import com.yxt.common.util.DateUtil;
import com.yxt.dbrouter.core.toolkit.DBRouteHolder;
import com.yxt.uacd.facade.bean.RegistryConfigBean;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.Set;

/**
 * AOM数据迁移服务
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AomDataMigrComponent {
    private final ActivityManager activityManager;
    private final ArrangeManager arrangeManager;
    private final PartManager partManager;
    private final ControlManager controlManager;
    private final AomRegistryManager registryManager;
    private final AomRedisCache redisCache;
    private final AomMqComponent mqComponent;
    private final ActivityService activityService;
    private final UacdWrapper uacdWrapper;

    @Value("${spring.application.name:api}")
    private String serviceName;

    /**
     * Send migr failed mq.
     *
     * @param orgId     the org id
     * @param actvId    the actv id
     * @param actvRegId the actv reg id
     * @param step      the step
     * @param errorMsg  the error msg
     */
    public void sendMigrFailedMq(String orgId, String actvId, String actvRegId, MigrStepEnum step, String errorMsg) {
        MigrActivityFailedMq mq = new MigrActivityFailedMq();
        mq.setOrgId(orgId);
        mq.setActvId(actvId);
        mq.setActvRegId(actvRegId);
        mq.setStep(step);
        mq.setErrorMsg(errorMsg);
        mq.setServiceName(serviceName);
        try {
            mqComponent.sendRocketMessage(MqConstants.TOPIC_AOM_MIGR_ACTIVITY_FAILED, JSON.toJSONString(mq));
        } catch (Exception e) {
            log.error("sendMigrFailedMq error", e);
        }
    }

    /**
     * Migr activity arrange.
     *
     * @param mq the mq
     */
    public void migrActivityArrange(MigrActivityArrangeMq mq) {
        String orgId = mq.getOrgId();
        Activity4Migr activity = mq.getActivity();
        if (StringUtils.isBlank(orgId) || activity == null) {
            throw new IllegalArgumentException("migrActivityArrange invalid mq : " + mq);
        }

        String actvRegId = activity.getActvRegId();
        RegistryConfigBean registryConfig = registryManager.getRegistryConfig(actvRegId);
        if (registryConfig == null) {
            throw new IllegalArgumentException("migrActivityArrange invalid actvRegId : " + actvRegId);
        }

        String actvId = activity.getId();
        AomDataSourceTypeHolder.set(actvRegId);
        DBRouteHolder.push(orgId);
        deleteMigrCache(orgId, actvId);
        delete4Migr(orgId, Sets.newHashSet(actvId));
        migrActivity(orgId, actvRegId, activity);
        arrangeManager.migrArrange(mq);
    }

    /**
     * 数据迁移前先清理老数据
     *
     * @param req the req
     */
    public void delete4Migr(String orgId, Set<String> actvIds) {
        if (CollectionUtils.isEmpty(actvIds)) {
            delete4MigrLimit1000(orgId, actvIds);
        } else {
            BatchOperationUtil.batchExecute(new ArrayList<>(actvIds), Constants.INT_1000,
                    subList -> delete4MigrLimit1000(orgId, new HashSet<>(subList)));
        }
    }

    private void delete4MigrLimit1000(String orgId, Set<String> actvIds) {
        activityManager.delete4Migr(orgId, actvIds);
        arrangeManager.delete4Migr(orgId, actvIds);
        partManager.delete4Migr(orgId, actvIds);
        controlManager.delete4MigrProj(orgId, actvIds);
    }

    private void deleteMigrCache(String orgId, String actvId) {
        deleteCache(orgId, actvId, AomCacheKeyEnum.MIGR_ACTIVITY_ARRANGE);
        deleteCache(orgId, actvId, AomCacheKeyEnum.MIGR_MEMBER_FINISHED_COUNT);
        deleteCache(orgId, actvId, AomCacheKeyEnum.MIGR_RESULT_FINISHED_ITEMIDSET);
    }

    private void deleteCache(String orgId, String actvId, AomCacheKeyEnum keyEnum) {
        AomCacheKey cacheKey = keyEnum.typedKey(
                mapBuilder -> mapBuilder.of(AomPropConstants.ORG_ID, orgId, AomPropConstants.ACTV_ID, actvId));
        redisCache.delete(cacheKey.ofString());
    }

    private void migrActivity(String orgId, String actvRegId, Activity4Migr activity4Migr) {
        Activity entity = toActivity(orgId, actvRegId, activity4Migr);

        UserCacheDetail user = new UserCacheDetail();
        user.setOrgId(entity.getOrgId());
        user.setUserId(entity.getCreateUserId());

        AuditConfig auditConfig = new AuditConfig();
        auditConfig.setAutoRelease(activity4Migr.getAutoRelease());
        AllConfig allConfig = new AllConfig();
        allConfig.setAuditConfig(auditConfig);

        activityService.create(user, activity4Migr.getMgrUserIds(), entity, allConfig, null);

        activity4Migr.setDesignerId(entity.getDesignerId());
    }

    private Activity toActivity(String orgId, String actvRegId, Activity4Migr activity4Migr) {
        Activity entity = new Activity();
        entity.setId(ObjectUtils.defaultIfNull(activity4Migr.getId(), activityService.generateActivityId(actvRegId)));
        entity.setOrgId(orgId);
        entity.setActvRegId(actvRegId);
        entity.setActvName(ObjectUtils.defaultIfNull(activity4Migr.getActvName(), StringUtils.EMPTY));
        entity.setActvType(ObjectUtils.defaultIfNull(activity4Migr.getActvType(), ActivityTypeEnum.PROJ.getType()));
        entity.setActvStatus(
                ObjectUtils.defaultIfNull(activity4Migr.getActvStatus(), AomActivityStatusEnum.DRAFT.getType()));
        entity.setTimeModel(ObjectUtils.defaultIfNull(activity4Migr.getTimeModel(), AomTimeModelEnum.FIX.getType()));
        entity.setStartTime(activity4Migr.getStartTime());
        entity.setEndTime(activity4Migr.getEndTime());
        entity.setStartDayOffset(activity4Migr.getStartDayOffset());
        entity.setEndDayOffset(activity4Migr.getEndDayOffset());
        entity.setImageUrl(activity4Migr.getImageUrl());
        entity.setDescription(activity4Migr.getDescription());
        entity.setDesignerId(uacdWrapper.getDesignerIdByRegistryId(actvRegId));
        entity.setSourceId(StringUtils.EMPTY);
        entity.setSourceName(StringUtils.EMPTY);
        entity.setSourceRegId(StringUtils.EMPTY);
        entity.setModelId(ObjectUtils.defaultIfNull(activity4Migr.getModelId(), StringUtils.EMPTY));
        entity.setSceneId(ObjectUtils.defaultIfNull(activity4Migr.getSceneId(), StringUtils.EMPTY));
        entity.setCategoryId(ObjectUtils.defaultIfNull(activity4Migr.getCategoryId(), StringUtils.EMPTY));
        entity.setAutoEnd(ObjectUtils.defaultIfNull(activity4Migr.getAutoEnd(), 0));
        entity.setAutoArchive(YesOrNo.NO.getValue());
        entity.setAuditEnabled(ObjectUtils.defaultIfNull(activity4Migr.getAuditEnabled(), 0));
        entity.setAuditStatus(activity4Migr.getAuditEnabled() == 1 ? 0 : 2);
        entity.setCreateUserId(ObjectUtils.defaultIfNull(activity4Migr.getCreateUserId(), StringUtils.EMPTY));
        entity.setUpdateUserId(ObjectUtils.defaultIfNull(activity4Migr.getUpdateUserId(), StringUtils.EMPTY));
        entity.setCreateTime(ObjectUtils.defaultIfNull(activity4Migr.getCreateTime(), DateUtil.currentTime()));
        entity.setUpdateTime(ObjectUtils.defaultIfNull(activity4Migr.getUpdateTime(), DateUtil.currentTime()));
        entity.setDeleted(YesOrNo.NO.getValue());
        return entity;
    }
}
