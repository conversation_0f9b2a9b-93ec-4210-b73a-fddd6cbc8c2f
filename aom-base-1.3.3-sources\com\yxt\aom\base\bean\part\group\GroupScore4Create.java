package com.yxt.aom.base.bean.part.group;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;


/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class GroupScore4Create {

    @NotNull
    @Schema(description = "UACD注册表中定义Id")
    private String regId;

    @NotNull
    @Schema(description = "活动id", example = "1206750826790273025", requiredMode = Schema.RequiredMode.REQUIRED)
    private String actvId;

    @NotNull
    @Schema(description = "小组id", example = "1206750826790273025", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long groupId;

    @NotNull
    @Schema(description = "积分", example = "1206750826790273025", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer score;

    @Schema(description = "增加原因", example = "")
    private String reason;

    @Schema(description = "1不分配 2分配给学员", example = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer allotType;

    @Schema(description = "是否分享 默认0不分享 1分享", example = "0", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer sharedIm = 0;

    @Schema(description = "学员分配积分明细集合", example = "0")
    private List<UserIdAndScore> userIdAndScores;

    @Data
    public static class UserIdAndScore {
        @Schema(description = "学员ID", example = "0")
        @NotBlank
        private String userId;
        @Schema(description = "学员分配积分", example = "0")
        @NotNull
        private BigDecimal score = BigDecimal.ZERO;
    }

}
