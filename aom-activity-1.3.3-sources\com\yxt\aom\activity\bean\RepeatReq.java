package com.yxt.aom.activity.bean;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.yxt.aom.base.enums.AomActivitySubTypeEnum;
import com.yxt.common.Constants;
import com.yxt.common.annotation.timezone.DateFormatField;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

import static com.yxt.common.Constants.INT_0;
import static com.yxt.common.Constants.INT_NEGATIVE_1;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/9/5 13:51
 * @Param
 * @return
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(name = "重复指派学习")
public class RepeatReq extends CheckBoxBaseBean {

    @Schema(description = "机构Id")
    private String orgId;

    @Schema(description = "活动id")
    private String actvId;

    @Schema(description = "是否发送消息 默认0 不发 1发")
    private Integer sendMsgFlag = 0;

    @Schema(description = "消息发送类型 0:默认通知模板，1:个性化通知模板")
    private Integer messageType;

    @Schema(description = "自定义消息targetId, messageType=1必传", example = "xx")
    private String messageTargetId;

    @Schema(description = "指派重学活动类型")
    private String regType;

    @Schema(description = "当前活动所属来源，比如培训项目id")
    private String sourceId;

    @Schema(description = "当前活动所属来源类型，比如培训项目类型")
    private String sourceType;

    @JsonIgnore
    private AomActivitySubTypeEnum aomActivitySubTypeEnum;



}
